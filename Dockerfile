FROM python:3.11-slim

WORKDIR /app

RUN apt-get -y update && apt-get -y upgrade

COPY requirements.txt .

RUN pip install -r requirements.txt
COPY . .

RUN groupadd -g 1000 app_group

RUN useradd -g app_group --uid 1000 app_user

RUN chown -R app_user:app_group /app

USER app_user

#CMD ["gunicorn", "app.main:app", "-c", "gunicorn.conf.py"]
CMD ["gunicorn", "app.main:app", "-c", "gunicorn.conf.py", "-b", "0.0.0.0:8080"]
