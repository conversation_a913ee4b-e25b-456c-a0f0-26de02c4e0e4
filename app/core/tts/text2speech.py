import re
import asyncio
from typing import Any, Generator, List
import edge_tts
from janome.tokenizer import Tokenizer


class JapaneseTextPreprocessor:
    __slots__ = ['chunk_size', 'tokenizer']

    def __init__(self, chunk_size: int = 150):
        self.chunk_size = chunk_size
        self.tokenizer = Tokenizer()

    def segment_words(self, text: str) -> List[str]:
        words = [token.surface for token in self.tokenizer.tokenize(text)]
        return words

    def chunk_text(self, words: List[str]) -> List[str]:
        chunks = []
        current_chunk = ""

        for word in words:
            if len(current_chunk) + len(word) <= self.chunk_size:
                current_chunk += word
            else:
                chunks.append(current_chunk)
                current_chunk = word

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def preprocess(self, text: str) -> List[str]:
        words = self.segment_words(text)
        chunks = self.chunk_text(words)
        return chunks


class TTSProcessor:
    __slots__ = ['voice', 'language', 'rate', 'volume', 'pitch', 'stream', 'preprocessor', 'delay_between_requests']

    def __init__(self, language: str = 'ja', voice: str = 'ja-JP-KeitaNeural', rate: str = '-20%',
                 volume: str = '+10%', pitch: str = '-10Hz', stream: bool = False, delay_between_requests: float = 0.5):
        self.language = language
        self.voice = voice
        self.rate = rate
        self.volume = volume
        self.pitch = pitch
        self.stream = stream
        self.preprocessor = JapaneseTextPreprocessor()
        self.delay_between_requests = delay_between_requests

    async def _generate_audio_chunk(self, text_chunk: str) -> bytes:
        communicate = edge_tts.Communicate(text_chunk, voice=self.voice, rate=self.rate, volume=self.volume,
                                           pitch=self.pitch)
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        return audio_data

    async def audio_generate(self, text: str) -> bytes:
        text_chunks = self.preprocessor.preprocess(text)
        all_audio_data = b""

        for chunk in text_chunks:
            audio_data = await self._generate_audio_chunk(chunk)
            all_audio_data += audio_data

            await asyncio.sleep(self.delay_between_requests)

        return all_audio_data


class TTSProcessorFactory:
    @staticmethod
    def create_tts_processor(language: str = 'ja', voice: str = 'ja-JP-KeitaNeural',
                             rate: str = '-20%', volume: str = '+10%', pitch: str = '-10Hz',
                             delay_between_requests: float = 0.5) -> TTSProcessor:
        return TTSProcessor(language, voice, rate, volume, pitch, delay_between_requests=delay_between_requests)
