from datetime import datetime
import hashlib
import numpy as np
from multiprocessing.pool import ThreadPool
from langchain_chroma import Chroma
from chromadb import PersistentClient
from chromadb.config import Settings
from langchain_google_vertexai import VertexAIEmbeddings
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from app.helpers.config import settings
# from sklearn.decomposition import PCA


class ChromaRetriever:
    def __init__(self, persist_directory='./vectordb', collection_name=settings.EMBED_STORE, embeddings=None):
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.target_dim = settings.MAX_EMBEDDING_DIM
        self.embeddings = embeddings if embeddings else VertexAIEmbeddings(model_name="text-embedding-004")
        self.vectorstore = Chroma(
            collection_name=self.collection_name,
            embedding_function=self.embeddings,
            client_settings= Settings(
                chroma_server_host=settings.CHROMA_SERVER_HOST,
                chroma_server_http_port=settings.CHROMA_SERVER_HTTP_PORT
            )
        )

    @staticmethod
    def hash_text(text):
        return hashlib.sha256(text.encode("utf-8")).hexdigest()

    # def _normalize_embedding(self, embedding):
    #     embedding = np.array(embedding)
    #
    #     if len(embedding) > self.target_dim:
    #         try:
    #             pca = PCA(n_components=self.target_dim)
    #             return pca.fit_transform(embedding.reshape(1, -1))[0]
    #         except:
    #             return embedding[:self.target_dim]
    #
    #     elif len(embedding) < self.target_dim:
    #         return np.pad(embedding, (0, self.target_dim - len(embedding)), mode='constant')
    #
    #     return embedding

    def pad_or_truncate(self, embedding):
        if len(embedding) > self.target_dim:
            return embedding[:self.target_dim]
        return np.pad(embedding, (0, self.target_dim - len(embedding)), mode='constant')

    def vectorized_text_document(self, page, user_id, source, chatroom_code):
        metadata = page.metadata
        document_content = page.page_content
        hashed_text = self.hash_text(document_content)

        where_condition = {"$and": [{"hashed_text": hashed_text}]}
        if chatroom_code is not None:
            where_condition["$and"].append({"chatroom_code": chatroom_code})
        if source is not None:
            where_condition["$and"].append({"source": source})

        existing_metadata = self.vectorstore._collection.get(
            where=where_condition,
            include=["metadatas"]
        )["metadatas"]

        if existing_metadata:
            # print(f"Đã tồn tại trang {metadata.get('page')} từ tệp {source} trong vectorstore với chatroom_code {chatroom_code} .")
            return

        raw_embedding = self.embeddings.embed_query(document_content)
        normalized_embedding = self.pad_or_truncate(raw_embedding)

        metadata["user_id"] = user_id or ""
        metadata["chatroom_code"] = chatroom_code or ""
        metadata["hashed_text"] = hashed_text
        metadata["timestamp"] = datetime.now().timestamp()

        self.vectorstore.add_texts([document_content], metadatas=[metadata], embeddings=[normalized_embedding.tolist()])
        # print(f"Thêm trang {metadata.get('page')} từ tệp {source} vào vectorstore với chatroom_code {chatroom_code} .")

    def add_documents(self, documents, user_id=None, source=None, chatroom_code=None):
        pool_size = min(5, len(documents))
        with ThreadPool(pool_size) as pool:
            args_list = [(page, user_id, source, chatroom_code) for page in documents]
            pool.starmap(self.vectorized_text_document, args_list)

    def embedded_documents(self, documents, user_id=None, source=None, chatroom_code=None):
        for page in documents:
            metadata = page.metadata
            document_content = page.page_content
            hashed_text = self.hash_text(document_content)

            where_condition = {
                "$and": []
            }
            where_condition["$and"].append({"hashed_text": hashed_text})
            if chatroom_code is not None:
                where_condition["$and"].append({"chatroom_code": chatroom_code})
            if source is not None:
                where_condition["$and"].append({"source": source})

            existing_metadata = self.vectorstore._collection.get(
                where=where_condition,
                include=["metadatas"]
            )["metadatas"]

            if existing_metadata:
                print(
                    f"Đã tồn tại trang {metadata.get('page')} từ tệp {source} trong vectorstore với chatroom_code {chatroom_code} .")
                continue

            metadata["user_id"] = user_id or ""
            metadata["chatroom_code"] = chatroom_code or ""
            metadata["hashed_text"] = hashed_text
            metadata["timestamp"] = datetime.now().timestamp()

            self.vectorstore.add_texts([document_content], metadatas=[metadata])
            print(
                f"Thêm trang {metadata.get('page')} từ tệp {source} vào vectorstore với chatroom_code {chatroom_code} .")

    def query(self, query_text: str, chatroom_code: str | None =None, sources: list[str] | None = None, top_k: int = 5):
        sources = sources or []

        if chatroom_code is None:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail="chatroom_code là bắt buộc để truy vấn tài liệu.")

        metadata_filter = {"chatroom_code": chatroom_code}
        if sources:
            metadata_filter = {"$and": [{"chatroom_code": chatroom_code}, {"source": {"$in": sources}}]}

        total_docs = self.vectorstore._collection.count()
        if total_docs == 0:
            return []

        top_k = min(top_k, total_docs)

        retriever = self.vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={
                "k": top_k,
                "filter": metadata_filter
            }
        )

        results = retriever.invoke(query_text)
        return results

    def get_all_documents(self):
        results = self.vectorstore._collection.get(include=["documents", "metadatas"])
        return results

    def get_document_by_id(self, doc_id):
        results = self.vectorstore._collection.query(where={"id": doc_id}, include=["documents", "metadatas"])
        return results

    def get_list_of_collections(self):
        collections = self.vectorstore._client.list_collections()
        return [collection.name for collection in collections]
