import hashlib
from langchain_chroma import Chroma


class ChromaVectorStore:
    def __init__(self, persist_directory='./chroma_db', collection_name="default_collection", embeddings=None):
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.embeddings = embeddings
        self.vectorstore = Chroma(persist_directory=self.persist_directory, collection_name=self.collection_name,
                                  embedding_function=self.embeddings)

    def add_pdf_pages_with_hash(self, documents: list | None = None):
        existing_metadata = self.vectorstore._collection.get(include=["metadatas"])["metadatas"]
        existing_hashes = {meta.get("hashed_text") for meta in existing_metadata if "hashed_text" in meta}

        if documents:
            for page in documents:
                metadata = page.metadata
                document_content = page.page_content
                hashed_text = self.hash_text(document_content)
                metadata["hashed_text"] = hashed_text
                metadata["timestamp"] = datetime.now().timestamp()

                if hashed_text in existing_hashes:
                    print(f"Page {metadata.get('page')} from {metadata.get('source')} already exists, skipping.")
                else:
                    self.vectorstore.add_texts([document_content], metadatas=[metadata])
                    existing_hashes.add(hashed_text)
                    print(f"Added page {metadata.get('page')} from {metadata.get('source')} to vectorstore.")

    @staticmethod
    def hash_text(text):
        return hashlib.sha256(text.encode("utf-8")).hexdigest()

    def get_list_ids(self):
        results = self.vectorstore._collection.get(include=["metadatas"])
        return results["metadatas"]

    def get_documents_by_user_id(self, user_id):
        results = self.vectorstore._collection.query(where={"user_id": user_id}, include=["metadatas", "documents"])
        return results

    def list_collections(self):
        collections = self.vectorstore._client.list_collections()
        return [collection.name for collection in collections]

    def get_documents_by_collection(self, collection_name):
        client = Chroma(persist_directory=self.persist_directory, collection_name=collection_name,
                        embedding_function=self.embeddings)
        results = client._collection.get(include=["documents", "metadatas"])
        return results
