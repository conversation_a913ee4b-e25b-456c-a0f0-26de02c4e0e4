import traceback
from tempfile import NamedTemporaryFile
from typing import Dict, List, Optional, Union
from fastapi import status as http_status
from langchain.text_splitter import CharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader
from app.core.cloud_engine.google import GoogleCloudPlatform
from app.helpers.config import settings
from app.helpers.exceptions import GatewayException
from app.utils.common import get_message


class Document:
    def __init__(self, content: str, metadata: Dict[str, str]):
        self.content = content
        self.metadata = metadata

    def __repr__(self):
        return f"Document(content={self.content[:30]}..., metadata={self.metadata})"


class DocumentLoader:
    def __init__(self, chunk_size=1500, chunk_overlap=50):
        self.text_splitter = CharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        self.cloud_storage = GoogleCloudPlatform()
        self.media_types = [
            "image/jpg", "image/jpeg", "image/png", "image/gif", "image/bmp", "image/tiff", "image/webp",
            "audio/mpeg", "audio/wav", "audio/ogg", "audio/aac", "audio/flac",
            "video/mp4", "video/mpeg", "video/webm", "video/quicktime", "video/x-msvideo"
        ]

    async def load_document(self, file, language: str = "jp", s3_storage: bool = True,
                            bucket_name=settings.BUCKET_NAME, upload_folder='upload_file_chat') -> Union[
        None, tuple, List]:
        if file is None:
            return None, []

        file_name = file.filename
        file_type = file.content_type
        file_size = len(await file.read())
        await file.seek(0)

        if file_size > 2048 * 1024 * 1024:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("file_size_exceeds", language)
            )

        document = []
        if file_type == "application/pdf":
            document = await self._process_pdf(file)
        elif file_type in ["application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                           "application/vnd.ms-word"]:
            document = await self._process_docx(file)
        elif file_type == "application/msword":
            document = await self._process_doc(file)

        source = file_name
        if s3_storage:
            s3_url = await self._upload_to_s3(file, bucket_name, upload_folder)
            source = s3_url

        for doc in document:
            doc.metadata["source"] = source

        return source, document

    async def _process_pdf(self, file) -> List:
        with NamedTemporaryFile(delete=True, suffix=".pdf") as temp_file:
            temp_file.write(await file.read())
            temp_file_path = temp_file.name

            loader = PyPDFLoader(temp_file_path)
            documents = loader.load_and_split(text_splitter=self.text_splitter)

        return documents

    async def _process_docx(self, file) -> List:
        with NamedTemporaryFile(delete=True, suffix=".docx") as temp_file:
            temp_file.write(await file.read())
            temp_file_path = temp_file.name

            loader = Docx2txtLoader(temp_file_path)
            documents = loader.load_and_split(text_splitter=self.text_splitter)

        return documents

    async def _process_doc(self, file) -> List:
        with NamedTemporaryFile(delete=True, suffix=".doc") as temp_file:
            temp_file.write(await file.read())
            temp_file_path = temp_file.name

            try:
                loader = Docx2txtLoader(temp_file_path)
                documents = loader.load_and_split(text_splitter=self.text_splitter)
            except Exception:
                try:
                    from langchain.document_loaders import UnstructuredWordDocumentLoader
                    loader = UnstructuredWordDocumentLoader(temp_file_path)
                    documents = loader.load_and_split(text_splitter=self.text_splitter)
                except (ImportError, Exception):
                    documents = []

        return documents

    async def _upload_to_s3(self, file, bucket_name, upload_folder) -> Optional[str]:
        try:
            return await self.cloud_storage.upload_to_gcs(file, bucket_name, upload_folder)
        except:
            print(f"[ERROR UPLOAD S3 FOR CHAT]: {traceback.format_exc()}")
            raise GatewayException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail='Không thể upload file lên S3'
            )
