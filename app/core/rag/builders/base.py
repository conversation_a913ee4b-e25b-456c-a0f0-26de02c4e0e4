from abc import ABC, abstractmethod
from typing import Any, AsyncIterator
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.runnables import Runnable
from langchain_core.runnables.schema import StreamEvent
from langchain_core.runnables.history import RunnableWithMessageHistory
from app.schemas.chat import LLMInvokeResponse


class ChainChatBase(ABC):
    runnable: RunnableWithMessageHistory

    def __init__(self, llm: BaseChatModel, **kwargs):
        self.runnable = self._get_chain(llm=llm, **kwargs)

    @abstractmethod
    def _get_chain(
        self, llm: BaseChatModel, **kwargs: dict[str, Any]
    ) -> RunnableWithMessageHistory: ...

    def invoke(self, *args: Any, **kwargs: dict[str, Any]) -> LLMInvokeResponse:
        return LLMInvokeResponse(**self.runnable.invoke(*args, **kwargs))

    def astream_events(self, *args: Any, **kwargs) -> AsyncIterator[StreamEvent]:
        return self.runnable.astream_events(*args, **kwargs)

    def runnable_with_message_history(
        self, chain: Runnable, chat_history
    ) -> RunnableWithMessageHistory:
        conversation_chain = RunnableWithMessageHistory(
            chain,
            lambda _: chat_history,
            input_messages_key="input",
            history_messages_key="chat_history",
            output_messages_key="answer",
        )
        return conversation_chain
