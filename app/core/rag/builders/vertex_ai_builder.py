from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Literal
from google.cloud import discoveryengine_v1alpha as discoveryengine
from google.api_core.client_options import ClientOptions


EngineDataTypeStr = Literal["UNSTRUCTURED", "STRUCTURED", "WEBSIT<PERSON>", "BLENDED"]
EngineChunkTypeStr = Literal[
    "DOCUMENT_WITH_SNIPPETS", "DOCUMENT_WITH_EXTRACTIVE_SEGMENTS", "CHUNK"
]
SummaryTypeStr = Literal[
    "NONE", "VERTEX_AI_SEARCH", "GENERATE_GROUNDED_ANSWERS", "GEMINI"
]


class SearchConfigBuilder:
    @dataclass
    class SearchConfig:
        project_id: str
        location: str
        data_store_id: str
        system_prompt: str
        engine_data_type: str = "UNSTRUCTURED"
        engine_chunk_type: str = "CHUNK"
        summary_type: str = "VERTEX_AI_SEARCH"
        language_code: str = "ja"
        llm_model_version: str = "latest"
        page_size: int = 10
        additional_configs: Dict[str, Any] = field(default_factory=dict)

        def __post_init__(self) -> None:

            self.engine_data_type = self._validate_enum(
                self.engine_data_type, EngineDataTypeStr, "UNSTRUCTURED"
            )
            self.engine_chunk_type = self._validate_enum(
                self.engine_chunk_type, EngineChunkTypeStr, "CHUNK"
            )
            self.summary_type = self._validate_enum(
                self.summary_type, SummaryTypeStr, "VERTEX_AI_SEARCH"
            )

        @staticmethod
        def _validate_enum(value: str, enum_type: Any, default: str) -> str:
            if value in enum_type.__args__:
                return value
            print(f"Warning: Invalid value '{value}'. Using default: '{default}'")
            return default

        def to_dict(self) -> dict[str, str]:
            return {
                "project_id": self.project_id,
                "location": self.location,
                "data_store_id": self.data_store_id,
                "engine_data_type": self.engine_data_type,
                "engine_chunk_type": self.engine_chunk_type,
                "summary_type": self.summary_type,
                "system_prompt": self.system_prompt,
                "answer_language_code": self.language_code,
                "llm_model_version": self.llm_model_version,
            }

    def __init__(self, config: SearchConfig):
        self.config = config


class SearchClientFactory:
    @staticmethod
    def create_client(config: SearchConfigBuilder.SearchConfig) -> Any:
        client_options = None
        if config.location != "global":
            api_endpoint = f"{config.location}-discoveryengine.googleapis.com"
            client_options = ClientOptions(api_endpoint=api_endpoint)

        return {
            "search": discoveryengine.SearchServiceClient(client_options=client_options),
            "conversational": discoveryengine.ConversationalSearchServiceClient(client_options=client_options)
        }


class SearchRequestBuilder:
    @staticmethod
    def build_search_request(config: SearchConfigBuilder.SearchConfig, query: str) -> discoveryengine.SearchRequest:
        snippet_spec = None
        extractive_content_spec = None

        if config.engine_chunk_type == "DOCUMENT_WITH_SNIPPETS":
            snippet_spec = discoveryengine.SearchRequest.ContentSearchSpec.SnippetSpec(
                return_snippet=True
            )

        if config.engine_chunk_type == "DOCUMENT_WITH_EXTRACTIVE_SEGMENTS":
            snippet_spec = discoveryengine.SearchRequest.ContentSearchSpec.SnippetSpec(
                return_snippet=True
            )
            extractive_content_spec = discoveryengine.SearchRequest.ContentSearchSpec.ExtractiveContentSpec(
                max_extractive_answer_count=1,
                return_extractive_segment_score=True
            )

        summary_spec = None
        if config.summary_type == "VERTEX_AI_SEARCH":
            summary_spec = discoveryengine.SearchRequest.ContentSearchSpec.SummarySpec(
                summary_result_count=5,
                include_citations=True,
                ignore_adversarial_query=True,
                ignore_non_summary_seeking_query=True
            )

        return discoveryengine.SearchRequest(
            serving_config=f"projects/{config.project_id}/locations/{config.location}/collections/default_collection/engines/{config.data_store_id}/servingConfigs/default_serving_config",
            query=query,
            page_size=config.page_size,
            content_search_spec=discoveryengine.SearchRequest.ContentSearchSpec(
                snippet_spec=snippet_spec,
                extractive_content_spec=extractive_content_spec,
                summary_spec=summary_spec
            ),
            query_expansion_spec=discoveryengine.SearchRequest.QueryExpansionSpec(
                condition=discoveryengine.SearchRequest.QueryExpansionSpec.Condition.AUTO
            ),
            spell_correction_spec=discoveryengine.SearchRequest.SpellCorrectionSpec(
                mode=discoveryengine.SearchRequest.SpellCorrectionSpec.Mode.AUTO
            )
        )


class AnswerGenerationRequestBuilder:
    @staticmethod
    def build_query_understanding_spec(
            config: SearchConfigBuilder.SearchConfig) -> discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec:
        return discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec(
            query_rephraser_spec=discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryRephraserSpec(
                disable=False, max_rephrase_steps=1
            ),
            query_classification_spec=discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec(
                types=[
                    discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec.Type.ADVERSARIAL_QUERY,
                    discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec.Type.NON_ANSWER_SEEKING_QUERY
                ]
            )
        )

    @staticmethod
    def build_answer_generation_spec(
            config: SearchConfigBuilder.SearchConfig) -> discoveryengine.AnswerQueryRequest.AnswerGenerationSpec:
        return discoveryengine.AnswerQueryRequest.AnswerGenerationSpec(
            ignore_adversarial_query=False,
            ignore_non_answer_seeking_query=False,
            ignore_low_relevant_content=False,
            model_spec=discoveryengine.AnswerQueryRequest.AnswerGenerationSpec.ModelSpec(
                model_version=config.llm_model_version
            ),
            prompt_spec=discoveryengine.AnswerQueryRequest.AnswerGenerationSpec.PromptSpec(
                preamble=config.system_prompt
            ),
            include_citations=True,
            answer_language_code=config.language_code
        )


class VertexAISearchService:
    def __init__(self, config: SearchConfigBuilder.SearchConfig):
        self.config = config
        self.clients = SearchClientFactory.create_client(config)

    def search(self, query: str) -> Dict[str, Any]:
        request = SearchRequestBuilder.build_search_request(self.config, query)
        search_pager = self.clients["search"].search(request)
        return self._process_search_results(search_pager)

    def answer_query(self, query: str) -> Dict[str, Any]:
        query = discoveryengine.Query({"text": query})
        query_understanding_spec = AnswerGenerationRequestBuilder.build_query_understanding_spec(self.config)
        answer_generation_spec = AnswerGenerationRequestBuilder.build_answer_generation_spec(self.config)

        request = discoveryengine.AnswerQueryRequest(
            serving_config=self.config,
            query=query,
            query_understanding_spec=query_understanding_spec,
            answer_generation_spec=answer_generation_spec
        )

        response = self.clients["conversational"].answer_query(request=request)
        return {
            "query": query,
            "answers": response.answers,
            "citations": response.citations
        }

    def _process_search_results(self, search_pager):
        results = []
        for result in search_pager:
            if hasattr(result, 'document'):
                results.append(self._parse_document_result(result.document))

        return {
            "total_results": len(results),
            "results": results,
            "attribution_token": search_pager.attribution_token,
            "next_page_token": search_pager.next_page_token
        }

    def _parse_document_result(self, document):
        metadata = document.get('struct_data', {})
        content = metadata.get('content', '')
        return {
            "metadata": metadata,
            "page_content": content
        }
