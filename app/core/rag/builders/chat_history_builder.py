from typing import Any
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder


class HistoryAwareRetriever:
    __slots__ = ['llm', 'retriever', 'chat_history']

    def __init__(self, llm, retriever, chat_history):
        self.llm = llm
        self.retriever = retriever
        self.chat_history = chat_history

    async def ainvoke(self, query):
        reformulated_query = await self.reformulate_query(query)
        return await self.retriever.ainvoke(reformulated_query)

    async def reformulate_query(self, query):
        contextualize_q_system_prompt = (
            "Given a chat history and the latest user question "
            "which might reference context in the chat history, "
            "formulate a standalone question which can be understood "
            "without the chat history. Do NOT answer the question, "
            "just reformulate it if needed and otherwise return it as is."
        )
        prompt_with_history = ChatPromptTemplate.from_messages([
            ("system", contextualize_q_system_prompt),
            ("human", query),
            ("chat_history", ' '.join([msg.content for msg in self.chat_history])),
        ])
        result = await self.llm.ainvoke(prompt_with_history)

        return result['text']