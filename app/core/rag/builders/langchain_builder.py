import re
import os
import traceback
from datetime import datetime, date
from typing import Optional, Dict, Any
from google.api_core import exceptions
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.retrievers import BaseRetriever
from langchain_core.messages import BaseMessage, HumanMessage, messages_from_dict
from langchain_core.runnables.schema import StreamEvent
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.chains import create_retrieval_chain
from langchain.chains.history_aware_retriever import create_history_aware_retriever
from langchain_openai import ChatOpenAI
from langchain_google_vertexai import ChatVertexAI
from langchain_google_vertexai.model_garden import ChatAnthropicVertex
from langchain_google_community import (VertexAISearch<PERSON>etriever, VertexAIMultiTurnSearchRetriever,
                                        VertexAISearchSummaryTool)
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from app.helpers.constants import USER_INFO_PROMPT, CONTEXT_HISTORY_PROMPT, FORMAT_OUTPUT
from app.core.rag.vectorstores.chroma import ChromaVectorStore
from app.core.rag.retrievers.chroma import ChromaRetriever
from app.db.base import get_db
from sqlalchemy.orm import Session
from fastapi import Depends
from app.models.datastores_information import DatastoresInformation
from app.utils.common import get_message


class RetrievalAndLLMChain:
    __slots__ = ['llm', 'project_id', 'llm_type', 'system_prompt', 'human_prompt', 'chain_prompt', 'format_output',
                 'query',
                 'retriever', 'chain_of_thought_prompt', 'callback_handler', 'use_ragsource', 'db']

    def __init__(self, db: Session, llm_type, query, retriever_type='single', system_prompt: str | None = None,
                 human_prompt: str | None = None, chain_prompt: str | None = None, format_output: str | None = None,
                 config: dict | None = None, project_id: str | None = None, location: str | None = None,
                 model_name: str | None = None, api_key: str | None = None,
                 chain_of_thought_prompt: bool = True, user_reasoning_preference: bool | None = None,
                 use_ragsource: bool = True, callback_handler: Any | None = None):
        self.db = db
        self.llm_type = llm_type
        self.system_prompt = system_prompt
        self.human_prompt = human_prompt or ""
        self.chain_prompt = chain_prompt or ""
        self.format_output = format_output or ""

        # Apply reasoning logic: User preference takes highest prioritychain_of_thought_prompt
        # If user has explicit preference, use it regardless of use case setting
        if user_reasoning_preference is not None:
            self.chain_of_thought_prompt = user_reasoning_preference
        else:
            # Fallback to use case setting
            self.chain_of_thought_prompt = chain_of_thought_prompt

        self.query = query
        self.use_ragsource = use_ragsource
        self.retriever = self.create_vertex_ai_retriever(retriever_type=retriever_type,

                                                         retriever_params=self.handle_datastore_id(
                                                             config)) if self.use_ragsource else None
        self.callback_handler = callback_handler
        self.llm = self._get_llm(project_id=project_id, location=location, model_name=model_name, api_key=api_key,
                                 temperature=0)

    @classmethod
    def create_vertex_ai_retriever(cls, retriever_type: str = "single", retriever_params: Optional[Dict] = None):
        try:
            if retriever_params is None:
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail="Retriever params cannot be None.")

            # required_params = ["project", "location", "search_engine_id"]
            # missing_params = [param for param in required_params if param not in retriever_params]
            # if missing_params:
            #     raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
            #                                        detail=f"Missing required parameters: {missing_params}")

            if retriever_type == "multi":
                retriever = VertexAIMultiTurnSearchRetriever(**retriever_params)
            else:
                retriever = VertexAISearchRetriever(**retriever_params)

        except exceptions.PermissionDenied:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail="Retriever access denied. Please ensure LLM API is enabled in your project.")
        except exceptions.InvalidArgument:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=f"Retriever Invalid Configuration.")
        except exceptions.NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"Retriever DataStore not found. Please check if the datastore.")
        except exceptions.ResourceExhausted:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=f"Retriever API resource exhausted or quota exceeded.")

        return retriever

    def _get_llm(self, project_id=None, location=None, model_name=None, api_key=None, temperature=0):
        try:
            callbacks = [self.callback_handler] if self.callback_handler else None

            match self.llm_type.lower():
                case "vertexai":
                    llm = ChatVertexAI(
                        model_name=model_name,
                        temperature=0.5,
                        callbacks=callbacks
                    )

                case "anthropic" | "claude-vertexai" | "claude":
                    llm = ChatAnthropicVertex(
                        model_name=model_name,
                        project=project_id,
                        location=location,
                        temperature=0.3,
                        callbacks=callbacks
                    )

                case "openai":
                    llm = ChatOpenAI(
                        model_name=model_name,
                        openai_api_key=api_key,
                        temperature=temperature,
                        callbacks=callbacks
                    )

                case _:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"Unsupported LLM type: {self.llm_type}")

            return llm
        except:
            print('traceback llm: ', traceback.format_exc())

    @staticmethod
    def process_references(references):
        result = []
        file_map = {}

        for item in references:
            match = re.match(r"(gs://.+?)(?::(\d+)|(\d+))$", item)
            if match:
                file_name = match.group(1)
                page = match.group(2) if match.group(2) else match.group(3)
            else:
                file_name = item
                page = None

            if file_name in file_map:
                file_map[file_name].append(int(page)) if page else []
            else:
                file_map[file_name] = [int(page)] if page else []

        for file_name, pages in file_map.items():
            result.append(
                {"source": file_name, "pages": sorted(set(pages)), "data_type": "structured", "from_source": "s3"})

        return result

    def get_system_prompt_example(self):
        return """あなたは会社の総務責任者です。以下の条件で回答をしてください。
    # 条件
    - アプリ側で思考内容の表示非表示の制御を行っているため、以下の「# 回答フォーマット」に厳格に従った回答をしてください。
    - [思考の内容]には回答に至るまでの思考を記載してください。開発者のデバッグに使うことが多いため、極力詳細に記載してください。
    - [回答の内容]には詳細な回答を記載してください。こちらはユーザー向けです。長くなっても良いので詳しい回答が喜ばれます。
    - 回答内で情報を引用する際は、必ず以下の形式で参照元を明記してください：
      [space] **[文書X][[source]](source_url)**
    - 特に重要な情報や規定を引用する場合は、対応する参照元の文書番号とリンクを必ず記載してください。
    - ユーザーに最初の質問で「もっと詳しく」と追加で質問をされないように、最初から詳細な情報を盛り込んでください。
    - 一概に回答を行うことが難しい場合は、多角的な回答を行うことでユーザーの判断を補助できるような回答を作成してください。
    - コンテキストの情報のみを使用して回答を作成してください。一般的な知識は使用しないでください。"""

    def get_human_prompt_example(self, formatted_content):
        return f"""ユーザーの質問: '{self.query}'

            # コンテキスト
            {formatted_content}

            以下のフォーマットで回答を作成してください：

            Reasoning:
            [思考の内容を記載]
            - 各文書の関連性と重要性を評価
            - 文書間で矛盾がないか確認
            - 情報の新旧を確認

            FinalAnswer:
            [回答の内容を記載]
            - 重要な情報を引用する際は、文書番号とソースリンクを含めて記載してください
            例：「この規定によると... [資料 1][[link]](対応するURL)」"""

    def format_docs(self, docs, use_ragsource: bool = True):
        formatted_texts = []
        source_references = []
        upload_references = []
        uploaded_sources = []

        if docs and isinstance(docs, list):
            for idx, doc in enumerate(docs, start=1):
                page_content = doc.page_content
                source = doc.metadata.get("source", "Unknown source")
                formatted_text = f"""{page_content}"""
                formatted_texts.append(formatted_text)

                if not use_ragsource:
                    upload_references.append((source, doc.metadata.get('page', 0)))
                else:
                    source_references.append(source)

        all_content = "\n".join(formatted_texts)

        if not use_ragsource:
            source_page_mapping = {}
            for source, page in upload_references:
                if source not in source_page_mapping:
                    source_page_mapping[source] = []
                source_page_mapping[source].append(page)

            uploaded_sources = [
                {'source': source, 'pages': sorted(set(pages)), "data_type": "structured", "from_source": "s3"} for
                source, pages in source_page_mapping.items()]

        return {
            'content': all_content,
            'references': self.process_references(source_references) if use_ragsource else uploaded_sources
        }

    def _paser_human_prompt(self, formatted_content):
        from app.helpers.constants import FORMAT_OUTPUT

        if not self.chain_of_thought_prompt:
            return f"{formatted_content}\n\n"

        # Use default FORMAT_OUTPUT if format_output is None or empty
        format_to_use = self.format_output or FORMAT_OUTPUT
        return f"{formatted_content}\n\n{format_to_use}\n"

    async def run_with_message_history(self, history_messages: list | None = None):
        chat_history = history_messages or []
        contextualize_h_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", CONTEXT_HISTORY_PROMPT),
                MessagesPlaceholder("chat_history", optional=True),
                ("human", self.query),
            ]
        )

        # print(f"Prompt input variables: {contextualize_h_prompt.input_variables}")
        if history_messages:
            q_history_chain = (
                    contextualize_h_prompt
                    | self.llm
                    | StrOutputParser()
            )

            q_history_answer = await q_history_chain.ainvoke(input={
                "chat_history": chat_history
            })

        contextualize_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", CONTEXT_HISTORY_PROMPT),
                MessagesPlaceholder("chat_history", optional=True),
                ("human", self.query),
            ]
        )

        # standard_chain = contextualize_q_prompt | self.llm | StrOutputParser()
        # results = await standard_chain.ainvoke(input={
        #     "question": self.query,
        #     "chat_history": chat_history
        # })
        #
        # history_aware_retriever = create_history_aware_retriever(
        #     self.llm, self.retriever, contextualize_q_prompt
        # )
        #
        # qa_prompt = ChatPromptTemplate.from_messages(
        #     [
        #         ("system", SYSTEM_PROMPT),
        #         MessagesPlaceholder("chat_history"),
        #         ("human", "{question}"),
        #     ]
        # )
        #
        # question_answer_chain = create_stuff_documents_chain(self.llm, qa_prompt)
        # rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)

    async def run(self, params, history_messages: list | None = None, user_info: dict | None = None):
        try:
            system_prompt = self.system_prompt
                        
            # Modify system prompt when reasoning is disabled
            if not self.chain_of_thought_prompt:
                # Remove reasoning format instructions from system prompt
                if system_prompt:
                    # Remove lines containing reasoning format instructions
                    lines = system_prompt.split('\n')
                    filtered_lines = []
                    skip_reasoning_section = False
                    
                    for line in lines:
                        line_lower = line.lower().strip()
                        # Skip reasoning format section
                        if any(keyword in line_lower for keyword in ['reasoning:', 'finalanswer:', '回答フォーマット', 'reasoning', 'final answer']):
                            skip_reasoning_section = True
                            continue
                        # Stop skipping when we reach a new section or non-format content
                        elif skip_reasoning_section and (line.strip().startswith('#') or line.strip() == '' or not any(char in line for char in ['[', ']', ':'])):
                            skip_reasoning_section = False
                            if line.strip() != '':
                                filtered_lines.append(line)
                        elif not skip_reasoning_section:
                            filtered_lines.append(line)
                    
                    system_prompt = '\n'.join(filtered_lines)
                    
                    # Add simple instruction for direct answering
                    system_prompt += "\n\n注意: 質問に対して直接的で明確な回答を提供してください。思考プロセスや特別なフォーマットは不要です。"

            # if user_info:
            #     system_prompt = f"{system_prompt}\n\n{USER_INFO_PROMPT.format(full_name=user_info['full_name'], roles=user_info['roles'])}"

            chat_history = history_messages or []
            retriever = await self.retriever.ainvoke(self.query) if self.retriever else []
            human_message_content = []
            context = ''
            references = []

            if self.use_ragsource:
                format_docs = self.format_docs(retriever)
                context = self._paser_human_prompt(format_docs['content'])
                references = format_docs['references']
            else:
                # Handle non-RAG source case
                context = ''
                # vectorstore = ChromaRetriever()
                if params.documents and params.chatroom_code:
                    for doc_entry in params.documents:
                        source = doc_entry.source
                        document = doc_entry.document
                        references.append(
                            {'source': source, 'pages': [], "data_type": "structured", "from_source": "s3"})
                        file_extension = os.path.splitext(source)[1].lower()
                        if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.pdf']:
                            # uri = {
                            #     'type': 'image_url',
                            #     'image_url': f'data:image/png;base64,${encoded_string}',
                            # }
                            human_message_content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": (
                                        source.replace("gs://", "https://storage.googleapis.com/")
                                    )
                                }
                            })
                        elif file_extension in ['.mp3', '.wav', '.ogg', '.m4a', '.flac']:
                            mime_type_map = {
                                '.mp3': 'audio/mpeg',
                                '.wav': 'audio/wav',
                                '.ogg': 'audio/ogg',
                                '.m4a': 'audio/mp4',
                                '.flac': 'audio/flac'
                            }
                            mime_type = mime_type_map.get(file_extension, 'audio/mpeg')
                            human_message_content.append({
                                "type": "media",
                                "mime_type": mime_type,
                                "file_uri": source.replace("gs://", "https://storage.googleapis.com/")
                            })
                        elif file_extension in ['.mp4', '.mov', '.avi', '.webm', '.mkv']:
                            mime_type_map = {
                                '.mp4': 'video/mp4',
                                '.mov': 'video/quicktime',
                                '.avi': 'video/x-msvideo',
                                '.webm': 'video/webm',
                                '.mkv': 'video/x-matroska'
                            }
                            mime_type = mime_type_map.get(file_extension, 'video/mp4')
                            human_message_content.append({
                                "type": "media",
                                "mime_type": mime_type,
                                "file_uri": source.replace("gs://", "https://storage.googleapis.com/")
                            })
                        else:
                            # content_file = '\n'.join([doc.page_content for doc in document])
                            human_message_content.append({
                                "type": "text",
                                "text": str(document),
                            })
                #         if document:
                #             vectorstore.add_documents(documents=document, source=source,
                #                                       chatroom_code=params.chatroom_code)
                #
                # retriever = vectorstore.query(self.query, chatroom_code=params.chatroom_code, sources=references)
                # format_docs = self.format_docs(retriever, use_ragsource=False)

                # Apply reasoning format even for non-RAG source
                context = self._paser_human_prompt(context)

            def preview_data(data):
                print(f"Preview data: {data}\n\n")
                return data

            human_message_content.append({
                "type": "text",
                "text": f"Context:\n{context}\n"
                        f"Question:\n{self.query}\n"
                        f"If relevant document is available, please refer to it if necessary. \n"
            })

            prompt_template = ChatPromptTemplate.from_messages([
                ("system", system_prompt + f'\ntoday: {date.today()}'),
                MessagesPlaceholder("chat_history"),
                HumanMessage(content=human_message_content)
            ])

            chains = (
                    prompt_template
                    # | preview_data
                    | self.llm
                    | StrOutputParser()
            )

            results = await chains.ainvoke(input={
                "chat_history": chat_history,
            })

            token_usage = None
            if self.callback_handler:
                token_usage = {
                    'prompt_tokens': self.callback_handler.token_info.prompt_tokens,
                    'completion_tokens': self.callback_handler.token_info.completion_tokens,
                    'total_tokens': self.callback_handler.token_info.total_tokens
                }

            return {
                "response": results,
                "token_usage": token_usage,
                "references": references
            }

        except exceptions.FailedPrecondition:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   # detail="Chat AI is only available when LLM add-on is enabled."
                                   detail="Agent Builder設定中です。しばらくお待ちください。"
                                   )
        except exceptions.ResourceExhausted:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=f"Chat AI resource exhausted or quota exceeded.")
        except exceptions.ServiceUnavailable:
            raise GatewayException(status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                                   detail=f"Chat AI service is currently unavailable.")

        except GatewayException:
            print(traceback.format_exc())
            raise

        except Exception:
            print(traceback.format_exc())
            error_trace = traceback.format_exc()
            if "Incorrect API key" in error_trace:
                raise GatewayException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="OpenAI キーが存在しないか正しくありません。確認するか、管理者に報告してください。"
                )
            else:
                print(error_trace)
                raise GatewayException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Agent Builder設定中です。しばらくお待ちください。"
                )

    def handle_datastore_id(self, config: dict | None = None) -> dict:
        try:
            if not config or 'data_store_id' not in config:
                raise GatewayException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="Invalid config."
                )

            datastore_info = self.db.query(DatastoresInformation).filter(
                DatastoresInformation.datastore_id == config.get('data_store_id')
            ).first()

            if datastore_info is None:
                raise GatewayException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="データストアが存在しないか、システムから削除されています。"
                )
            config['project_id'] = datastore_info.project_id
            return config

        except GatewayException:
            raise
        except Exception:
            print(traceback.format_exc())
            raise GatewayException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Agent Builder設定中です。しばらくお待ちください。"
            )
