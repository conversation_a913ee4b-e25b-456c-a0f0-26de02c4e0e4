from typing import Any, AsyncIterator, Callable, Dict, List, Optional
from pydantic import BaseModel
from fastapi import status as http_status
from langchain.callbacks.base import Async<PERSON><PERSON>backHandler
from langchain_openai import ChatOpenAI
from langchain_google_vertexai import ChatVertexAI
from langchain_google_vertexai.model_garden import ChatAnthropicVertex
from app.helpers.exceptions import GatewayException


class TokenUsage(BaseModel):
    total_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0

    def __str__(self) -> str:
        return (
            f"Total Tokens: {self.total_tokens}\n"
            f"\tPrompt Tokens: {self.prompt_tokens}\n"
            f"\tCompletion Tokens: {self.completion_tokens}\n"
        )

    def increment_prompt_tokens(self, count: int) -> None:
        self.prompt_tokens += count
        self.total_tokens += count

    def increment_completion_tokens(self, count: int) -> None:
        self.completion_tokens += count
        self.total_tokens += count


class BaseTokenInfoCallbackHandler(AsyncCallbackHandler):
    __slots__ = ['token_usage', 'model']

    def __init__(self, *args, **kwargs):
        self.token_usage = TokenUsage()
        super().__init__(*args, **kwargs)

    def __str__(self) -> str:
        return str(self.token_usage)

    @property
    def token_info(self) -> TokenUsage:
        return self.token_usage


class VertexAITokenCallbackHandler(BaseTokenInfoCallbackHandler):
    def __init__(self, model_name: str, *args, **kwargs):
        self.model = ChatVertexAI(model=model_name)
        if not self.model:
            raise GatewayException(http_status.HTTP_404_NOT_FOUND, detail="Model Token VertexAI Initialization Failed")

        super().__init__(*args, **kwargs)

    async def on_llm_end(self, response: Any, **kwargs: Any) -> None:
        print(f"Response: {response}")

        for generation_list in response.generations:
            for generation in generation_list:
                usage_metadata = getattr(generation.message, "usage_metadata", {})

                input_tokens = usage_metadata.get("input_tokens", 0)
                output_tokens = usage_metadata.get("output_tokens", 0)

                self.token_usage.increment_prompt_tokens(input_tokens)
                self.token_usage.increment_completion_tokens(output_tokens)

    async def on_chat_model_start(
            self,
            serialized: Dict[str, Any],
            message_batches: List[List[Any]],
            **kwargs: Any,
    ) -> None:
        for batch in message_batches:
            self.token_usage.increment_prompt_tokens(
                self.model.get_num_tokens_from_messages(batch)
            )


class ClaudeVertexAITokenCallbackHandler(BaseTokenInfoCallbackHandler):
    def __init__(self, model_name: str, project: str, location: str, temperature: float = 0.0, *args, **kwargs):
        self.model = ChatAnthropicVertex(model_name=model_name, project=project, location=location, temperature=temperature)
        if not self.model:
            raise GatewayException(http_status.HTTP_404_NOT_FOUND, detail="Model Token ClaudeAI Initialization Failed")

        super().__init__(*args, **kwargs)

    async def on_llm_end(self, response: Any, **kwargs: Any) -> None:
        usage = response.llm_output['usage']
        self.token_usage.increment_prompt_tokens(
            usage['input_tokens']
        )
        self.token_usage.increment_completion_tokens(
            usage['output_tokens']
        )

    async def on_chat_model_start(
            self,
            serialized: Dict[str, Any],
            message_batches: List[List[Any]],
            **kwargs: Any,
    ) -> None:
        pass


class OpenAITokenCallbackHandler(BaseTokenInfoCallbackHandler):
    def __init__(self, model_name: str, api_key: str, temperature: float = 0.0, *args, **kwargs):
        self.model = ChatOpenAI(model_name=model_name, openai_api_key=api_key, temperature=temperature)
        if not self.model:
            raise GatewayException(http_status.HTTP_404_NOT_FOUND, detail="Model Token OpenAI Initialization Failed")

        super().__init__(*args, **kwargs)

    async def on_llm_end(self, response: Any, **kwargs: Any) -> None:
        for generation_list in response.generations:
            for generation in generation_list:
                self.token_usage.increment_completion_tokens(
                    self.model.get_num_tokens(generation.text)
                )

    async def on_chat_model_start(
            self,
            serialized: Dict[str, Any],
            message_batches: List[List[Any]],
            **kwargs: Any,
    ) -> None:
        for batch in message_batches:
            self.token_usage.increment_prompt_tokens(
                self.model.get_num_tokens_from_messages(batch)
            )
