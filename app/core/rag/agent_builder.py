from google.cloud import discoveryengine_v1 as discoveryengine


class AgentBuilder:
    def __init__(self, db_usecase):
        self.db_usecase = db_usecase
        self.client_options = self._create_client_options()
        self.serving_config = self._create_serving_config()

    def _create_client_options(self):
        if self.db_usecase.location != "global":
            return discoveryengine.ClientOptions(
                api_endpoint=f"{self.db_usecase.location}-discoveryengine.googleapis.com"
            )
        return None

    def _create_serving_config(self):
        return (
            f"projects/{self.db_usecase.project_id}/locations/{self.db_usecase.location}"
            f"/collections/default_collection/engines/{self.db_usecase.data_store_id}/servingConfigs/default_serving_config"
        )

    def build_query_understanding_spec(self):
        return discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec(
            query_rephraser_spec=discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryRephraserSpec(
                disable=False, max_rephrase_steps=1
            ),
            query_classification_spec=discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec(
                types=[
                    discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec.Type.ADVERSARIAL_QUERY,
                    discoveryengine.AnswerQueryRequest.QueryUnderstandingSpec.QueryClassificationSpec.Type.NON_ANSWER_SEEKING_QUERY,
                ]
            ),
        )

    def build_answer_generation_spec(self):
        return discoveryengine.AnswerQueryRequest.AnswerGenerationSpec(
            ignore_adversarial_query=False,
            ignore_non_answer_seeking_query=False,
            ignore_low_relevant_content=False,
            model_spec=discoveryengine.AnswerQueryRequest.AnswerGenerationSpec.ModelSpec(
                model_version=self.db_usecase.llms_model_version
            ),
            prompt_spec=discoveryengine.AnswerQueryRequest.AnswerGenerationSpec.PromptSpec(
                preamble=self.db_usecase.system_prompt
            ),
            include_citations=True,
            answer_language_code=self.db_usecase.answer_language_code,
        )
