import traceback
from pathlib import Path
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from jinja2 import Environment, FileSystemLoader
from app.helpers.constants import EmailStatus


class EmailAsyncClient:
    __slots__ = ['smtp_server', 'smtp_port', 'sender_email', 'password', 'smtp']

    def __init__(self, config: dict):
        self.smtp_server = config.get('smtp_server')
        self.smtp_port = config.get('smtp_port')
        self.sender_email = config.get('sender_email')
        self.password = config.get('password')
        self.smtp = None

    async def connect(self):
        try:
            self.smtp = aiosmtplib.SMTP(hostname=self.smtp_server, port=self.smtp_port, use_tls=self.smtp_port == 465)
            await self.smtp.connect()

            # if self.smtp_port != 465:
            #     await self.smtp.starttls()

            await self.smtp.login(self.sender_email, self.password)
            print('Login Email successfully')
        except Exception as e:
            print(f"Error during email connection: {e}")
            self.smtp = None


class EmailAsyncTemplate:
    __slots__ = ['template']

    def __init__(self, template_name: str):
        jinja2_env = Environment(loader=FileSystemLoader(str(Path(__file__).parent.resolve() / '../../../templates/')))
        self.template = jinja2_env.get_template(template_name)

    def render(self, context: dict) -> str:
        return self.template.render(context)


class EmailAsyncService:
    __slots__ = ['client']

    def __init__(self, client: EmailAsyncClient):
        self.client = client

    async def send_email(self, recipient: str, subject: str, html_content: str) -> str:
        message = MIMEMultipart()
        message['To'] = recipient
        message['From'] = self.client.sender_email
        message['Subject'] = subject
        message.attach(MIMEText(html_content, 'html'))

        try:
            if not self.client.smtp or not self.client.smtp.is_connected:
                await self.client.connect()

            await self.client.smtp.send_message(message)
            print(f"Email sent successfully to {recipient}")
            return EmailStatus.SUCCESS
        except Exception as exc:
            print(f'Failed to send email to {recipient}: {traceback.format_exc()}')
            return EmailStatus.FAIL
