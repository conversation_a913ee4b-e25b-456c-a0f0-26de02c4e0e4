import smtplib
import traceback
from pathlib import Path
from email.mime.text import MI<PERSON><PERSON>ext
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from jinja2 import Environment, FileSystemLoader
from app.helpers.constants import EmailStatus


class EmailClient:
    __slots__ = ['smtp_server', 'smtp_port', 'sender_email', 'password', 'smtp']

    def __init__(self, config: dict):
        self.smtp_server = config.get('smtp_server')
        self.smtp_port = config.get('smtp_port')
        self.sender_email = config.get('sender_email')
        self.password = config.get('password')
        self.smtp = None
        self.connect()

    def connect(self):
        """Establish the SMTP connection."""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()  # Secure connection
            self.smtp.login(self.sender_email, self.password)
            print('Login Email successfully')
        except Exception as e:
            print(f"Error during email connection: {e}")
            self.smtp = None

    def close_connection(self):
        """Close the SMTP connection safely."""
        if self.smtp:
            try:
                self.smtp.quit()
                print("Connection closed successfully")
            except smtplib.SMTPServerDisconnected:
                print("Connection was already closed")
            except Exception as e:
                print(f"Error closing the connection: {e}")
            finally:
                self.smtp = None
        else:
            print("No SMTP connection to close.")

    def __del__(self):
        """Ensure connection is closed when the object is deleted."""
        if self.smtp:
            self.close_connection()
        else:
            print("No SMTP connection to close during cleanup.")


class EmailTemplate:
    __slots__ = ['template']

    def __init__(self, template_name: str):
        jinja2_env = Environment(loader=FileSystemLoader(str(Path(__file__).parent.resolve() / '../../../templates/')))
        self.template = jinja2_env.get_template(template_name)

    def render(self, context: dict) -> str:
        return self.template.render(context)


class EmailService:
    __slots__ = ['client']

    def __init__(self, client: EmailClient):
        self.client = client

    def send_email(self, recipient: str, subject: str, html_content: str) -> str:
        message = MIMEMultipart()
        message['To'] = recipient
        message['From'] = self.client.sender_email
        message['Subject'] = subject
        message.attach(MIMEText(html_content, 'html'))

        try:
            if not self.client.smtp:
                self.client.connect()
            resp = self.client.smtp.sendmail(self.client.sender_email, recipient, message.as_string())

            if not resp:
                print(f"Email sent successfully to {recipient}")
                return EmailStatus.SUCCESS
            else:
                print(f"Failed to send email to {recipient}: {resp}")
                return EmailStatus.FAIL
        except Exception as exc:
            print(f'Failed to send email to {recipient}: {traceback.format_exc()}')
            return EmailStatus.FAIL
