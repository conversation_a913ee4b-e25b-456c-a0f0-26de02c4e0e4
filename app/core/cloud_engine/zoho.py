import os
import httpx
import pandas as pd
import pytz
import datetime
from typing import Dict, List, Any
from google.oauth2 import service_account
import uuid
from sqlalchemy.dialects.postgresql import insert
from app.models.users import Users, Role, UserRole, Group, UserGroup
from app.helpers.security import hash_password
from sqlalchemy.ext.asyncio import AsyncSession
import requests
from sqlalchemy.orm import Session

# Configure timezone and environment
JST = pytz.timezone('Asia/Tokyo')

REFRESH_TOKEN = "**********************************************************************"
CLIENT_ID = "1000.5RVLYO7UBQJE7T9T2NQ26YYVW8U6KU"
CLIENT_SECRET = "e454a7ed70674d5800881fb6579842965d351d689c"


class ZohoPlatform:
    def __init__(self, credentials_path=None):
        self.get_list_users_zoho = self._get_data_user_zoho
        self.credentials_path = credentials_path or os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.credentials = self._load_credentials(self.credentials_path)

    def _load_credentials(self, credentials_path):
        if credentials_path:
            return service_account.Credentials.from_service_account_file(credentials_path)
        return None

    async def _get_data_user_zoho(self, db: Session):
        try:
            utc_dt = datetime.datetime.now(datetime.timezone.utc)
            now = utc_dt.astimezone(JST).strftime("%Y-%m-%d %H:%M:%S")

            # Get authentication token
            response = self.refresh_token(REFRESH_TOKEN, CLIENT_ID, CLIENT_SECRET)
            print(response)

            access_token = response['access_token']
            token_type = response['token_type']
            headers = {
                'Authorization': f"{token_type} {access_token}",
                'Content-Type': 'application/json'
            }
            print(f"[SYNC-ZOHO] Start sync users from Zoho People")
            list_users = await self.get_zoho_people_user(headers, now)
            upload_dir = os.path.expanduser("uploads")
            os.makedirs(upload_dir, exist_ok=True)

            # Define the full path for the CSV file
            file_path = os.path.join(upload_dir, "zoho_people.csv")
            if os.path.exists(file_path):
                os.remove(file_path)

            # Save the DataFrame to CSV
            list_users.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"[SYNC-ZOHO] Start sync users to DB")
            self.sync_users_to_db(db)
            return file_path
        except Exception as e:
            print(f"Error: {e}")

    @staticmethod
    def refresh_token(refresh_token: str, client_id: str, client_secret: str) -> Dict[str, Any]:
        """Refresh Zoho authentication token."""
        print(f"[REFRESH_TOKEN_ZOHO]---------OK")
        url = "https://accounts.zoho.com/oauth/v2/token"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = f'refresh_token={refresh_token}&client_id={client_id}&client_secret={client_secret}&grant_type=refresh_token'
        response = requests.request("POST", url, headers=headers, data=payload)
        return response.json()

    async def get_zoho_people_user(self, headers: Dict[str, str], execute_datetime: str) -> pd.DataFrame:
        all_data = []
        sIndex = 1
        limit = 200
        url_base = "https://people.zoho.com/people/api/forms/employee/getRecords?"

        while True:
            url = f"{url_base}sIndex={sIndex}&limit={limit}"
            print(url)
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)

            data = response.json()
            # print(data)

            if 'result' in data['response']:
                records = data['response']['result']
                data_list = [
                    (
                        list(record.values())[0][0]['Zoho_ID'],
                        list(record.values())[0][0]['EmployeeID'],
                        list(record.values())[0][0]['name1'],
                        list(record.values())[0][0]['Employeestatus'],
                        list(record.values())[0][0]['MailAddress_musashino'],
                        list(record.values())[0][0]['Role']
                    )
                    for record in records if list(record.values())[0][0].get('MailAddress_musashino')
                ]
                all_data.extend(data_list)
                if len(records) < limit:
                    break
                sIndex += limit
            else:
                print("Key 'result' not found in the response")
                break

        data = pd.DataFrame(all_data, columns=[
            'Zoho_ID',
            'EmployeeID',
            'name1',
            'Employeestatus',
            'MailAddress_musashino',
            'Role'
        ])

        # Add execution timestamp
        data['executed_at'] = execute_datetime
        print(f"[SYNC-ZOHO] Stop sync users from Zoho People")
        return data

    def sync_users_to_db(self, db: Session):
        df = pd.read_csv("uploads/zoho_people.csv")
        list_users = df.to_dict(orient='records')
        prepared_users = []
        for user in list_users:
            prepared_users.append({
                "full_name": user.get("name1"),
                "email": user.get("MailAddress_musashino"),
                "hashed_password": hash_password("Kj5LC6a!"),
                "uid": f"{uuid.uuid4().hex}".upper(),
                "is_first_login": True,
                "is_active": True,
                "is_deleted": False,
                "role": user.get("Role")
            })
        print(f"[SYNC] Prepared users")
        self.batch_insert_users(db, prepared_users)

    def batch_insert_users(self, db: Session, users: list[dict], batch_size: int = 1000):
        print(f"[SYNC] Start batch insert users")
        role = db.query(Role).filter(Role.name == "User").first()
        group = db.query(Group).filter(Group.name == "Gemini活用セミナー").first()

        # Tạo Group nếu chưa tồn tại
        if not group:
            group = Group(
                name="Gemini活用セミナー",
                description="Group for new users created by excel",
                is_active=True,
                is_deleted=False
            )
            db.add(group)
            db.commit()
            db.refresh(group)

        user_roles_batch = []
        user_groups_batch = []

        for i in range(0, len(users), batch_size):
            batch = users[i:i + batch_size]

            user_data = [{k: v for k, v in user.items() if k != 'role'} for user in batch]
            print(f"[SYNC] Inserting {len(user_data)} users")

            stmt = insert(Users).values(user_data)
            stmt = stmt.on_conflict_do_nothing(
                index_elements=["email"])  # Bỏ qua nếu email đã tồn tại

            db.execute(stmt)
            db.commit()

            # Truy vấn lại các user vừa được thêm vào để lấy ID
            inserted_users = db.query(Users).filter(Users.email.in_([user['email'] for user in batch])).all()

            for user in inserted_users:
                if role:
                    user_roles_batch.append(UserRole(user_id=user.id, role_id=role.id))
                if group:
                    user_groups_batch.append(UserGroup(user_id=user.id, group_id=group.id))

            # Bulk insert UserRole theo batch
            if user_roles_batch:
                print(f"[SYNC] Inserting {len(user_roles_batch)} UserRoles")
                db.bulk_save_objects(user_roles_batch)
                db.commit()
                user_roles_batch.clear()

            # Bulk insert UserGroup theo batch
            if user_groups_batch:
                print(f"[SYNC] Inserting {len(user_groups_batch)} UserGroups")
                db.bulk_save_objects(user_groups_batch)
                db.commit()
                user_groups_batch.clear()

        print(f"[SYNC-ZOHO] Done sync users to DB")
