import io
import os
import asyncio
import traceback
import boto3
import fitz
import tempfile
import json
import docx2python
import base64
import hashlib
import re
import csv
from botocore.exceptions import ClientError
from google.auth import default
from google.cloud import storage, discoveryengine_v1
from google.oauth2 import service_account
from google.api_core.client_options import ClientOptions
from googleapiclient.discovery import build
from google.cloud import discoveryengine
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from datetime import timedelta
from google.cloud import storage, discoveryengine_v1beta
import httpx
from google.auth import default
from typing import List, Optional
from google.oauth2 import service_account


class GoogleCloudPlatform:
    __slots__ = ['credentials_path', 'credentials', 'storage_client', 'discovery_engine_client', 'list_project',
                 'create_data_store_AB', 'create_data_store_AB_trigger', 'import_document', 'list_model_ai']

    def __init__(self, credentials_path=None):
        self.credentials_path = credentials_path or os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.credentials = self._load_credentials(self.credentials_path)
        self.storage_client = self._get_storage_client()
        self.discovery_engine_client = self._get_discovery_engine_client()
        self.list_project = self._get_list_project_id()
        self.create_data_store_AB = self._create_data_store_AB
        self.create_data_store_AB_trigger = self._create_data_store_AB_trigger
        self.import_document = self._import_document
        self.list_model_ai = self._list_model_ai

    def _load_credentials(self, credentials_path):
        if credentials_path:
            return service_account.Credentials.from_service_account_file(credentials_path)
        return None

    @staticmethod
    def init_credentials():
        creds, project_id = default()
        return creds, project_id

    def _get_storage_client(self):
        return storage.Client(credentials=self.credentials)

    def _get_discovery_engine_client(self, location="global"):
        client_options = ClientOptions(
            api_endpoint=f"{location}-discoveryengine.googleapis.com") if location != "global" else None
        return discoveryengine_v1.DataStoreServiceClient(client_options=client_options)

    def _get_list_project_id(self, serviceName='cloudresourcemanager', version='v1', status='ACTIVE'):
        credentials = service_account.Credentials.from_service_account_file(
            self.credentials_path,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )
        service = build(serviceName, version, credentials=credentials)

        request = service.projects().list()
        response = request.execute()

        if response.get('projects', []):
            active_projects = [
                project for project in response.get('projects', [])
                if project.get('lifecycleState') == status
            ]
            # for project in active_projects:
            #     project_id = project.get('projectId')
            #     if project_id:
            #         project['regions'] = self.get_regions(project_id)
            return active_projects
        else:
            return []

    # def get_regions(self, project_id):
    #     compute_service = build('compute', 'v1', credentials=self.credentials)
    #     regions_request = compute_service.regions().list(project=project_id)
    #     regions_response = regions_request.execute()
    #
    #     if regions_response.get('items', []):
    #         return [region['name'] for region in regions_response['items']]
    #     return []

    async def upload_to_s3(self, file, bucket_name, upload_folder):
        s3_client = boto3.client('s3')
        try:
            try:
                s3_client.head_bucket(Bucket=bucket_name)
            except ClientError:
                print(f"Bucket {bucket_name} không tồn tại. Đang tạo bucket...")
                s3_client.create_bucket(Bucket=bucket_name)

            s3_file_path = f"{upload_folder}/{file.filename}"
            s3_url = f"s3://{bucket_name}/{s3_file_path}"

            await file.seek(0)
            s3_client.upload_fileobj(file.file, bucket_name, s3_file_path)
            # print(f"Đã upload file {file.filename} lên {s3_url}")

            return s3_url
        except Exception as e:
            raise Exception(f"Không thể upload file lên S3: {str(e)}")

    async def upload_to_gcs(self, file, bucket_name, upload_folder):
        try:
            bucket = self.storage_client.get_bucket(bucket_name)
            gcs_file_path = f"{upload_folder}/{file.filename}"
            blob = bucket.blob(gcs_file_path)

            await file.seek(0)
            blob.upload_from_file(file.file, timeout=2 * 60 * 60)
            gcs_url = f"gs://{bucket_name}/{gcs_file_path}"
            # print(f"Đã upload file {file.filename} lên {gcs_url}")
            return gcs_url
        except:
            print(f"[ERROR UPLOAD TO GCS]: {traceback.format_exc()}")
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Không thể upload file lên GCS"
            )

    # TODO-MdM: Implement the following method
    async def _create_data_store_AB(self, path, client_docs, project_id, location, data_store_id, company_id):
        try:
            await self._process_create_AB(path, client_docs, project_id, location, data_store_id, company_id)
        except Exception as e:
            print(f"Error: {e}")

    async def _process_create_AB(self, path, client_docs, project_id, location, data_store_id, company_id):
        try:
            parent_docs = client_docs.branch_path(
                project=project_id,
                location=location,
                data_store=data_store_id,
                branch="default_branch",
            )

            # gcs_folder_path = path + f"COMPANY_{company_id}/"
            gcs_folder_path = path

            async def list_files_in_gcs_folder(bucket_name: str, prefix: str) -> list:
                """Asynchronously list all files in a GCS folder."""

                def fetch_blobs():
                    storage_client = storage.Client()
                    bucket = storage_client.bucket(bucket_name)
                    blobs = bucket.list_blobs(prefix=prefix)

                    file_uris = []
                    while True:
                        for blob in blobs:
                            if not blob.name.endswith('/'):
                                file_uris.append(f"gs://{bucket_name}/{blob.name}")

                        if blobs.next_page_token:
                            blobs = bucket.list_blobs(prefix=prefix, page_token=blobs.next_page_token)
                        else:
                            break
                    return file_uris

                return await asyncio.to_thread(fetch_blobs)

            bucket_name, prefix = gcs_folder_path.split("/", 1)
            gcs_uris = await list_files_in_gcs_folder(bucket_name, prefix)

            if not gcs_uris:
                raise ValueError(f"No files found in GCS folder: gs://{bucket_name}/{prefix}")

            gcs_uri_chunks = self.chunk_uris(gcs_uris)

            async def import_documents_for_chunk(chunk):
                request_docs = discoveryengine_v1beta.ImportDocumentsRequest(
                    parent=parent_docs,
                    gcs_source=discoveryengine_v1beta.GcsSource(
                        input_uris=chunk,
                        data_schema="content",
                    ),
                    reconciliation_mode=discoveryengine_v1beta.ImportDocumentsRequest.ReconciliationMode.INCREMENTAL,
                )

                operation_docs = await asyncio.to_thread(client_docs.import_documents, request_docs)
                print(f"Waiting for operation to complete: {operation_docs.operation.name}")

                response_docs = await asyncio.to_thread(operation_docs.result)
                metadata_docs = discoveryengine_v1beta.ImportDocumentsMetadata(operation_docs.metadata)
                print(response_docs)
                print(metadata_docs)

            tasks = []
            for chunk in gcs_uri_chunks:
                tasks.append(import_documents_for_chunk(chunk))

            await asyncio.gather(*tasks)
        except Exception as e:
            print(f"Error: {e}")

    # TODO-MdM: Implement the following method
    async def _create_data_store_AB_trigger(self, project_id, location, collection, data_store_id, path):
        try:
            await self._process_create_AB_trigger(project_id, location, collection, data_store_id, path)
        except Exception as e:
            print(f"Error: {e}")

    async def _process_create_AB_trigger(self, project_id, location, collection, data_store_id, path):
        try:
            bucket_name, prefix = self.parse_bucket_and_prefix(path)
            storage_client = storage.Client()
            bucket = storage_client.bucket(bucket_name)
            blobs = await asyncio.to_thread(lambda: list(bucket.list_blobs(prefix=prefix)))
            all_files = [blob for blob in blobs if not blob.name.endswith("/")]
            if not all_files:
                raise Exception("ERROR")

            ndjson_lines = []

            for blob in all_files:
                file_name_lower = blob.name.lower()

                with tempfile.NamedTemporaryFile(suffix="", delete=False) as tmp_file:
                    local_file_path = tmp_file.name

                await asyncio.to_thread(blob.download_to_filename, local_file_path)

                text_content = ""
                try:
                    if file_name_lower.endswith(".pdf"):
                        def extract_pdf_text():
                            text = ""
                            with fitz.open(local_file_path) as doc:
                                for page in doc:
                                    text += page.get_text()
                            return text

                        text_content = await asyncio.to_thread(extract_pdf_text)

                    elif file_name_lower.endswith(".docx"):
                        def extract_docx_text():
                            doc_result = docx2python.docx2python(local_file_path)
                            return "\n".join(
                                "\n".join(page) for section in doc_result.body for page in section
                            )

                        text_content = await asyncio.to_thread(extract_docx_text)

                    elif file_name_lower.endswith(".txt"):
                        def read_txt():
                            with open(local_file_path, "r", encoding="utf-8", errors="ignore") as f:
                                return f.read()

                        text_content = await asyncio.to_thread(read_txt)

                    else:
                        text_content = f"(Unsupported format: {blob.name})"

                except Exception as e:
                    print(f"Error extracting text from {blob.name}: {e}")
                    text_content = f"(Failed to parse {blob.name})"

                long_name = blob.name
                doc_id = self.md5_hex_id(long_name)
                doc_json = {
                    "id": doc_id,
                    "content": {
                        "uri": f"gs://{bucket_name}/{blob.name}",
                        "mimeType": "application/pdf"
                    },
                    "structData": {
                        "title": "MUSASHINO",
                        "uri": f"gs://{bucket_name}/{blob.name}",
                    }
                }
                ndjson_line = json.dumps(doc_json, ensure_ascii=False)
                ndjson_lines.append(ndjson_line)

                await asyncio.to_thread(os.remove, local_file_path)

            prefix_clean = prefix.strip("/").replace("/", "_")
            ndjson_filename = f"{prefix_clean}_documents.ndjson"
            local_ndjson_path = os.path.join(tempfile.gettempdir(), ndjson_filename)

            def write_ndjson():
                with open(local_ndjson_path, "w", encoding="utf-8") as f:
                    for line in ndjson_lines:
                        f.write(line + "\n")

            await asyncio.to_thread(write_ndjson)

            ndjson_blob_name = f"{prefix}docs.ndjson"
            ndjson_blob = bucket.blob(ndjson_blob_name)
            await asyncio.to_thread(ndjson_blob.upload_from_filename, local_ndjson_path)
            print(f"Uploaded NDJSON to gs://{bucket_name}/{ndjson_blob_name}")

            client = discoveryengine.DocumentServiceClient()
            parent = (
                f"projects/{project_id}/locations/{location}/collections/{collection}"
                f"/dataStores/{data_store_id}/branches/default_branch"
            )

            print(f"Trigger Import data from GCS to Datastore: {data_store_id}")

            input_uris = [f"gs://{bucket_name}/{ndjson_blob_name}"]
            import_request = discoveryengine.ImportDocumentsRequest(
                parent=parent,
                gcs_source=discoveryengine.GcsSource(
                    input_uris=input_uris,
                    data_schema="document"
                )
            )
            operation = client.import_documents(request=import_request)
            print(f"Deleted NDJSON gs://{bucket_name}/{ndjson_blob_name}")
            await asyncio.to_thread(operation.result)
            await asyncio.to_thread(os.remove, local_ndjson_path)
            ndjson_blob.delete()
        except Exception as e:
            print(f"Error: {e}")

    async def _import_document(self, project_id, location, collection, data_store_id, path, import_mode):
        try:
            bucket_name, prefix = self.parse_bucket_and_prefix(path)
            storage_client = storage.Client()
            bucket = storage_client.bucket(bucket_name)

            # blobs = await asyncio.to_thread(lambda: list(bucket.list_blobs(prefix=prefix)))
            # all_files = [blob for blob in blobs if not blob.name.endswith("/")]
            def is_known_file_extension(name: str):
                known_extensions = ['.pdf', '.docx', '.xlsx', '.csv', '.txt', '.json']
                return any(name.endswith(ext) for ext in known_extensions)

            last_part = prefix.rstrip("/").split("/")[-1]
            if prefix.endswith("/") and "." in last_part and is_known_file_extension(last_part):
                prefix = prefix.rstrip("/")

            if prefix and not prefix.endswith("/"):
                blob = bucket.blob(prefix)
                if not await asyncio.to_thread(blob.exists):
                    raise Exception(f"File {prefix} does not exist in bucket {bucket_name}")
                all_files = [blob]
            else:
                blobs = await asyncio.to_thread(lambda: list(bucket.list_blobs(prefix=prefix)))
                all_files = [blob for blob in blobs if not blob.name.endswith("/")]
            if not all_files:
                raise Exception("No valid files found in the specified GCS path.")

            ndjson_lines = []
            for blob in all_files:
                file_name_lower = blob.name.lower()
                with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                    local_file_path = tmp_file.name

                asyncio.to_thread(blob.download_to_filename, local_file_path)

                text_content = ""
                doc_id = self.md5_hex_id(blob.name)
                mime_type = self.get_mime_type(file_name_lower)
                doc_json = {
                    "id": doc_id,
                    "content": {
                        "uri": f"gs://{bucket_name}/{blob.name}",
                        "mimeType": mime_type
                    },
                    "structData": {
                        "title": "Imported Document",
                        "uri": f"gs://{bucket_name}/{blob.name}",
                        "text": text_content[:1000]  # Optional preview
                    }
                }
                ndjson_lines.append(json.dumps(doc_json, ensure_ascii=False))
                await asyncio.to_thread(os.remove, local_file_path)

            ndjson_filename = f"{prefix.strip('/').replace('/', '_')}_docs.ndjson"
            local_ndjson_path = os.path.join(tempfile.gettempdir(), ndjson_filename)

            def write_ndjson():
                with open(local_ndjson_path, "w", encoding="utf-8") as f:
                    for line in ndjson_lines:
                        f.write(line + "\n")

            await asyncio.to_thread(write_ndjson)

            ndjson_blob_name = f"{prefix}import_docs.ndjson"
            ndjson_blob = bucket.blob(ndjson_blob_name)
            await asyncio.to_thread(ndjson_blob.upload_from_filename, local_ndjson_path)

            client = discoveryengine.DocumentServiceClient()
            parent = (
                f"projects/{project_id}/locations/{location}/collections/{collection}"
                f"/dataStores/{data_store_id}/branches/default_branch"
            )

            input_uris = [f"gs://{bucket_name}/{ndjson_blob_name}"]
            import_request = discoveryengine.ImportDocumentsRequest(
                parent=parent,
                gcs_source=discoveryengine.GcsSource(
                    input_uris=input_uris,
                    data_schema="document"
                ),
                reconciliation_mode=(
                    discoveryengine.ImportDocumentsRequest.ReconciliationMode.INCREMENTAL
                    if import_mode == "incremental"
                    else discoveryengine.ImportDocumentsRequest.ReconciliationMode.FULL
                )
            )

            operation = client.import_documents(request=import_request)
            await asyncio.to_thread(operation.result)

            await asyncio.to_thread(os.remove, local_ndjson_path)
            ndjson_blob.delete()

            return {
                "message": f"Successfully imported {len(ndjson_lines)} documents to datastore {data_store_id}"}

        except Exception as e:
            print(traceback.format_exc())

    def get_mime_type(self, file_name: str) -> str:
        if file_name.endswith(".pdf"):
            return "application/pdf"
        elif file_name.endswith(".docx"):
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif file_name.endswith(".txt"):
            return "text/plain"
        elif file_name.endswith(".csv"):
            return "text/plain"
        elif file_name.endswith(".xlsx"):
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        else:
            return "application/octet-stream"

    def parse_bucket_and_prefix(self, path: str):
        path = path.strip()
        if path.endswith("/"):
            path = path[:-1]
        parts = path.split("/", 1)
        bucket_name = parts[0]
        prefix = ""
        if len(parts) > 1:
            prefix = parts[1] + "/"
        return bucket_name, prefix

    def md5_hex_id(self, long_name: str) -> str:
        # Tạo hash MD5 từ tên file
        hash_obj = hashlib.md5(long_name.encode("utf-8"))
        return hash_obj.hexdigest()

    def generate_signed_url(self, gs_uri: str,
                            expiration_seconds: int = int(os.getenv('MAX_AGENT_BUILDERS', 1000))) -> str:
        if not gs_uri.startswith("gs://"):
            return None

        bucket_name, *object_path = gs_uri.replace("gs://", "").split("/", 1)
        object_name = object_path[0] if object_path else ""

        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(object_name)

        response_disposition = "inline"

        url = blob.generate_signed_url(
            version="v4",
            expiration=timedelta(seconds=expiration_seconds),
            method="GET",
            response_disposition=response_disposition,
        )
        return url

    def chunk_uris(self, uris, chunk_size=100):
        return [uris[i:i + chunk_size] for i in range(0, len(uris), chunk_size)]

    def fetch_projects_from_gcp(self) -> List[dict]:
        credentials = service_account.Credentials.from_service_account_file(
            self.credentials_path,
            scopes=["https://www.googleapis.com/auth/cloud-platform"]
        )

        service = build("cloudresourcemanager", "v1", credentials=credentials)
        request = service.projects().list()
        projects = []

        while request is not None:
            response = request.execute()
            for project in response.get("projects", []):
                projects.append({
                    "project_id": project.get("projectId"),
                    "project_number": project.get("projectNumber"),
                    "project_name": project.get("name"),
                })
            request = service.projects().list_next(previous_request=request, previous_response=response)

        return projects

    def get_discovery_usage(self, project_id: str, location: str = "global") -> dict:
        credentials = service_account.Credentials.from_service_account_file(
            self.credentials_path,
            scopes=["https://www.googleapis.com/auth/cloud-platform"]
        )
        service = build("discoveryengine", "v1beta", credentials=credentials)
        result = {"project_id": project_id}

        try:
            parent = f"projects/{project_id}/locations/{location}/collections/default_collection"

            # Count Datastores
            ds_response = service.projects().locations().collections().dataStores().list(parent=parent).execute()
            result["datastores"] = len(ds_response.get("dataStores", []))

            # Count Engines
            eng_response = service.projects().locations().collections().engines().list(parent=parent).execute()
            result["engines"] = len(eng_response.get("engines", []))

        except Exception as e:
            if e.args == 403:
                print(f"🚫 Permission denied for {project_id}")
                return None
            else:
                raise

        return result

    async def copy_gcs_folder_to_internal(self, bucket: str, dest_bucket: str, relative_path: str, dest_project_id: str,
                                          dest_path_override: str = None) -> str:
        location = 'global'
        source_bucket_path = bucket

        # Parse bucket + prefix từ SOURCE_BUCKET_PREFIX
        # source_bucket_path = source_bucket_prefix[5:]  # remove 'gs://'
        if "/" in source_bucket_path:
            source_bucket, root_prefix = source_bucket_path.split("/", 1)
            source_prefix = f"{root_prefix.rstrip('/')}/{relative_path.strip('/')}"
        else:
            source_bucket = source_bucket_path
            source_prefix = relative_path.strip("/")

        is_file = any(source_prefix.lower().endswith(ext) for ext in
                      [".pdf", ".docx", ".doc", ".txt", ".xls", ".xlsx", ".csv", ".ppt", ".pptx", ".jpg", ".jpeg",
                       ".png"])

        if not is_file and not source_prefix.endswith("/"):
            source_prefix += "/"
        self.ensure_internal_bucket_exists(bucket, dest_project_id, location)

        target_prefix = dest_path_override.strip("/") if dest_path_override else source_prefix

        storage_client = storage.Client()
        source_bucket_obj = storage_client.bucket(source_bucket)
        dest_bucket_obj = storage_client.bucket(dest_bucket)
        # source_prefix = source_prefix.rstrip("/") + "/"
        # blobs = source_bucket_obj.list_blobs(prefix=source_prefix)
        # print(source_prefix)
        print(f"[DEBUG] source_bucket: {source_bucket}")
        print(f"[DEBUG] source_prefix: {source_prefix}")
        blobs = await asyncio.to_thread(lambda: list(source_bucket_obj.list_blobs(prefix=source_prefix)))
        print(f"[DEBUG] Found {len(blobs)} blobs under prefix {source_prefix}")

        async def copy_blob(blob):
            relative_blob_path = blob.name[len(source_prefix):].lstrip("/")
            target_blob_name = f"{target_prefix}/{relative_blob_path}" if relative_blob_path else target_prefix
            source_uri = f"gs://{source_bucket}/{blob.name}"
            target_uri = f"gs://{bucket}/{target_blob_name}"

            print(f"Copying {source_uri} → {target_uri}")
            await asyncio.to_thread(source_bucket_obj.copy_blob, blob, dest_bucket_obj, new_name=target_blob_name)

        tasks = [copy_blob(blob) for blob in blobs if not blob.name.endswith("/")]
        await asyncio.gather(*tasks)
        print(f"Done copy to gs://{bucket}/{target_prefix}")
        return f"{dest_bucket}/{target_prefix}"

    def ensure_internal_bucket_exists(self, bucket_name: str, project_id: str, location: str):
        """
        Check if the bucket already exists, if not create a new one.
        """
        client = storage.Client(project=project_id)
        bucket = client.lookup_bucket(bucket_name)
        if bucket:
            print(f"Bucket '{bucket_name}' đã tồn tại.")
            return bucket_name

        print(f"Bucket '{bucket_name}' chưa tồn tại, tiến hành tạo...")
        bucket = client.bucket(bucket_name)
        bucket.storage_class = "STANDARD"
        client.create_bucket(bucket, location=location)
        print(f"Đã tạo bucket '{bucket_name}' tại {location}.")
        return bucket_name

    def _list_model_ai(self, search=None, gcp_api_key=None, openai_api_key=None, ai_service=None):
        filtered_models = []
        if ai_service.lower() == "google":
            headers = {
                "x-goog-api-key": gcp_api_key
            }
            with httpx.Client() as client:
                response = client.get('https://generativelanguage.googleapis.com/v1beta/models', headers=headers)
                if response.status_code != 200:
                    return {"error": response.text, "status_code": response.status_code}

                data = response.json()
                all_models = data.get("models", [])

                filtered_models = []
                for model in all_models:
                    raw_name = model.get("name", "")
                    short_name = raw_name.replace("models/", "")
                    display_name = model.get("displayName", "")

                    if "generateContent" not in model.get("supportedGenerationMethods", []):
                        continue
                    if search:
                        if search.lower() in short_name.lower() or search.lower() in display_name.lower():
                            model["name"] = short_name
                            filtered_models.append(model)
                    else:
                        model["name"] = short_name
                        filtered_models.append(model)
        elif ai_service.lower() == "openai":
            headers = {
                "Authorization": f"Bearer {openai_api_key}"
            }
            with httpx.Client() as client:
                response = client.get('https://api.openai.com/v1/models', headers=headers)
                if response.status_code != 200:
                    return {"error": response.text, "status_code": response.status_code}

                data = response.json()
                all_models = data.get("data", [])

                for model in all_models:
                    model_id = model.get("id", "")
                    if not model_id.startswith("gpt-"):
                        continue
                    if not search or search.lower() in model_id.lower():
                        filtered_models.append({
                            "name": model_id,
                            "displayName": self._format_display_name(model_id),
                            "description": "",
                            "version": "",
                            "inputTokenLimit": None,
                            "outputTokenLimit": None,
                            "supportedGenerationMethods": [],
                            "temperature": None,
                            "topP": None,
                            "topK": None,
                            "maxTemperature": None
                        })
        return filtered_models

    def _format_display_name(self, model_id):
        name = re.sub(r"[-_]", " ", model_id)
        return name.title()
