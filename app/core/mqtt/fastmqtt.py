import asyncio
import logging
import uuid
from ssl import SSLContext
from itertools import zip_longest
from typing import Any, Callable, Dict, List, Tuple, Optional, Union
from pydantic import BaseModel, ConfigDict, Field
from gmqtt import Client as MQTTClient
from gmqtt import Message, Subscription
from gmqtt.mqtt.constants import MQTTv50

from .handlers import (
    MQTTConnectionHandler,
    MQTTDisconnectHandler,
    MQTTHandlers,
    MQTTMessageHandler,
    MQTTSubscriptionHandler,
)


class MQTTConfig(BaseModel):

    host: str = "localhost"
    port: int = 1883
    ssl: Union[bool, SSLContext] = False
    keepalive: int = 60
    username: Optional[str] = None
    password: Optional[str] = None
    version: int = Field(default=MQTTv50, ge=4, le=5)
    reconnect_retries: Optional[int] = 1
    reconnect_delay: Optional[int] = 6
    will_message_topic: Optional[str] = None
    will_message_payload: Optional[str] = None
    will_delay_interval: Optional[int] = None
    model_config = ConfigDict(arbitrary_types_allowed=True)


class RagMQTT:
    def __init__(
        self,
        config: MQTTConfig,
        *,
        client_id: Optional[str] = None,
        clean_session: bool = True,
        optimistic_acknowledgement: bool = True,
        mqtt_logger: Optional[logging.Logger] = None,
        **kwargs: Any,
    ) -> None:
        if not client_id:
            client_id = uuid.uuid4().hex

        self.client: MQTTClient = MQTTClient(client_id, **kwargs)
        self.config: MQTTConfig = config

        self.client._clean_session = clean_session
        self.client._username = config.username
        self.client._password = config.password
        self.client._host = config.host
        self.client._port = config.port
        self.client._keepalive = config.keepalive
        self.client._ssl = config.ssl
        self.client.optimistic_acknowledgement = optimistic_acknowledgement
        self.client._connect_properties = kwargs
        self.client.on_message = self.__on_message
        self.client.on_connect = self.__on_connect
        self.subscriptions: Dict[str, Tuple[Subscription, List[MQTTMessageHandler]]] = {}
        self._logger = mqtt_logger
        self.mqtt_handlers = MQTTHandlers(self.client, self._logger)

        if (
            self.config.will_message_topic
            and self.config.will_message_payload
            and self.config.will_delay_interval
        ):
            self.client._will_message = Message(
                self.config.will_message_topic,
                self.config.will_message_payload,
                will_delay_interval=self.config.will_delay_interval,
            )
            self._logger.debug(
                "WILL MESSAGE INITIALIZED: "
                "topic -> %s\n payload -> %s\n will_delay_interval -> %s",
                self.config.will_message_topic,
                self.config.will_message_payload,
                self.config.will_delay_interval,
            )

    @staticmethod
    def match(topic: str, template: str) -> bool:
        if str(template).startswith("$share/"):
            template = template.split("/", 2)[2]

        topic_parts = topic.split("/")
        template_parts = template.split("/")

        for topic_part, part in zip_longest(topic_parts, template_parts):
            if part == "#" and not str(topic_part).startswith("$"):
                return True
            elif (topic_part is None or part not in {"+", topic_part}) or (
                part == "+" and topic_part.startswith("$")
            ):
                return False
            continue

        return len(template_parts) == len(topic_parts)

    async def connection(self) -> None:
        if self.client._username:
            self.client.set_auth_credentials(self.client._username, self.client._password)
            self._logger.debug("user is authenticated")

        await self.__set_connetion_config()

        version = self.config.version or MQTTv50
        self._logger.info("Used broker version is %s", version)

        await self.client.connect(
            self.client._host,
            self.client._port,
            self.client._ssl,
            self.client._keepalive,
            version,
        )
        self._logger.debug("Connected to broker")

    async def __set_connetion_config(self) -> None:
        self.client.set_config(
            {
                "reconnect_retries": self.config.reconnect_retries,
                "reconnect_delay": self.config.reconnect_delay,
            }
        )

    def __on_connect(self, client: MQTTClient, flags: int, rc: int, properties: Any) -> None:
        if self.mqtt_handlers.user_connect_handler is not None:
            self.mqtt_handlers.user_connect_handler(client, flags, rc, properties)

        for topic in self.subscriptions:
            self._logger.debug("Subscribing for %s", topic)
            self.client.subscribe(self.subscriptions[topic][0])

    async def __on_message(
        self, client: MQTTClient, topic: str, payload: bytes, qos: int, properties: Any
    ) -> Any:
        gather = []
        if self.mqtt_handlers.user_message_handler is not None:
            self._logger.debug("Calling user_message_handler")
            gather.append(
                self.mqtt_handlers.user_message_handler(client, topic, payload, qos, properties)
            )

        for topic_template in self.subscriptions:
            if self.match(topic, topic_template):
                self._logger.debug("Calling specific handler for topic %s", topic)
                for handler in self.subscriptions[topic_template][1]:
                    gather.append(handler(client, topic, payload, qos, properties))

        return await asyncio.gather(*gather)

    def publish(
        self,
        message_or_topic: str,
        payload: Any = None,
        qos: int = 0,
        retain: bool = False,
        **kwargs,
    ) -> None:
        return self.client.publish(
            message_or_topic, payload=payload, qos=qos, retain=retain, **kwargs
        )

    def unsubscribe(self, topic: str, **kwargs):
        self._logger.debug("unsubscribe")
        if topic in self.subscriptions:
            del self.subscriptions[topic]

        return self.client.unsubscribe(topic, **kwargs)

    async def mqtt_startup(self) -> None:
        """Initial connection for MQTT client, for lifespan startup."""
        await self.connection()

    async def mqtt_shutdown(self) -> None:
        """Final disconnection for MQTT client, for lifespan shutdown."""
        await self.client.disconnect()

    def init_app(self, fastapi_app) -> None:  # pragma: no cover
        """Add startup and shutdown event handlers for app without lifespan."""

        @fastapi_app.on_event("startup")
        async def startup() -> None:
            await self.mqtt_startup()

        @fastapi_app.on_event("shutdown")
        async def shutdown() -> None:
            await self.mqtt_shutdown()

    def subscribe(
        self,
        *topics,
        qos: int = 0,
        no_local: bool = False,
        retain_as_published: bool = False,
        retain_handling_options: int = 0,
        subscription_identifier: Any = None,
    ) -> Callable[..., Any]:

        def subscribe_handler(handler: MQTTMessageHandler) -> MQTTMessageHandler:
            self._logger.debug("Subscribe for topics: %s", topics)
            for topic in topics:
                if topic not in self.subscriptions:
                    subscription = Subscription(
                        topic,
                        qos,
                        no_local,
                        retain_as_published,
                        retain_handling_options,
                        subscription_identifier,
                    )
                    self.subscriptions[topic] = (subscription, [handler])
                else:
                    # Use the most restrictive field of the same subscription
                    old_subscription = self.subscriptions[topic][0]
                    new_subscription = Subscription(
                        topic,
                        max(qos, old_subscription.qos),
                        no_local or old_subscription.no_local,
                        retain_as_published or old_subscription.retain_as_published,
                        max(
                            retain_handling_options,
                            old_subscription.retain_handling_options,
                        ),
                        old_subscription.subscription_identifier or subscription_identifier,
                    )
                    self.subscriptions[topic] = (
                        new_subscription,
                        self.subscriptions[topic][1],
                    )
                    self.subscriptions[topic][1].append(handler)
            return handler

        return subscribe_handler

    def on_connect(self) -> Callable[..., Any]:
        def connect_handler(handler: MQTTConnectionHandler) -> MQTTConnectionHandler:
            self._logger.debug("handler accepted")
            return self.mqtt_handlers.on_connect(handler)

        return connect_handler

    def on_message(self) -> Callable[..., Any]:
        def message_handler(handler: MQTTMessageHandler) -> MQTTMessageHandler:
            self._logger.debug("on_message handler accepted")
            return self.mqtt_handlers.on_message(handler)

        return message_handler

    def on_disconnect(self) -> Callable[..., Any]:
        def disconnect_handler(handler: MQTTDisconnectHandler) -> MQTTDisconnectHandler:
            self._logger.debug("on_disconnect handler accepted")
            return self.mqtt_handlers.on_disconnect(handler)

        return disconnect_handler

    def on_subscribe(self) -> Callable[..., Any]:
        def subscribe_handler(handler: MQTTSubscriptionHandler) -> MQTTSubscriptionHandler:
            self._logger.debug("on_subscribe handler accepted")
            return self.mqtt_handlers.on_subscribe(handler)

        return subscribe_handler