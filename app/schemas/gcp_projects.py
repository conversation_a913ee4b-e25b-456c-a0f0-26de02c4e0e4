from datetime import datetime
from pydantic import BaseModel, constr, Field
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr


class GcpProjectReps(BaseModel):
    id: Optional[int]
    project_id: str = Field(None, example="musashino-rag-dev-001")
    project_number: Optional[str] = Field(None, example="1042137654321")
    project_name: Optional[str] = Field(None, example="MRAG Dev Project")
    bucket: Optional[str] = Field(None, example="musashino")
    description: Optional[str] = Field(None, example="Project for MRAG dev environment")

    datastore_quota: Optional[int] = Field(500, ge=0)
    datastore_usage_on_gcp: Optional[int] = Field(0)
    datastore_usage: Optional[int] = Field(0, ge=0)
    current_datastore_usage_percentage: Optional[float] = Field(0, ge=0)
    agent_builder_quota: Optional[int] = Field(100, ge=0)
    agent_builder_usage_on_gcp: Optional[int] = Field(0)
    agent_builder_usage: Optional[int] = Field(0, ge=0)
    current_agent_builder_percentage: Optional[float] = Field(0, ge=0)
    in_use: Optional[bool] = Field(False)
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class GcpProjectCreate(BaseModel):
    project_id: str
    project_number: Optional[str]
    project_name: Optional[str]
    bucket: Optional[str]
    description: Optional[str]
    datastore_quota: Optional[int]
    agent_builder_quota: Optional[int]
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class GcpProjectUpdate(BaseModel):
    project_id: str
    project_number: Optional[str]
    project_name: Optional[str]
    bucket: Optional[str]
    description: Optional[str]
    datastore_quota: Optional[int]
    agent_builder_quota: Optional[int]
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UpdateCurrentProject(BaseModel):
    current_project: str

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
