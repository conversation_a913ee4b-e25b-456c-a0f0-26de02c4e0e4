from typing import Optional
from pydantic import BaseModel, field_serializer
from datetime import datetime


class ConfigResp(BaseModel):
    id: int
    config_key: str
    config_value: str
    important: Optional[bool] = False
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ConfigCreateUpdate(BaseModel):
    config_key: str
    config_value: str
    important: Optional[bool] = False

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserChatTitleUpdate(BaseModel):
    userchat_title_jp: str
    userchat_title_en: str

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ConfigUserChatResp(BaseModel):
    id: int
    userchat_title_jp: str
    userchat_title_en: str
    company_id: Optional[int] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ModelKeyUpdate(BaseModel):
    service_name: str = "OPENAI_KEY"
    service_key: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ModelKeyResponse(BaseModel):
    id: int
    service_name: str
    service_key: Optional[str]
    company_id: Optional[int] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None

    @field_serializer('service_key')
    def mask_service_key(cls, value):
        if cls is None or cls == "":
            return cls
        return "****************************************"

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
