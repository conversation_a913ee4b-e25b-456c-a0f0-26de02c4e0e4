from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class ContractReps(BaseModel):
    id: int
    company_id: int
    package_id: int
    start_date: datetime
    end_date: datetime
    total_price: float
    status: str
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ContractCreate(BaseModel):
    company_id: int
    package_id: int
    start_date: datetime
    end_date: datetime
    total_price: float
    status: str
    is_active: bool = Field(True, description="", example=None)


class ContractUpdate(BaseModel):
    company_id: int
    package_id: int
    start_date: datetime
    end_date: datetime
    total_price: float
    status: str
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
