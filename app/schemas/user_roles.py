from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class UserRoleResp(BaseModel):
    id: int
    user_id: int
    role_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserRoleCreate(BaseModel):
    user_id: int
    role_id: int
    is_active: bool = Field(True, description="", example=None)


class UserRoleUpdate(BaseModel):
    user_id: int
    role_id: int
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
