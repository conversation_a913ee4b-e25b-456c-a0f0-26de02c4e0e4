from datetime import datetime, date
from typing import Optional
from pydantic import BaseModel, constr, Field
from app.schemas.service_packages import ServicePackagesResp


class NotificationsReps(BaseModel):
    id: int
    title: str
    content: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    level: Optional[int] = None
    company_id: Optional[int] = None
    type: Optional[int] = None
    is_expired: Optional[bool] = False
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class NotificationsCreate(BaseModel):
    title: str
    content: Optional[str] = None
    level: Optional[int] = None
    company_id: int
    type: Optional[int] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    is_active: bool = Field(True, description="", example=None)


class NotificationsUpdate(BaseModel):
    title: str
    content: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    type: Optional[int] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class NotificationFilter(BaseModel):
    type: Optional[int] = None
    expired: Optional[bool] = False
    is_active: Optional[str] = None
    is_deleted: Optional[bool] = False
