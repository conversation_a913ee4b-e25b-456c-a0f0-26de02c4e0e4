from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class PermissionReps(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    endpoint: Optional[str] = None
    grant_type: Optional[str] = None
    component: Optional[str] = None
    position: Optional[int] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class PermissionCreate(BaseModel):
    name: str
    description: Optional[str] = None
    endpoint: Optional[str] = None
    grant_type: Optional[str] = None
    component: Optional[str] = None
    position: Optional[int] = None
    is_active: bool = Field(True, description="", example=None)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class PermissionUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    endpoint: Optional[str] = None
    grant_type: Optional[str] = None
    component: Optional[str] = None
    position: Optional[int] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class PermissionFilter(BaseModel):
    is_active: Optional[str] = None
    is_deleted: Optional[bool] = False
