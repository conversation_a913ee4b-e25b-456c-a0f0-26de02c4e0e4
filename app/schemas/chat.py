import uuid
import re
from typing import Optional, List, Any, ClassVar
from pydantic import BaseModel, Field
from langchain_core.documents.base import Document
from app.utils.common import generate_id_code


class DocumentEntry(BaseModel):
    source: str
    document: List[Document]


class LLMInvokeResponse(BaseModel):
    input: str
    answer: str
    service_type: str = "rag"
    ai_service: str | None = None
    chat_history: List[Any] = Field(default_factory=list)


class ChatReq(BaseModel):
    title: Optional[str] = None
    chatroom_code: Optional[str] = Field(default_factory=generate_id_code)
    chat_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    message_id: ClassVar[str] = str(uuid.uuid4())
    documents: Optional[List[DocumentEntry]] = Field(default_factory=list)
    message: str
    use_case_id: int
    provider_id: Optional[int] = None
    enable_reasoning: Optional[bool] = None  # User's reasoning preference for this request

    def __init__(self, **data):
        super().__init__(**data)
        if self.title is None:
            self.title = " ".join(re.split(r"\s+", self.message)[:5])

        if self.chatroom_code is None:
            self.chatroom_code = generate_id_code()
        if self.session_id is None:
            self.session_id = str(uuid.uuid4())
        if self.chat_id is None:
            self.chat_id = str(uuid.uuid4())

    class Config:
        from_attributes = True
        protected_namespaces = ()
