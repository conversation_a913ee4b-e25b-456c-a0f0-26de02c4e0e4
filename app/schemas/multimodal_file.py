import os
import uuid
from pathlib import Path
from typing import Optional, ClassVar
from pydantic import BaseModel, Field, field_validator
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException


class MultimodalFile(BaseModel):
    file_name: Optional[str] = None
    content_type: Optional[str] = None
    object_name: Optional[str] = None
    upload_url: Optional[str] = None
    bucket_name: ClassVar[str] = os.getenv("MULTIMODAL_BUCKET_NAME")

    @property
    def file_extension(self) -> str:
        if not self.file_name:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY, detail="file_name is not set")
        extension = Path(self.file_name).suffix.lstrip(".").lower()
        if not extension:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY, detail="file_extension could not be determined")
        return extension

    def generate_object_name(self, chat_room_id: str) -> str:
        self.object_name = f"{chat_room_id}-{uuid.uuid4()}.{self.file_extension}"
        return self.object_name

    def gcs_path(self) -> str:
        if not self.object_name:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                               detail="object_name is not set")

        if not self.bucket_name:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                               detail="Bucket name is not set in environment")
        return f"gs://{self.bucket_name}/{self.object_name}"


class MultimodalFileUploadUrlReq(MultimodalFile):
    file_name: str = Field(..., regex=r"^.+\..+$", description="File name with extension.")
    content_type: str

    accepted_types: ClassVar[list[str]] = [
        "video/mp4",
        "audio/mpeg",
        "image/jpeg",
        "image/png",
        "application/pdf",
    ]

    @field_validator("content_type")
    def validate_content_type(cls, v: str) -> str:
        if v.lower() not in cls.accepted_types:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=f"Content type must be one of {cls.accepted_types}")
        return v


class MultimodalFileUploadUrlRes(MultimodalFile):
    object_name: str
    upload_url: str
