from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class GroupReps(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    group_config: Optional[dict] = None
    list_user_id: Optional[list[int]] = None
    list_usecase_id: Optional[list[int]] = None
    company_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class GroupCreate(BaseModel):
    name: str
    company_id: int
    description: Optional[str] = None
    group_config: Optional[dict] = None
    list_user_id: Optional[list[int]] = None
    list_usecase_id: Optional[list[int]] = None
    is_active: bool = Field(True, description="", example=None)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class GroupUpdate(BaseModel):
    company_id: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    group_config: Optional[dict] = None
    list_user_id: Optional[list[int]] = None
    list_usecase_id: Optional[list[int]] = None
    is_deleted: Optional[bool] = False
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True


class GroupRepsV2(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    group_config: Optional[dict] = None
    list_user_id: Optional[list[dict]] = None
    list_usecase_id: Optional[list[dict]] = None
    company_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
