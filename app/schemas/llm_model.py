from enum import Enum
from typing import List, Dict
from pydantic import BaseModel, <PERSON>
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException


class RetrievalEngineDataType(int, Enum):
    UNSTRUCTURED_DATA = 0
    STRUCTURED_DATA = 1
    WEBSITE_DATA = 2
    BLENDED_SEARCH = 3


class AiServices(str, Enum):
    GOOGLE = "Google"
    OPEN_AI = "OpenAI"
    CLAUDE = "Claude"
    AZURE = "Azure"
    GROK = "Grok"


class LlmModel(BaseModel):
    name: str = Field(..., description="The name of the language model")
    ai_service: AiServices = Field(..., description="The AI service providing the model")
    description: str = Field(..., description="Description of the model's capabilities")
    can_multimodal: bool = Field(False, description="Indicates if the model supports multimodal inputs")
    multimodal_extensions: List[str] = Field(default_factory=list, description="Supported multimodal file extensions")


def get_llm_model_by_name(ai_service_name: str, llm_model_name: str, models: Dict[AiServices, List[LlmModel]]) -> LlmModel:
    try:
        ai_service = AiServices(ai_service_name)
    except ValueError as e:
        available_services = [service.value for service in AiServices]
        message = f"Invalid AI service name: '{ai_service_name}'. Available options: {available_services}."
        raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY, detail=message) from e

    models_for_service = models.get(ai_service, [])
    for model in models_for_service:
        if model.name == llm_model_name:
            return model

    available_models = [model.name for model in models_for_service]
    message = f"Invalid model name: '{llm_model_name}'. Available options: {available_models}."
    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY, detail=message)


OPEN_AI_MULTIMODAL_EXTENSIONS = [".png", ".jpg", ".jpeg", ".webp", ".gif"]
GOOGLE_MULTIMODAL_EXTENSIONS = [
    ".png", ".jpg", ".jpeg", ".flv", ".mov", ".mpeg", ".mpegps", ".mpg", ".mp4",
    ".webm", ".wmv", ".3gp", ".3gpp", ".aac", ".flac", ".mp3", ".m4a", ".mpga",
    ".opus", ".pcm", ".wav", ".pdf"
]

llm_models: Dict[AiServices, List[LlmModel]] = {
    AiServices.OPEN_AI: [
        LlmModel(
            name="gpt-4o",
            ai_service=AiServices.OPEN_AI,
            description="Our flagship model with high intelligence for complex, multi-step tasks.",
            can_multimodal=True,
            multimodal_extensions=OPEN_AI_MULTIMODAL_EXTENSIONS,
        ),
        LlmModel(
            name="gpt-4o-mini",
            ai_service=AiServices.OPEN_AI,
            description="An affordable, efficient model designed for lightweight tasks.",
            can_multimodal=True,
            multimodal_extensions=OPEN_AI_MULTIMODAL_EXTENSIONS,
        ),
    ],
    AiServices.GOOGLE: [
        LlmModel(
            name="gemini-1.5-pro",
            ai_service=AiServices.GOOGLE,
            description="This multimodal model can handle images, audio, video, and PDF files in responses to text or chat prompts. "
                        "It supports understanding of long contexts up to the maximum input token limit.",
            can_multimodal=True,
            multimodal_extensions=GOOGLE_MULTIMODAL_EXTENSIONS,
        ),
        LlmModel(
            name="gemini-1.5-flash",
            ai_service=AiServices.GOOGLE,
            description="Optimized for applications requiring high throughput and cost-efficiency, this model offers speed "
                        "and efficiency for fast, cost-effective application building without sacrificing quality.",
            can_multimodal=True,
            multimodal_extensions=GOOGLE_MULTIMODAL_EXTENSIONS,
        ),
    ],
}
