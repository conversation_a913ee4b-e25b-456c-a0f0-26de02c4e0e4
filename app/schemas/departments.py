from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class DepartmentReps(BaseModel):
    id: int
    company_id: str
    department_name: str
    manager_id: str
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class DepartmentCreate(BaseModel):
    company_id: str
    department_name: str
    manager_id: str
    is_active: bool = Field(True, description="", example=None)


class DepartmentUpdate(BaseModel):
    company_id: str
    department_name: str
    manager_id: str
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
