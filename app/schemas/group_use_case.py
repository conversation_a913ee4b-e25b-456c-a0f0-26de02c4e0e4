from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class GroupUseCaseReps(BaseModel):
    id: int
    group_id: int
    use_case_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class CreateGroupUseCase(BaseModel):
    group_id: int
    use_case_id: int
    is_active: bool = Field(True, description="", example=None)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class UpdateGroupUseCase(BaseModel):
    group_id: int
    use_case_id: int
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
