from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class ResourceUsageReps(BaseModel):
    id: int
    resource_id: int
    company_id: int
    resource_type: str
    usage_amount: int
    usage_date: datetime
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ResourceUsageCreate(BaseModel):
    resource_id: int
    company_id: int
    resource_type: str
    usage_amount: int
    usage_date: datetime
    is_active: bool = Field(True, description="", example=None)


class ResourceUsageUpdate(BaseModel):
    resource_id: int
    company_id: int
    resource_type: str
    usage_amount: int
    usage_date: datetime
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
