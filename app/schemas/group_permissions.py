from datetime import datetime
from typing import Optional, Any
from pydantic import BaseModel, constr, Field


class GroupPermissionReps(BaseModel):
    id: int
    group_id: int
    permission_id: int
    grant_type: Optional[str] = None
    valid_from: Optional[Any] = None
    valid_to: Optional[Any] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class GroupPermissionCreate(BaseModel):
    group_id: int
    permission_id: int
    grant_type: Optional[str] = None
    is_active: bool = Field(True, description="", example=None)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class GroupPermissionUpdate(BaseModel):
    grant_type: Optional[str] = None
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
