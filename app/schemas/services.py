import re
from uuid import uuid4
from datetime import datetime
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from app.schemas.groups import GroupReps
from sqlalchemy.dialects.postgresql import JSONB


class CreateProvider(BaseModel):
    ai_service: str = Field(..., description="The AI service provided")
    service_name: Optional[str] = Field(None, description="Name of the service")
    model_name: Optional[str] = Field(None, description="Model name used in the service")
    llm_model_id: Optional[str] = Field(None, description="LLM model identifier")
    description: Optional[str] = Field(None, description="Description of the service")
    is_public: Optional[bool] = Field(False, description="Indicates if the service is public")

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UpdateProvider(BaseModel):
    ai_service: str = Field(..., description="The AI service provided")
    service_name: Optional[str] = Field(None, description="Name of the service")
    model_name: Optional[str] = Field(None, description="Model name used in the service")
    llm_model_id: Optional[str] = Field(None, description="LLM model identifier")
    description: Optional[str] = Field(None, description="Description of the service")
    is_active: Optional[bool] = True
    is_public: Optional[bool] = False

    class Config:
        from_attributes = True
        protected_namespaces = ()


class ProviderResp(BaseModel):
    id: int
    ai_service: str
    service_name: Optional[str]
    model_name: Optional[str]
    llm_model_id: Optional[str]
    description: Optional[str]
    is_public: Optional[bool] = False
    is_active: Optional[bool] = True
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class CreateDataSource(BaseModel):
    title: str = Field(..., description="Unique title of the data source")
    description: Optional[str] = Field(None, description="Description of the data source")
    provider_id: Optional[int] = Field(None, description="ID of the associated provider")
    retriever_type: Optional[str] = Field(None, description="Type of retriever used")
    ai_search_data_store_id: Optional[str] = Field(None, description="ID of the AI search data store")
    engine_compute_id: Optional[str] = Field(None, description="ID of the compute engine")
    engine_data_type: Optional[str] = Field(None, description="Data type of the engine")
    max_document: Optional[int] = Field(1, description="Maximum number of documents")
    max_extractive_answer_count: Optional[int] = Field(1, description="Maximum number of extractive answers")
    ai_service: Optional[str] = Field(None, description="AI service used")
    llms_model: Optional[str] = Field(None, description="LLM model used")
    chain_of_thought_prompt: Optional[bool] = Field(False, description="Chain of thought prompting")
    chain_prompt: Optional[str] = Field(None, description="Prompt for chain of thought")
    list_group: Optional[List[int]] = Field([], description="List of groups")

    class Config:
        from_attributes = True
        protected_namespaces = ()


class DataSourceResp(BaseModel):
    id: int
    title: str
    description: Optional[str]
    provider_id: Optional[int]
    retriever_type: Optional[str]
    ai_search_data_store_id: Optional[str]
    engine_compute_id: Optional[str]
    engine_data_type: Optional[str]
    max_document: Optional[int] = 1
    max_extractive_answer_count: Optional[int] = 1
    ai_service: Optional[str]
    llms_model: Optional[str]
    chain_of_thought_prompt: Optional[bool] = False
    chain_prompt: Optional[str] = False
    list_group: Optional[List[int]] = []
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class CreateUseCase(BaseModel):
    use_case_title: Optional[str] = Field(None, description="Title of the use case")
    ai_service: Optional[str] = Field(None, description="AI service related to the use case")
    use_case_private: Optional[bool] = Field(True, description="Indicates if the use case is private")
    provider_id: Optional[int] = Field(None, description="ID of the associated provider")
    project_id: Optional[str] = Field(None, description="ID of the associated project")
    location: Optional[str] = Field("global", description="Location of the use case")
    engine_id: Optional[str] = Field(None, description="ID of the engine")
    description: Optional[str] = Field(None, description="Description of the use case")
    can_multimodal: Optional[bool] = Field(True, description="Indicates if the use case can be multimodal")
    is_multimodal: Optional[bool] = Field(False, description="Indicates if the use case is multimodal")
    multimodal_extensions: Optional[Dict[str, Any]] = Field(None, description="Multimodal extension details")
    data_store_id: Optional[str] = Field(None, description="ID of the data store")
    max_documents: Optional[int] = Field(1, description="Maximum number of documents")
    max_extractive_answer_count: Optional[int] = Field(5, description="Maximum number of extractive answers")
    get_extractive_answers: Optional[bool] = Field(True, description="Indicates if extractive answers are enabled")
    engine_data_type: Optional[int] = Field(2, description="Data type of the engine")
    llms_model_name: Optional[str] = Field(..., description="Name of the LLM model")
    llms_model_version: Optional[str] = Field(..., description="Version of the LLM model")
    system_prompt: Optional[str] = Field(..., description="System prompt for the use case")
    chain_prompt: Optional[str] = Field(None, description="Prompt for chain of thought")
    answer_language_code: Optional[str] = Field("ja", description="Language code for the answers")
    chain_of_thought_prompt: Optional[bool] = Field(True, description="Chain of thought prompting")
    search_type: Optional[str] = Field(None, description="Type of search")
    retriever_type: Optional[str] = Field(None, description="Type of retriever used")
    result_count: Optional[int] = Field(None, description="Number of results")
    related_questions: Optional[bool] = Field(False, description="Indicates if related questions are enabled")
    snippets_or_extractive: Optional[bool] = Field(False,
                                                   description="Indicates if snippets or extractive answers are enabled")
    autocomplete_suggestions: Optional[bool] = Field(False,
                                                     description="Indicates if autocomplete suggestions are enabled")
    feedback: Optional[bool] = Field(False, description="Indicates if feedback is enabled")
    list_group_id: Optional[List[int]] = Field([], description="List of groups")
    human_prompt: Optional[str] = Field(None, description="Human prompt for the use case")
    format_output: Optional[str] = Field(None, description="Format of the output")
    use_ragsource: Optional[bool] = Field(True, description="Use Ragsource")
    priority: int = Field(1, description="Priority of the use case")
    is_public: Optional[bool] = Field(False, description="Indicates if the use case is public")
    list_provider_ids: Optional[List[int]]

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UseCaseResp(BaseModel):
    id: int
    use_case_title: Optional[str]
    use_case_code: Optional[str]
    use_case_private: Optional[bool] = True
    description: Optional[str]
    provider_id: Optional[int]
    project_id: Optional[str]
    location: Optional[str] = "global"
    engine_id: Optional[str]
    data_store_id: Optional[str]
    max_documents: Optional[int] = 1
    max_extractive_answer_count: Optional[int] = 5
    get_extractive_answers: Optional[bool] = True
    engine_data_type: Optional[int] = 2
    ai_service: Optional[str]
    llms_model_name: Optional[str]
    llms_model_version: Optional[str]
    system_prompt: Optional[str]
    chain_prompt: Optional[str]
    answer_language_code: Optional[str] = "ja"
    chain_of_thought_prompt: Optional[bool] = True
    search_type: Optional[str]
    can_multimodal: Optional[bool] = True
    is_multimodal: Optional[bool] = False
    multimodal_extensions: Optional[Dict[str, Any]]
    retriever_type: Optional[str]
    result_count: Optional[int]
    related_questions: Optional[bool] = False
    snippets_or_extractive: Optional[bool] = False
    autocomplete_suggestions: Optional[bool] = False
    feedback: Optional[bool] = False
    list_group_id: Optional[List[int]] = []
    is_active: Optional[bool] = None
    is_deleted: Optional[bool] = None
    use_ragsource: Optional[bool] = None
    priority: Optional[int] = None
    is_public: Optional[bool] = None
    list_provider_ids: Optional[List[int]]
    list_models: Optional[List[Dict[str, Any]]]
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    created_by_full_name: Optional[str] = None
    created_by_email: Optional[str] = None
    updated_by_full_name: Optional[str] = None
    updated_by_email: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class CreateChatRoom(BaseModel):
    title: str = Field(..., description="")
    chatroom_code: Optional[str] = Field(None, description="")
    user_id: int = Field(..., description="")
    use_case_id: Optional[str] = Field(None, description="")

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UpdateChatRoom(BaseModel):
    title: str = Field(..., description="")

    class Config:
        from_attributes = True
        protected_namespaces = ()


class ChatRoomResp(BaseModel):
    id: int
    title: str
    chatroom_code: Optional[str]
    user_id: int
    use_case_id: Optional[int]
    reasoning_enabled: Optional[bool] = None  # Added for reasoning toggle feature
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()

    # @validator("title", pre=True, always=True)
    # def set_title(cls, v, values):
    #     if v is None and values.get("user_message"):
    #         return " ".join(re.split(r"\s+", values["user_message"])[:20])
    #     return v


class ChatRoomFilters(BaseModel):
    use_case_title: Optional[str] = Field(None, description="ID Usecase")
    use_case_code: Optional[str] = Field(None, description="Code Usecase")
    service: Optional[str] = Field(None, description="Name Service")
    model: Optional[str] = Field(None, description="Name Model")
    reaction: Optional[str] = Field(None, description="Reaction Type")


class CreateChatHistory(BaseModel):
    user_id: int = Field(..., description="ID of the user")
    chatroom_id: int = Field(..., description="ID of the chatroom")
    message_id: str = Field(..., description="Unique identifier for the message")
    chat_id: str = Field(..., description="ID of the chat session")
    session_id: str = Field(..., description="Session ID")
    service_name: Optional[str] = Field(None, description="Name of the AI service, e.g., 'chatgpt', 'claude'")
    llm_model_id: Optional[int] = Field(None, description="ID of the LLM model")
    use_case_id: Optional[int] = Field(None, description="ID of the associated use case")
    user_message: Optional[str] = Field(None, description="Message from the user")
    ai_response: Optional[str] = Field(None, description="Response generated by the AI")
    language: Optional[str] = Field(None, description="Language of the conversation, e.g., 'ja', 'en'")
    sentiment: Optional[str] = Field(None, description="Detected sentiment, e.g., 'positive', 'neutral', 'negative'")
    input_length: Optional[int] = Field(None, description="Length of the user input message")
    output_length: Optional[int] = Field(None, description="Length of the AI response")
    input_tokens: Optional[int] = Field(None, description="Token count for the input message")
    output_tokens: Optional[int] = Field(None, description="Token count for the AI response")
    response_time_ms: Optional[int] = Field(None, description="Response time in milliseconds")
    is_resolved: Optional[bool] = Field(False, description="Indicates if the issue is resolved")
    source_info: Optional[str] = Field(None, description="Source information for the chat history")
    has_images: Optional[bool] = Field(False, description="Indicates if there are images in the message")
    has_files: Optional[bool] = Field(False, description="Indicates if there are files in the message")
    image_list: Optional[List[Dict[str, Any]]] = Field(None, description="List of images in JSON format")
    file_list: Optional[List[Dict[str, Any]]] = Field(None, description="List of files in JSON format")

    class Config:
        from_attributes = True

    @validator("message_id", "chat_id", "session_id", pre=True, always=True)
    def generate_uuid(cls, v):
        return v or str(uuid4())

    @validator("input_length", pre=True, always=True)
    def set_input_length(cls, v, values):
        if v is None and values.get("user_message"):
            return len(re.split(r"\s+", values["user_message"].strip()))
        return v


class ChatHistoryResp(BaseModel):
    id: int
    user_id: int
    chatroom_id: int
    message_id: str
    chat_id: str
    session_id: str
    service_name: Optional[str]
    llm_model_id: Optional[int]
    use_case_id: Optional[int]
    user_message: Optional[str]
    ai_response: Optional[str]
    language: Optional[str]
    sentiment: Optional[str]
    input_length: Optional[int]
    output_length: Optional[int]
    input_tokens: Optional[int]
    output_tokens: Optional[int]
    response_time_ms: Optional[int]
    is_resolved: Optional[bool] = False
    source_info: Optional[str]
    has_images: Optional[bool] = False
    has_files: Optional[bool] = False
    image_list: Optional[List[Dict[str, Any]]]
    file_list: Optional[List[Dict[str, Any]]]
    references: Optional[List[Dict[str, Any]]]
    reactions: Optional[str]
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class ListUseCase(BaseModel):
    ai_service: str
    llms_model: str

    class Config:
        from_attributes = True
        protected_namespaces = ()


class DatasourceFilter(BaseModel):
    is_active: Optional[bool] = True
    is_deleted: Optional[bool] = False


class UserGroupReps(BaseModel):
    id: int
    group_name: str
    list_use_case: Optional[List[UseCaseResp]] = []
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UseCaseV2Resp(BaseModel):
    list_use_case: Optional[List[UseCaseResp]] = []
    # list_use_case: Optional[List[UseCaseResp]] = []


class UpdateUseCase(BaseModel):
    use_case_title: Optional[str] = Field(None, description="Title of the use case")
    ai_service: Optional[str] = Field(None, description="AI service related to the use case")
    use_case_private: Optional[bool] = Field(True, description="Indicates if the use case is private")
    provider_id: Optional[int] = Field(None, description="ID of the associated provider")
    project_id: Optional[str] = Field(None, description="ID of the associated project")
    location: Optional[str] = Field("global", description="Location of the use case")
    engine_id: Optional[str] = Field(None, description="ID of the engine")
    description: Optional[str] = Field(None, description="Description of the use case")
    can_multimodal: Optional[bool] = Field(True, description="Indicates if the use case can be multimodal")
    is_multimodal: Optional[bool] = Field(False, description="Indicates if the use case is multimodal")
    multimodal_extensions: Optional[Dict[str, Any]] = Field(None, description="Multimodal extension details")
    data_store_id: Optional[str] = Field(None, description="ID of the data store")
    max_documents: Optional[int] = Field(1, description="Maximum number of documents")
    max_extractive_answer_count: Optional[int] = Field(5, description="Maximum number of extractive answers")
    get_extractive_answers: Optional[bool] = Field(True, description="Indicates if extractive answers are enabled")
    engine_data_type: Optional[int] = Field(2, description="Data type of the engine")
    llms_model_name: Optional[str] = Field(..., description="Name of the LLM model")
    llms_model_version: Optional[str] = Field(..., description="Version of the LLM model")
    system_prompt: Optional[str] = Field(..., description="System prompt for the use case")
    chain_prompt: Optional[str] = Field(None, description="Prompt for chain of thought")
    answer_language_code: Optional[str] = Field("ja", description="Language code for the answers")
    chain_of_thought_prompt: Optional[bool] = Field(True, description="Chain of thought prompting")
    search_type: Optional[str] = Field(None, description="Type of search")
    retriever_type: Optional[str] = Field(None, description="Type of retriever used")
    result_count: Optional[int] = Field(None, description="Number of results")
    related_questions: Optional[bool] = Field(False, description="Indicates if related questions are enabled")
    snippets_or_extractive: Optional[bool] = Field(False,
                                                   description="Indicates if snippets or extractive answers are enabled")
    autocomplete_suggestions: Optional[bool] = Field(False,
                                                     description="Indicates if autocomplete suggestions are enabled")
    feedback: Optional[bool] = Field(False, description="Indicates if feedback is enabled")
    list_group_id: Optional[List[int]] = Field([], description="List of groups")
    human_prompt: Optional[str] = Field(None, description="Human prompt for the use case")
    format_output: Optional[str] = Field(None, description="Format of the output")
    is_active: Optional[bool] = Field(None, description="Indicates if the use case is active")
    use_ragsource: Optional[bool] = Field(True, description="Use Ragsource")
    priority: Optional[int] = Field(1, description="Priority of the use case")
    is_public: Optional[bool] = Field(False, description="Indicates if the use case is public")
    list_provider_ids: Optional[List[int]]

    class Config:
        from_attributes = True
        protected_namespaces = ()


class ChatRoomRespV2(BaseModel):
    id: int
    title: str
    chatroom_code: Optional[str]
    user_id: int
    user_name: Optional[str]
    user_email: Optional[str]
    use_case_title: Optional[str]
    ai_service: Optional[str]
    llms_model_name: Optional[str]
    use_case_id: Optional[int]
    avatar_url: Optional[str] = None
    provider_id: Optional[int]
    llms_model: Optional[Dict[str, Any]] = None
    reasoning_enabled: Optional[bool] = None  # Added for reasoning toggle feature
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class CreateReactionForChat(BaseModel):
    chat_hist_id: int = Field(..., description="ID of the chatroom")
    reaction_type: str = Field(..., description="Type of reaction")

    class Config:
        from_attributes = True


class ChatHistoryRespWithReaction(BaseModel):
    id: int
    user_id: int
    chatroom_id: int
    message_id: str
    chat_id: str
    session_id: str
    service_name: Optional[str]
    llm_model_id: Optional[int]
    use_case_id: Optional[int]
    user_message: Optional[str]
    ai_response: Optional[str]
    language: Optional[str]
    sentiment: Optional[str]
    input_length: Optional[int]
    output_length: Optional[int]
    input_tokens: Optional[int]
    output_tokens: Optional[int]
    response_time_ms: Optional[int]
    reactions: Optional[str]
    is_resolved: Optional[bool] = False
    source_info: Optional[str]
    has_images: Optional[bool] = False
    has_files: Optional[bool] = False
    image_list: Optional[List[Dict[str, Any]]]
    file_list: Optional[List[Dict[str, Any]]]
    references: Optional[List[Dict[str, Any]]]
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UseCaseRespV2(BaseModel):
    id: int
    use_case_title: Optional[str]
    use_case_code: Optional[str]
    use_case_private: Optional[bool] = True
    description: Optional[str]
    provider_id: Optional[int]
    project_id: Optional[str]
    location: Optional[str] = "global"
    engine_id: Optional[str]
    data_store_id: Optional[str]
    max_documents: Optional[int] = 1
    max_extractive_answer_count: Optional[int] = 5
    get_extractive_answers: Optional[bool] = True
    engine_data_type: Optional[int] = 2
    ai_service: Optional[str]
    llms_model_name: Optional[str]
    llms_model_version: Optional[str]
    system_prompt: Optional[str]
    chain_prompt: Optional[str]
    answer_language_code: Optional[str] = "ja"
    chain_of_thought_prompt: Optional[bool] = True
    search_type: Optional[str]
    can_multimodal: Optional[bool] = True
    is_multimodal: Optional[bool] = False
    multimodal_extensions: Optional[Dict[str, Any]]
    retriever_type: Optional[str]
    result_count: Optional[int]
    related_questions: Optional[bool] = False
    snippets_or_extractive: Optional[bool] = False
    autocomplete_suggestions: Optional[bool] = False
    feedback: Optional[bool] = False
    list_group_id: Optional[List[int]] = []
    human_prompt: Optional[str] = None
    format_output: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    use_ragsource: Optional[bool] = None
    priority: Optional[int] = None
    is_public: Optional[bool] = None
    list_provider_ids: Optional[List[int]]


class UsecaseFilter(BaseModel):
    is_active: Optional[str] = "true"


class ChatRoomRespV3(BaseModel):
    id: int
    title: str
    chatroom_code: Optional[str]
    user_id: int
    use_case_id: Optional[int]
    use_case_title: Optional[str] = None
    ai_service: Optional[str] = None
    llms_model_name: Optional[str] = None
    avatar_url: Optional[str] = None
    provider_id: Optional[int]
    llms_model: Optional[Dict[str, Any]] = None
    reasoning_enabled: Optional[bool] = None  # Added for reasoning toggle feature
    is_deleted: Optional[bool] = False
    deleted_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UseCaseRespV3(BaseModel):
    id: int
    use_case_title: Optional[str]
    use_case_code: Optional[str]
    use_case_private: Optional[bool] = True
    description: Optional[str]
    provider_id: Optional[int]
    project_id: Optional[str]
    location: Optional[str] = "global"
    engine_id: Optional[str]
    data_store_id: Optional[str]
    max_documents: Optional[int] = 1
    max_extractive_answer_count: Optional[int] = 5
    get_extractive_answers: Optional[bool] = True
    engine_data_type: Optional[int] = 2
    ai_service: Optional[str]
    llms_model_name: Optional[str]
    llms_model_version: Optional[str]
    system_prompt: Optional[str]
    chain_prompt: Optional[str]
    answer_language_code: Optional[str] = "ja"
    chain_of_thought_prompt: Optional[bool] = True
    search_type: Optional[str]
    can_multimodal: Optional[bool] = True
    is_multimodal: Optional[bool] = False
    multimodal_extensions: Optional[Dict[str, Any]]
    retriever_type: Optional[str]
    result_count: Optional[int]
    related_questions: Optional[bool] = False
    snippets_or_extractive: Optional[bool] = False
    autocomplete_suggestions: Optional[bool] = False
    feedback: Optional[bool] = False
    list_group_id: Optional[List[GroupReps]] = []
    human_prompt: Optional[str] = None
    format_output: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    use_ragsource: Optional[bool] = None
    priority: Optional[int] = None
    is_public: Optional[bool] = None
    list_provider_ids: Optional[List[int]]
    list_models: Optional[List[Dict[str, Any]]]
    created_by: Optional[int] = None
    updated_by: Optional[int] = None


class UsecaseFilter(BaseModel):
    data_store_id: Optional[str] = None
    group_name: Optional[str] = None
    is_active: Optional[str] = "true"


class ProviderFilter(BaseModel):
    is_active: Optional[str] = "true"


class UpdateProviderUsecase(BaseModel):
    list_use_provider: Optional[List[int]] = []
