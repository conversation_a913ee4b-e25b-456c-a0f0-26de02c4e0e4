from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel, constr, Field


class ResourceReps(BaseModel):
    id: int
    name: str
    type: str
    description: List[Any] = []
    resource_metadata: list[str]
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ResourceCreate(BaseModel):
    name: str
    type: str
    description: List[Any] = []
    resource_metadata: list[str]
    is_active: bool = Field(True, description="", example=None)


class ResourceUpdate(BaseModel):
    name: str
    type: str
    description: List[Any] = []
    resource_metadata: list[str]
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
