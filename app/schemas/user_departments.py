from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class UserDepartmentReps(BaseModel):
    id: int
    user_id: int
    department_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserDepartmentCreate(BaseModel):
    user_id: int
    department_id: int
    is_active: bool = Field(True, description="", example=None)


class UserDepartmentUpdate(BaseModel):
    user_id: int
    department_id: int
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
