import re
from enum import Enum
from pydantic import BaseModel, Field, validator, EmailStr
from typing import Optional, Literal, List
from fastapi import status
from app.helpers.exceptions import GatewayException


class AuthModeEnum(str, Enum):
    LEGACY = 'LEGACY'
    LDAP = 'LDAP'


class SSOLogin(BaseModel):
    access_token: str
    provider: str = 'google'
    email: Optional[str] = None
    fullname: str = None
    avatar_url: Optional[str] = None


class OauthLogin(BaseModel):
    email: str = Field(..., description="Thông tin email.")
    password: str = Field(..., description="Thông tin password.")

    @validator('email')
    def validate_email(cls, v):
        if not v.strip():
            raise GatewayException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail="email không được để trống.")
        email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(email_regex, v):
            raise GatewayException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail="email không hợp lệ.")
        return v

    @validator('password')
    def validate_password(cls, v):
        if not v.strip():
            raise GatewayException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail="password không được để trống.")
        return v

    class Config:
        from_attributes = True
        protected_namespaces = ()


class ChangePassword(BaseModel):
    old_password: str
    password: str
    confirm_pw: str


class SetPassword(BaseModel):
    token: str
    password: str
    confirm_pw: str
