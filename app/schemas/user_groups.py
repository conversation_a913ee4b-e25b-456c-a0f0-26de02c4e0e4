from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel, constr, Field


class UserGroupReps(BaseModel):
    id: int
    user_id: Optional[int] = None
    group_id: Optional[int] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserGroupCreate(BaseModel):
    user_id: int
    group_id: int
    is_active: bool = Field(True, description="", example=None)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class UserGroupUpdate(BaseModel):
    user_id: Optional[int] = None
    group_id: Optional[int] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserGroupFilter(BaseModel):
    is_active: Optional[bool] = True
    is_deleted: Optional[bool] = False

