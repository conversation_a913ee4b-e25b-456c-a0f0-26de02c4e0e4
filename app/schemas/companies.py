from datetime import datetime
from pydantic import BaseModel, constr, Field
from app.schemas.service_packages import ServicePackagesResp
from typing import Optional, List, Dict, Any


class CompanyReps(BaseModel):
    id: int
    company_name: str
    company_code: str
    company_type: str
    company_slug: str
    parent_company_id: Optional[int] = None
    tax_code: Optional[int] = None
    registration_number: Optional[int] = None
    address: Optional[str] = None
    ward: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    phone: Optional[str] = None
    website_url: Optional[str] = None
    industry: Optional[str] = None
    number_of_employees: Optional[int] = None
    registered_package: Optional[ServicePackagesResp] = None
    created_users: Optional[int] = None
    max_users: Optional[int] = None
    email: Optional[str] = None
    number_of_use_cases: Optional[int] = None
    number_of_data_stores: Optional[int] = None
    number_of_agent_builders: Optional[int] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class CompanyCreate(BaseModel):
    # TODO Create Company
    company_name: str
    company_slug: str
    company_type: str
    parent_company_id: Optional[int] = None
    tax_code: Optional[int] = None
    registration_number: Optional[int] = None
    address: Optional[str] = None
    ward: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    phone: Optional[str] = None
    website_url: Optional[str] = None
    email: Optional[str] = None
    industry: Optional[str] = None
    number_of_employees: Optional[int] = None
    # TODO add registered_package
    package_name: str = Field(None, description="Name of the service package")
    description: Optional[str] = Field(None, description="Description of the service package")
    max_datastores: int = Field(None, description="Maximum number of datastores")
    max_agent_builders: int = Field(None, description="Maximum number of agent builders")
    max_queries_per_day: int = Field(None, description="Maximum number of queries per day")
    max_documents_per_query: int = Field(None, description="Maximum number of documents per query")
    max_users: int = Field(None, description="Maximum number of users")
    max_admins: int = Field(None, description="Maximum number of admins")
    max_managers: int = Field(None, description="Maximum number of managers")
    storage_limit: int = Field(None, description="Storage limit")
    credits_package_limit: int = Field(None, description="Credits package limit")
    llm_model_type: Optional[Dict[str, Any]] = Field(None, description="LLM model type")
    max_tokens_per_month: int = Field(None, description="Maximum number of tokens per month")
    conversation_retention_days: int = Field(None, description="Conversation retention days")
    max_concurrent_conversations: int = Field(None, description="Maximum number of concurrent conversations")
    encryption_level: str = Field(None, description="Encryption level")
    is_active: bool = Field(True, description="", example=None)


class CompanyUpdate(BaseModel):
    # TODO Update Company
    company_name: str
    company_slug: str
    company_type: str
    parent_company_id: Optional[int] = None
    tax_code: Optional[int] = None
    registration_number: Optional[int] = None
    address: Optional[str] = None
    ward: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    phone: Optional[str] = None
    website_url: Optional[str] = None
    email: Optional[str] = None
    industry: Optional[str] = None
    number_of_employees: Optional[int] = None
    # TODO update registered_package
    package_name: str = Field(None, description="Name of the service package")
    description: Optional[str] = Field(None, description="Description of the service package")
    max_datastores: int = Field(None, description="Maximum number of datastores")
    max_agent_builders: int = Field(None, description="Maximum number of agent builders")
    max_queries_per_day: int = Field(None, description="Maximum number of queries per day")
    max_documents_per_query: int = Field(None, description="Maximum number of documents per query")
    max_users: int = Field(None, description="Maximum number of users")
    max_admins: int = Field(None, description="Maximum number of admins")
    max_managers: int = Field(None, description="Maximum number of managers")
    storage_limit: int = Field(None, description="Storage limit")
    credits_package_limit: int = Field(None, description="Credits package limit")
    llm_model_type: Optional[Dict[str, Any]] = Field(None, description="LLM model type")
    max_tokens_per_month: int = Field(None, description="Maximum number of tokens per month")
    conversation_retention_days: int = Field(None, description="Conversation retention days")
    max_concurrent_conversations: int = Field(None, description="Maximum number of concurrent conversations")
    encryption_level: str = Field(None, description="Encryption level")
    note: Optional[str] = Field(None, description="Note")
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class CompanyFilter(BaseModel):
    company_name: Optional[str] = Field(None, description="Company Name")
    phone: Optional[str] = Field(None, description="Phone")
    company_slug: Optional[str] = Field(None, description="Company Slug")
    is_active: Optional[str] = None
    is_deleted: Optional[bool] = False
