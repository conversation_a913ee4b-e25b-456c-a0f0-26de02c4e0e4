import uuid
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, constr, Field, model_validator
from app.helpers.constants import RoleMapping, AuthMode
from app.schemas.groups import GroupReps
from app.schemas.companies import CompanyReps
from app.schemas.permissions import PermissionReps


class UserBase1(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = True

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UserCreateRequest(UserBase1):
    full_name: Optional[str]
    password: str
    email: EmailStr
    is_active: bool = True
    role: RoleMapping = RoleMapping.GUEST


class UserCreateSSRequest(BaseModel):
    full_name: Optional[str]
    email: EmailStr
    provider: Optional[str]
    is_active: bool = True
    role: RoleMapping = RoleMapping.GUEST


class UserRegisterRequest(BaseModel):
    full_name: str
    email: EmailStr
    password: str
    role: RoleMapping = RoleMapping.GUEST


class UserUpdateMeRequest(BaseModel):
    full_name: Optional[str]
    email: Optional[EmailStr]
    password: Optional[str]


class UserUpdateRequest(BaseModel):
    full_name: Optional[str]
    email: Optional[EmailStr]
    password: Optional[str]
    is_active: Optional[bool] = True
    role: Optional[RoleMapping]


class LoginRequest(BaseModel):
    username: EmailStr = '<EMAIL>'
    password: str = '123456c@'


class SSLoginRequest(BaseModel):
    provider: str = ''
    email: str = ''
    fullname: str = ''


class PasswordForgotReq(BaseModel):
    email: str

    @model_validator(mode='before')
    def strip_whitespace(cls, values):
        if 'email' in values:
            values['email'] = values['email'].strip()
        return values

    @property
    def email(self):
        return EmailStr.validate(self.email)[0]


class PasswordResetReq(BaseModel):
    token: str
    new_password: str
    confirm_new_password: str


class PermissionSchema(BaseModel):
    id: int
    name: str
    description: str | None = None
    grant_type: str | None = None
    valid_from: str | None = None
    valid_to: str | None = None

    class Config:
        from_attributes = True
        protected_namespaces = ()


class RoleSchema(BaseModel):
    id: int
    name: str
    description: Optional[str] = None


class UserGroupSchema(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True
        protected_namespaces = ()


class UserReps(BaseModel):
    id: int
    full_name: Optional[str] = None
    email: EmailStr
    provider: Optional[str] = None
    auth_mode: Optional[str] = None
    bio: Optional[dict] = None
    uid: Optional[str] = None
    roles: Optional[List[RoleSchema]] = []
    groups: Optional[List[GroupReps]] = []
    permissions: Optional[List[PermissionReps]] = []
    company: Optional[CompanyReps] = []
    is_superuser: Optional[bool] = False
    validated: Optional[bool] = Field(default=False, alias="validated")
    last_login: Optional[datetime] = None
    avatar_url: Optional[str] = None
    additional_info: Optional[dict] = None
    company_id: Optional[int] = None
    manager_id: Optional[int] = None
    user_name: Optional[str] = None
    business_type: Optional[str] = None
    company_slug: Optional[str] = None
    company_name: Optional[str] = None
    is_musashino: Optional[bool] = False
    company_active: Optional[bool] = False
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserCreate(BaseModel):
    full_name: Optional[str] = None
    email: EmailStr
    password: str
    confirm_pw: str
    provider: Optional[str] = None
    auth_mode: Optional[AuthMode] = AuthMode.OAUTH
    roles: Optional[List[int]] = []
    groups: Optional[List[int]] = []
    bio: Optional[dict] = None
    # uid: Optional[str] = Field(f"{uuid.uuid4().hex}".upper(), description="", example=None)
    last_login: Optional[datetime] = None
    avatar_url: Optional[str] = None
    additional_info: Optional[dict] = None
    validated: Optional[bool] = Field(default=False, alias="validated")
    is_superuser: Optional[bool] = False
    is_active: bool = Field(True, description="", example=None)
    company_id: Optional[int] = None
    manager_id: Optional[int] = None
    user_name: Optional[str] = None
    business_type: Optional[str] = None
    is_musashino: Optional[bool] = False

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    bio: Optional[dict] = None
    avatar_url: Optional[str] = None
    validated: Optional[bool] = False
    is_superuser: Optional[bool] = False
    additional_info: Optional[dict] = None
    roles: Optional[List[int]] = []
    groups: Optional[List[int]] = []
    is_active: bool = Field(True, description="", example=None)
    company_id: Optional[int] = None
    manager_id: Optional[int] = None
    user_name: Optional[str] = None
    business_type: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class DeleteUser(BaseModel):
    full_name: Optional[str] = None
    bio: Optional[dict] = None
    avatar_url: Optional[str] = None
    validated: Optional[bool] = False
    is_superuser: Optional[bool] = False
    additional_info: Optional[dict] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class SendEmail(BaseModel):
    email: EmailStr

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UpdateProfile(BaseModel):
    full_name: Optional[str] = None

    # avatar_url: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class UserFilter(BaseModel):
    role_name: Optional[str] = Field(None, description="Role Name")
    group_name: Optional[str] = Field(None, description="Group Name")
    is_active: Optional[str] = Field(None, description="Is Active")
    is_deleted: Optional[bool] = False


class UserUpdateExcel(BaseModel):
    list_user: Optional[List[int]] = []


class UserDeleteList(BaseModel):
    list_user: Optional[List[int]] = []

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ListUserActive(BaseModel):
    list_user: Optional[List[int]] = []
