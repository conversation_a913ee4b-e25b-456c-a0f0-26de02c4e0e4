from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class DSReps(BaseModel):
    id: int
    datastore_name: str
    datastore_id: str
    company_slug: str
    description: Optional[str] = None
    project_id: str
    path: str
    location: str
    company_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
