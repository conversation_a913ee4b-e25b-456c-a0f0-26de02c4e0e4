from datetime import datetime
from pydantic import BaseModel, constr, Field
from typing import Optional, List, Dict, Any


class ServicePackagesResp(BaseModel):
    id: int = Field(None, description="ID of the service package")
    package_name: str = Field(None, description="Name of the service package")
    description: Optional[str] = Field(None, description="Description of the service package")
    max_datastores: int = Field(None, description="Maximum number of datastores")
    max_agent_builders: int = Field(None, description="Maximum number of agent builders")
    max_queries_per_day: int = Field(None, description="Maximum number of queries per day")
    max_documents_per_query: int = Field(None, description="Maximum number of documents per query")
    max_users: int = Field(None, description="Maximum number of users")
    old_max_users: Optional[int] = Field(None, description="Old maximum number of users")
    max_admins: int = Field(None, description="Maximum number of admins")
    max_managers: int = Field(None, description="Maximum number of managers")
    storage_limit: int = Field(None, description="Storage limit")
    credits_package_limit: int = Field(None, description="Credits package limit")
    llm_model_type: Optional[Dict[str, Any]] = Field(None, description="LLM model type")
    max_tokens_per_month: int = Field(None, description="Maximum number of tokens per month")
    conversation_retention_days: int = Field(None, description="Conversation retention days")
    max_concurrent_conversations: int = Field(None, description="Maximum number of concurrent conversations")
    encryption_level: str = Field(None, description="Encryption level")
    note: Optional[str]
    company_active: Optional[bool] = False
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ServicePackagesCreate(BaseModel):
    package_name: str = Field(None, description="Name of the service package")
    description: Optional[str] = Field(None, description="Description of the service package")
    max_datastores: int = Field(None, description="Maximum number of datastores")
    max_queries_per_day: int = Field(None, description="Maximum number of queries per day")
    max_documents_per_query: int = Field(None, description="Maximum number of documents per query")
    max_users: int = Field(None, description="Maximum number of users")
    max_admins: int = Field(None, description="Maximum number of admins")
    max_managers: int = Field(None, description="Maximum number of managers")
    storage_limit: int = Field(None, description="Storage limit")
    credits_package_limit: int = Field(None, description="Credits package limit")
    llm_model_type: Optional[Dict[str, Any]] = Field(None, description="LLM model type")
    max_tokens_per_month: int = Field(None, description="Maximum number of tokens per month")
    conversation_retention_days: int = Field(None, description="Conversation retention days")
    max_concurrent_conversations: int = Field(None, description="Maximum number of concurrent conversations")
    encryption_level: str = Field(None, description="Encryption level")
    is_active: bool = Field(True, description="", example=None)


class ServicePackagesUpdate(BaseModel):
    package_name: str = Field(None, description="Name of the service package")
    description: Optional[str] = Field(None, description="Description of the service package")
    max_datastores: int = Field(None, description="Maximum number of datastores")
    max_agent_builders: int = Field(None, description="Maximum number of agent builders")
    max_queries_per_day: int = Field(None, description="Maximum number of queries per day")
    max_documents_per_query: int = Field(None, description="Maximum number of documents per query")
    max_users: int = Field(None, description="Maximum number of users")
    max_admins: int = Field(None, description="Maximum number of admins")
    max_managers: int = Field(None, description="Maximum number of managers")
    storage_limit: int = Field(None, description="Storage limit")
    credits_package_limit: int = Field(None, description="Credits package limit")
    llm_model_type: Optional[Dict[str, Any]] = Field(None, description="LLM model type")
    max_tokens_per_month: int = Field(None, description="Maximum number of tokens per month")
    conversation_retention_days: int = Field(None, description="Conversation retention days")
    max_concurrent_conversations: int = Field(None, description="Maximum number of concurrent conversations")
    encryption_level: str = Field(None, description="Encryption level")
    note: Optional[str]
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class ServicePackageFilter(BaseModel):
    is_active: Optional[str] = None
    is_deleted: Optional[bool] = False
