from typing import List

from pydantic import BaseModel
from typing import Optional


class AgentBuilderCreate(BaseModel):
    display_name: str
    path: str = "musashino/customers/",
    description: Optional[str] = None

    app_engine_id: str
    app_engine_name: str
    # data_store_ids: List[str]
    description_app: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class AgentBuilderCreateApp(BaseModel):
    app_engine_id: str
    app_engine_name: str
    data_store_ids: List[str]
    description: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
