from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel, constr, Field


class AuditLogReps(BaseModel):
    id: int
    user_id: int
    company_id: int
    action: str = Field(None, description='e.g., "Login", "Query Datastore"', example=None)
    details: str
    ip_address: str
    status: str = Field(None, description='S or F', example=None)
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class AuditLogCreate(BaseModel):
    user_id: int
    company_id: int
    action: str = Field(None, description='e.g., "Login", "Query Datastore"', example=None)
    details: str
    ip_address: str
    status: str = Field(None, description='S or F', example=None)
    is_active: bool = Field(True, description="", example=None)


class AuditLogUpdate(BaseModel):
    user_id: int
    company_id: int
    action: str = Field(None, description='e.g., "Login", "Query Datastore"', example=None)
    details: str
    ip_address: str
    status: str = Field(None, description='S or F', example=None)
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
