from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class DatastoreReps(BaseModel):
    id: int
    company_id: int
    datastore_name: str
    description: Optional[str] = None
    created_by: Optional[int] = None
    status: str
    storage_used: Optional[int] = None
    document_count: Optional[int] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class DatastoreCreate(BaseModel):
    company_id: int
    datastore_name: str
    description: Optional[str] = None
    created_by: int
    status: str
    storage_used: int
    document_count: int
    is_active: bool = Field(True, description="", example=None)


class DatastoreUpdate(BaseModel):
    company_id: int
    datastore_name: str
    description: Optional[str] = None
    created_by: int
    status: str
    storage_used: int
    document_count: int
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
