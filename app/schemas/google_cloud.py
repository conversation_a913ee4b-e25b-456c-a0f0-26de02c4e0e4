import re
from uuid import uuid4
from datetime import datetime
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any


class CreateBucket(BaseModel):
    bucket_name: str
    location: str = "asia-northeast1"
    storage_class: str = "STANDARD"  # Storage class defaults to STANDARD

    class Config:
        from_attributes = True
        protected_namespaces = ()


class BucketResp(BaseModel):
    bucket_name: str
    location: str
    storage_class: str

    class Config:
        from_attributes = True
        protected_namespaces = ()


class RenameBucket(BaseModel):
    old_bucket_name: str
    new_bucket_name: str
    location: str = "asia-northeast1"
    storage_class: str = "STANDARD"  # Storage class defaults to STANDARD

    class Config:
        from_attributes = True
        protected_namespaces = ()


class SetStatusBucket(BaseModel):
    bucket_name: str

    class Config:
        from_attributes = True
        protected_namespaces = ()
