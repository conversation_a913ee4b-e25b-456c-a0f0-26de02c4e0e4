from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class DepartmentDataStoreAccessReps(BaseModel):
    id: int
    department_id: int
    datastore_id: int
    access_level: str = Field(None, description="Access level of the department to the datastore: read/write/admin")
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class DepartmentDataStoreAccessCreate(BaseModel):
    department_id: int
    datastore_id: int
    access_level: str = Field(None, description="Access level of the department to the datastore: read/write/admin")
    is_active: bool = Field(True, description="", example=None)


class DepartmentDataStoreAccessUpdate(BaseModel):
    department_id: int
    datastore_id: int
    access_level: str = Field(None, description="Access level of the department to the datastore: read/write/admin")
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
