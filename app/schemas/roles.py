import uuid
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, constr, Field, UUID4
from app.schemas.users import PermissionReps


class RoleResp(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    important: Optional[bool] = False
    company_id: Optional[int] = None
    list_permission: Optional[list[PermissionReps]] = None
    is_active: Optional[bool] = None
    is_deleted: Optional[bool] = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    company_id: Optional[int] = None
    permission_ids: Optional[List[int]] = None
    is_active: bool = Field(True, description="", example=None)


class RoleUpdate(BaseModel):
    name: str
    description: Optional[str] = None
    permission_ids: Optional[List[int]] = None
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class RoleFilter(BaseModel):
    is_active: Optional[bool] = True
    is_deleted: Optional[bool] = False


class RoleRespV2(BaseModel):
    id: int
    name: str
    list_user_id: Optional[list[dict]] = None
    description: Optional[str] = None
    important: Optional[bool] = False
    is_active: Optional[bool] = None
    is_deleted: Optional[bool] = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
