from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, Field


class RolePermissionReps(BaseModel):
    id: int
    role_id: int
    permission_id: int
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()


class RolePermissionCreate(BaseModel):
    role_id: int
    permission_id: int
    is_active: bool = Field(True, description="", example=None)


class RolePermissionUpdate(BaseModel):
    role_id: int
    permission_id: int
    is_active: bool = Field(True, description="", example=None)

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
