from datetime import datetime
from typing import Optional
from pydantic import BaseModel, constr, <PERSON>
from typing import List


class ABReps(BaseModel):
    id: int
    agent_builder_name: str
    agent_builder_id: str
    data_store_ids: List[str]
    company_slug: int
    project_id: str
    location: str
    company_id: int
    description: Optional[str] = None
    is_deleted: Optional[bool] = False
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True
        protected_namespaces = ()
