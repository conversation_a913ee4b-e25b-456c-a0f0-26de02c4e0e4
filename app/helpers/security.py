import uuid
import hashlib
import json
import jwt
import traceback
from calendar import timegm
from functools import partial
from base64 import b64encode
from datetime import datetime, timezone, timedelta
from bcrypt import hashpw, gensalt
from typing import Any, Union, List, Optional, Dict
from itsdangerous import URLSafeTimedSerializer
from pydantic import ValidationError
from passlib.context import CryptContext
from password_strength import PasswordPolicy, PasswordStats
from fastapi import Request, Depends, status as http_status
from fastapi.security import HTTPBearer
from fastapi.security.utils import get_authorization_scheme_param
from sqlalchemy.orm import Session
from app.helpers.config import settings
from app.helpers.exceptions import GatewayException
from app.helpers.constants import RoleMapping
from app.db.base import get_db
from app.models.users import UserRole as UserRoleModel, Role, Permission
from app.models.role_permissions import RolePermission
from app.models.token import Token

serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
reusable_oauth2 = HTTPBearer(scheme_name='Authorization')


class Authorizer:
    def __init__(self, uid: int, client_id: str, email: str = None, role: str = None, client_host: str = None,
                 p_limit: int = 0):
        self.uid = uid
        self.client_id = client_id
        # self.role = role
        self.client_host = client_host

    def to_dict(self):
        return self.__dict__.copy()

    @classmethod
    def from_dict(cls, data: dict):
        return cls(data.get('uid'), data.get('client_id'), data.get('client_host'))


def check_client_ip(request: Request):
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        ip = forwarded.split(",")[0]
    else:
        ip = request.client.host
    return ip


def check_password_strength(password, language):
    policy = PasswordPolicy.from_names(
        length=8,  # Minimum length 8 characters
        uppercase=1,  # Require at least 1 uppercase letter
        numbers=1,  # Require at least 1 digit
        special=1,  # need min. 2 special characters
        nonletters=2,  # Require at least 2 non-letter character
    )
    stats = PasswordStats(password)
    score = stats.strength()
    is_leak = policy.test(password)
    if not is_leak:
        if language == "jp":
            message = "パスワードは十分強力です。"
        else:
            message = "Strong password."
        return True, score, message
    else:
        if language == "jp":
            message = "より強力なパスワードを設定してください"
        else:
            message = "Weak password."
        return False, score, message


def invite_token(user: Authorizer):
    return serializer.dumps(str(user.uid))


def hash_password(password: str):
    hashed_sha256 = hashlib.sha256(password.encode()).digest()
    b64pwd = b64encode(hashed_sha256)
    hashed_bcrypt = hashpw(b64pwd, gensalt())
    return hashed_bcrypt.decode()


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def is_exp(token):
    now = timegm(datetime.now(tz=timezone.utc).utctimetuple())
    return now > token['exp']


def generate_legacy_token(auth: Authorizer) -> str:
    to_encode = auth.to_dict()
    to_encode["iat"] = datetime.now(tz=timezone.utc)
    to_encode["jti"] = f"jti_{uuid.uuid4().hex}"
    to_encode.update({"exp": datetime.now(tz=timezone.utc) + timedelta(seconds=settings.ACCESS_TOKEN_EXPIRE_SECONDS)})
    encoded_jwt = jwt.encode(to_encode, settings.PARTNER_SECRET_KEY, algorithm=settings.SECURITY_ALGORITHM)
    return encoded_jwt


def generate_legacy_refresh_token(auth: Authorizer) -> str:
    to_encode = auth.to_dict()
    to_encode["iat"] = datetime.now(tz=timezone.utc)
    to_encode["jti"] = f"jti_{uuid.uuid4().hex}"
    to_encode.update({"exp": datetime.now(tz=timezone.utc) + timedelta(seconds=settings.REFRESH_TOKEN_EXPIRE_SECONDS)})
    encoded_jwt = jwt.encode(to_encode, settings.PARTNER_REFRESH_SECRET_KEY, algorithm=settings.SECURITY_ALGORITHM)
    return encoded_jwt


def get_token_from_request(request: Request, token_name: str = 't') -> str:
    auth_header = request.headers.get("Authorization")
    scheme, token = get_authorization_scheme_param(auth_header)
    if scheme.lower() == "bearer" and token:
        return token

    token = request.cookies.get(token_name)
    if token:
        return token

    raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Token not found.")


def validate_legacy_token(request: Request, token: str = Depends(get_token_from_request),
                          db: Session = Depends(get_db)) -> Union[str, Any]:
    try:
        if token.count('.') != 2:
            raise GatewayException(
                status_code=http_status.HTTP_401_UNAUTHORIZED,
                detail="Malformed token"
            )

        lee_time = 60
        payload = jwt.decode(
            token,
            settings.PARTNER_SECRET_KEY,
            leeway=timedelta(seconds=lee_time),
            algorithms=[settings.SECURITY_ALGORITHM]
        )

        if is_exp(payload):
            raise GatewayException(
                status_code=http_status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )

        request.state.user = Authorizer.from_dict(payload)

        exist_token = db.query(Token).filter(
            Token.user_id == request.state.user.client_id,
            Token.user_uid == request.state.user.uid,
            Token.is_active == True,
            Token.is_deleted == False
        ).first()
        if not exist_token:
            raise GatewayException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="アクセスできません。"
            )

        user_role = db.query(UserRoleModel, Role).join(Role, UserRoleModel.role_id == Role.id).filter(
            UserRoleModel.user_id == request.state.user.client_id,
            UserRoleModel.is_deleted == False
        ).all()
        role_names = [role.name.lower() for (_, role) in user_role]
    
        if exist_token.product_type not in role_names and role_names != ['superadmin']:
            raise GatewayException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="アクセスできません。"
            )

        return request.state.user

    except GatewayException:
        raise

    except (jwt.PyJWTError, ValidationError):
        print(f"[POST][GATEWAY ERROR] VALIDATE TOKEN: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Token invalid")


def validate_legacy_refresh_token(request: Request,
                                  token: str = Depends(partial(get_token_from_request, token_name="r"))) -> Union[
    str, Any]:
    try:
        if token and token.count('.') == 2:
            payload = jwt.decode(token, settings.PARTNER_REFRESH_SECRET_KEY, algorithms=[settings.SECURITY_ALGORITHM])
        else:
            raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Malformed token")

        if is_exp(payload):
            raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Token has expired")

        request.state.user = Authorizer.from_dict(payload)
        return request.state.user

    except GatewayException:
        raise

    except (jwt.PyJWTError, ValidationError) as exc:
        print(f"[POST][GATEWAY ERROR] VALIDATE REFRESH TOKEN: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Token invalid")


class AuthPermissionChecker:
    def __init__(self, roles, component: bool = False, required_permission: bool = False):
        self.roles = [role.lower() for role in roles]
        self.required_permission = required_permission

    def __is_super_admin(self, user_roles):
        return any(role.name.lower() == RoleMapping.SUPER_ADMIN.lower() for role in user_roles)

    def __is_valid_role(self, user_roles):
        return any(role.name.lower() in self.roles for role in user_roles)

    def __has_permission(self, db, role_ids, endpoint_name):
        return (
                db.query(Permission)
                .join(RolePermission, RolePermission.permission_id == Permission.id)
                .filter(
                    RolePermission.role_id.in_(role_ids),
                    Permission.endpoint == endpoint_name,
                )
                .first()
                is not None
        )

    @staticmethod
    def get_endpoint_name(request: Request) -> str:
        if "route" in request.scope:
            endpoint_name = getattr(request.scope["route"], "name", None)
            if endpoint_name:
                return endpoint_name

        return request.scope["endpoint"].__name__

    @staticmethod
    def requires_permission(request: Request) -> bool:
        tags = getattr(request.scope["route"], "tags", [])
        return "requires_permission" in tags

    def __call__(self,
                 request: Request,
                 endpoint_name: str = Depends(get_endpoint_name),
                 user: Authorizer = Depends(validate_legacy_token),
                 db: Session = Depends(get_db)):
        try:
            auth = request.state.user.to_dict()
            client_id = auth.get("client_id")

            if not client_id:
                raise GatewayException(
                    status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="Token invalid !"
                )

            user_roles = (
                db.query(UserRoleModel, Role)
                .join(Role, UserRoleModel.role_id == Role.id)
                .filter(
                    UserRoleModel.user_id == client_id,
                    UserRoleModel.is_deleted == False,
                )
                .all()
            )

            role_ids = [role.id for _, role in user_roles]

            if self.__is_super_admin([role for _, role in user_roles]):
                return user

            if not self.required_permission or not endpoint_name:
                return user

            # if self.__is_valid_role([role for _, role in user_roles]) and self.__has_permission(db, role_ids,
            #                                                                                     endpoint_name):
            #     return user

            raise GatewayException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="You have no right to access this resource !"
            )

        except GatewayException:
            raise
        except Exception:
            print(f"[POST][PERMISSION CHECKER ERROR]: {traceback.format_exc()}")
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="Bad Request."
            )


validate_super_admin = [RoleMapping.SUPER_ADMIN.lower()]
validate_admin = [RoleMapping.ADMIN.lower()]
validate_user = [RoleMapping.SUPER_ADMIN.lower(), RoleMapping.ADMIN.lower(), RoleMapping.USER.lower()]
