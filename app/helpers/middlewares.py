from __future__ import annotations
import time
import json
import uuid
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.base import BaseHTTPMiddleware, _StreamingResponse
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import Response, StreamingResponse
from app.helpers.config import settings
from app.helpers.logging import log
from typing import List, Union
import asyncio

__all__ = ["add_middleware"]

MAX_FILE_SIZE = 1024 * 1024 * 1024 * 4  # = 4GB
MAX_REQUEST_BODY_SIZE = MAX_FILE_SIZE + 1024


class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        x_request_id = str(uuid.uuid4())
        request.state.x_request_id = x_request_id
        response = await call_next(request)
        response.headers["X-Request-ID"] = x_request_id
        return response


class AllowHostMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, allowed_hosts: list[str]):
        super().__init__(app)
        self.allowed_hosts = allowed_hosts

    async def dispatch(self, request: Request, call_next):
        origin = request.headers.get("origin")
        response = await call_next(request)

        if origin and any(origin.endswith(host) for host in self.allowed_hosts):
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
            response.headers["Access-Control-Allow-Headers"] = (
                "Content-Type, Authorization, X-Requested-With, Accept, Origin, Referer, "
                "User-Agent, Cache-Control, X-CSRF-Token, X-Auth-Token, Accept-Language, "
                "Accept-Encoding, Connection, DNT, Host, Keep-Alive, If-Modified-Since, "
                "Upgrade-Insecure-Requests"
            )
        return response


class TimeProcessMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, log):
        super().__init__(app)
        self.log = log

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        if str(response.status_code).startswith(('4', '5')):
            self.log.error(f"Response Status: {response.status_code} - {request.method} {request.url}")
        else:
            self.log.info(f"Response Status: {response.status_code} - {request.method} {request.url}")

        return response


class LoggingMiddleware1(BaseHTTPMiddleware):
    def __init__(self, app, log):
        super().__init__(app)
        self.log = log

    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)

            if str(response.status_code).startswith(('4', '5')):
                if isinstance(response, _StreamingResponse):
                    content = []
                    if hasattr(response.body_iterator, '__aiter__'):
                        async for chunk in response.body_iterator:
                            content.append(chunk)
                    else:
                        for chunk in response.body_iterator:
                            content.append(chunk)

                    async def content_iterator():
                        for chunk in content:
                            yield chunk

                    new_response = _StreamingResponse(
                        content_iterator(),
                        status_code=response.status_code,
                        headers=dict(response.headers),
                        media_type=response.media_type
                    )

                    try:
                        if isinstance(content[0], bytes):
                            json_content = json.loads(b''.join(content).decode())
                        else:
                            json_content = json.loads(''.join(content))
                        error_message = json_content.get('error', 'No error message available')
                        self.log.error(
                            f"Response Status: {response.status_code} - {request.method} {request.url} - Error Message: {error_message}"
                        )
                    except (json.JSONDecodeError, UnicodeDecodeError, IndexError):
                        self.log.error(
                            f"Response Status: {response.status_code} - {request.method} {request.url}"
                        )

                    return new_response

                elif isinstance(response, JSONResponse):
                    content = json.loads(response.body.decode())
                    error_message = content.get('error', 'No error message available')
                    self.log.error(
                        f"Response Status: {response.status_code} - {request.method} {request.url} - Error Message: {error_message}"
                    )
                    return response

                else:
                    self.log.error(
                        f"Response Status: {response.status_code} - {request.method} {request.url}"
                    )
                    return response


        except Exception as e:
            self.log.error(
                f"Middleware Error: {str(e)} - {request.method} {request.url}"
            )
            raise


class MaxBodySizeException(Exception):
    def __init__(self, body_len: str):
        self.body_len = body_len


class MaxBodySizeValidator:
    def __init__(self, max_size: int):
        self.body_len = 0
        self.max_size = max_size

    def __call__(self, chunk: bytes):
        self.body_len += len(chunk)
        if self.body_len > self.max_size:
            raise MaxBodySizeException(body_len=self.body_len)


class ResponseHeaderMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        assert scope["type"] == "http"
        headers = dict(scope["headers"])
        headers[b"x-request-id"] = b'1'
        scope["headers"] = [(k, v) for k, v in headers.items()]
        await self.app(scope, receive, send)


def add_middleware(application: FastAPI):
    middleware_list = [
        (CORSMiddleware, {
            "allow_origins": settings.BACKEND_CORS_ORIGINS,
            "allow_credentials": True,
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            "allow_headers": [
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "Accept",
                "Origin",
                "Referer",
                "User-Agent",
                "Cache-Control",
                "X-CSRF-Token",
                "X-Auth-Token",
                "Accept-Language",
                "Accept-Encoding",
                "Connection",
                "DNT",
                "Host",
                "Keep-Alive",
                "If-Modified-Since",
                "Upgrade-Insecure-Requests",
            ],
            "expose_headers": ["*"],
        }),
        (AllowHostMiddleware, {"allowed_hosts": settings.ALLOWED_HOSTS}),
        (RequestIDMiddleware, {}),
        (TimeProcessMiddleware, {}),
        (LoggingMiddleware, {"log": log}),
    ]

    for middleware, options in middleware_list:
        application.add_middleware(middleware, **options)