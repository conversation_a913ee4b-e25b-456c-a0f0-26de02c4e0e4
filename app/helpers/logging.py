import os
import logging
from logging import Filter
from logging.handlers import RotatingFileHandler
from app.helpers.config import settings


def setup_logging():
    logger = logging.getLogger("musashino-rag-be")
    logger.setLevel(logging.DEBUG)

    #TODO: Formatter
    formatter = logging.Formatter(
        "[%(asctime)s][%(name)s][%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    log_dir = getattr(settings, "LOGGING_DIR", None)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

        class InfoFilter(Filter):
            def filter(self, record):
                return record.levelno < logging.ERROR

        #TODO: File handler for INFO
        info_handler = RotatingFileHandler(
            f"{log_dir}/info.log", maxBytes=10 * 1024 * 1024, backupCount=20
        )
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(formatter)
        info_handler.addFilter(InfoFilter())

        #TODO: File handler for ERROR
        error_handler = RotatingFileHandler(
            f"{log_dir}/error.log", maxBytes=10 * 1024 * 1024, backupCount=20
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)

        #TODO: Prevent duplicate logs
        if not logger.hasHandlers():
            logger.addHandler(info_handler)
            logger.addHandler(error_handler)

    #TODO: Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(formatter)

    #TODO: Prevent duplicate logs
    if not logger.hasHandlers():
        logger.addHandler(console_handler)

    return logger

log = setup_logging()
