import os
import secrets
from dotenv import load_dotenv

BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
load_dotenv(os.path.join(BASE_DIR, '.env'))


class Settings:
    PROJECT_NAME = os.getenv('PROJECT_NAME', 'MRAG SYSTEM')
    SECRET_KEY = os.getenv('SECRET_KEY', str(secrets.token_urlsafe(64)))
    ENVIRONMENT = os.getenv('ENV', 'local')
    HOST_CLIENT_APP: str = os.getenv('HOST_CLIENT_APP', 'http://localhost:3000')
    PARTNER_SECRET_KEY = os.getenv('PARTNER_SECRET_KEY',
                                   'XAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9J1aWQiOiIiLCJjbGllbnRfaG9zdCI6IiIsImV4cCI6MTc1NTg1MTg4MH0ZRIB9kSM0pOTo7UXz2TIzsXUFa39YDWUSHQ2PlCKA')
    PARTNER_REFRESH_SECRET_KEY: str = "WQiOiIiLCJjbGllbnRfaG9zdCI6IiIsImV4cCsHTrKA9KHsM/WbBjgAC9r+3ecBGu+ZEsxnwYW/gfDUfIdlEtefxOp7MczFPUcqcykS7en0jU+cdoeBKaLH5FQNgCehOHUTSIKELSLLKWOIOSLWLKLW"
    API_PREFIX = '/v2/api'
    BACKEND_CORS_ORIGINS: list[str] = os.getenv('BACKEND_CORS_ORIGINS', ['*'])
    ALLOWED_HOSTS: list[str] = os.getenv('ALLOWED_HOSTS', ['*'])
    DATABASE_URL = os.getenv('DATABASE_URL', '')
    ACCESS_TOKEN_EXPIRE_SECONDS: int = 60 * 60 * 24 * 1
    REFRESH_TOKEN_EXPIRE_SECONDS: int = 60 * 60 * 24 * 7
    DEFAULT_EXPIRY: int = 60 * 60 * 10
    REQUEST_TIMEOUT: int = 60 * 5
    LOGGING_DIR = os.getenv('LOGGING_DIR', None)
    TIME_ZONE = 'Asia/Tokyo'
    DATE_TIME_FORMAT = '%d-%m-%Y'
    SECURITY_ALGORITHM = 'HS256'
    LOGGING_CONFIG_FILE = os.path.join(BASE_DIR, 'logging.ini')
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '')
    CLAUDE_API_KEY: str = os.getenv('CLAUDE_API_KEY', '')
    AZURE_API_KEY: str = os.getenv('AZURE_API_KEY', '')
    SENDER_EMAIL = os.getenv('SENDER_EMAIL', '')
    SENDER_EMAIL_PASSWORD = os.getenv('SENDER_EMAIL_PASSWORD', '')
    SMTP_SERVER = os.getenv('SMTP_SERVER', '')
    SMTP_PORT = os.getenv('SMTP_PORT', 587)
    STT_URL = os.getenv('STT_URL', 'https://api.talkpeak.com/quest/transcribe/inference')
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', None)
    BUCKET_NAME = os.getenv('BUCKET_NAME', 'musashino-storage-saved')
    CHROMA_SERVER_HOST = os.getenv('CHROMA_SERVER_HOST', '*************')
    CHROMA_SERVER_HTTP_PORT = os.getenv('CHROMA_SERVER_HTTP_PORT', 8004)
    PERSIST_DIRECTORY = os.getenv('PERSIST_DIRECTORY', 'vectordb')
    MAX_EMBEDDING_DIM = os.getenv('MAX_EMBEDDING_DIM', 768)
    EMBED_STORE = os.getenv('EMBED_STORE', 'mrag_collection')
    BIG_QUERY_LOGGING = os.getenv('BIG_QUERY_LOGGING', 'musashino-rag-dev.rag.action_log')
    BIG_QUERY_LOGGING_FULL = os.getenv('BIG_QUERY_LOGGING_FULL', 'musashino-rag-dev.rag.action_log_full')
    MAX_DATASTORES = os.getenv('MAX_DATASTORES', 1000)
    MAX_AGENT_BUILDERS = os.getenv('MAX_AGENT_BUILDERS', 1000)
    EXPIRATION_SECONDS = os.getenv('EXPIRATION_SECONDS', 3600)
    GCP_PROJECT_ID = os.getenv('GCP_PROJECT_ID', 'musashino-rag-dev')
    BUCKET_PREFIX = os.getenv('BUCKET_PREFIX', 'BUCKET_PREFIX')
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', None)
    MUSA_OPENAI_API_KEY = os.getenv('MUSA_OPENAI_API_KEY', 'sk-')
    OPENAI_MODEL_URL = os.getenv('OPENAI_MODEL_URL', 'https://api.openai.com/v1/models')


settings = Settings()
