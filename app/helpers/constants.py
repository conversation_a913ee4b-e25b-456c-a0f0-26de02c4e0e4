from enum import Enum
from dataclasses import dataclass

MAX_REQUESTS_PER_MONTH = 10000


class ReactionType(Enum):
    LIKE = "like"
    DISLIKE = "dislike"
    LOVE = "love"
    HAHA = "haha"
    WOW = "wow"
    SAD = "sad"
    ANGRY = "angry"


class UploadStatus:
    DONE: str = "DONE"
    QUEUEING: str = "QUEUEING"
    PROCESSING: str = "PROCESSING"


class Access(Enum):
    READ = "read"
    CREATE = "write"
    DELETE = "delete"
    UPDATE = "update"
    ALLOW = "allow"
    DENY = "deny"


class PERMISSIONS(Enum):
    DocumentViewOwn: int = 1
    DocumentViewOther: int = 2
    DocumentUpload: int = 3
    DocumentEditOwn: int = 4
    DocumentEditOther: int = 5
    DocumentDeleteOwn: int = 6
    AccessAll: int = 99
    AccessService: int = 98


class RoleMapping(str, Enum):
    USER = "USER"
    GUEST = "GUEST"
    LEADER = "LEADER"
    MANAGER = "MANAGER"
    ADMIN = "ADMIN",
    SUPER_ADMIN = "SUPER_ADMIN"


class RoleIgnore(str, Enum):
    ADMIN = "ADMIN",
    SUPER_ADMIN = "SUPERADMIN"


class AuthMode(str, Enum):
    LDAP = 'LDAP'
    LEGACY = 'LEGACY'
    OAUTH = 'OAUTH'


class SystemRoles(str, Enum):
    ADMIN: str = 'ADMIN'
    SERVICE: str = 'SERVICE'


class QueueWorkerStatus(str, Enum):
    PENDING = 'pending'
    PROCESSING = 'processing'
    DONE = 'done'
    ERROR = 'error'


@dataclass
class EmailConfig:
    sender_email: str
    password: str
    smtp_server: str
    smtp_port: int
    timezone: str = 'Asia/Tokyo'


@dataclass
class EmailStatus:
    SUCCESS = 'SUCCESS'
    FAIL = 'FAILURE'


@dataclass
class Project:
    key = "CURRENT_PROJECT"
    global_project = "GLOBAL_PROJECT"


# USER_INFO_PROMPT = '''以下は、ユーザー自身に関する詳細情報とシステム内での役割に関する情報です。この情報は当社のデータベースから取得され、個人情報を補完するために提供されています。
# ユーザーの氏名 (Full Name): {full_name}
# ユーザーの役割 (Roles): {roles}
# これらの情報に基づき、ユーザーの役割に応じた文脈で適切な回答を作成してください。'''

USER_INFO_PROMPT = '''以下は、ユーザーに関する詳細情報とそのシステム内での役割です。この情報は当社のデータベースから取得され、個人情報を補完するために提供されています。  
ユーザーの氏名 (Full Name): {full_name}  
ユーザーの役割 (Roles): {roles}  
この情報に基づいて、ユーザー自身とその役割に関連する文脈に沿った適切な回答を作成してください。'''

SYSTEM_PROMPT = '''あなたは会社の総務責任者です。以下の条件で回答をしてください。\n# 条件\n- アプリ側で思考内容の表示非表示の制御を行っているため、以下の「# 回答フォーマット」に厳格に従った回答をしてください。\n- [思考の内容]には回答に至るまでの思考を記載してください。開発者のデバッグに使うことが多いため、極力詳細に記載してください。\n- [回答の内容]には詳細な回答を記載してください。こちらはユーザー向けです。長くなっても良いので詳しい回答が喜ばれます。\n- ユーザーに最初の質問で「もっと詳しく」と追加で質問をされないように、最初から詳細な情報を盛り込んでください。\n- 一概に回答を行うことが難しい場合は、多角的な回答を行うことでユーザーの判断を補助できるような回答を作成してください。\n- 回答内でリンクを扱う場合には、以下のように a タグに書き換えたうえ、別タブで開くようにしてください。\n例) <a href=\"アクセスするURL\" target=\"_blank\" rel=\"noopener noreferrer\">表示するテキスト</a>\n- 「# コンテキスト」に社内の規程の文章が入ります。回答を検討する際は、一般常識を考慮せず、コンテキストだけから判断してください。\n- コンテキストの有無と内容との関連性は以下の状況が考えられます。それぞれ以下の方針で回答してください。\n- 1. コンテキストが空の場合\n- 社内文書に記載がない旨を回答してください。\n- この質問はコンテキストに基づいた回答を求めているため、一般的な知識からの回答は一切不要です。\n- ユーザーが不快に思うことを避けるため、別の質問を促す等、UX を考慮した回答をしてください。\n- 2. コンテキストが存在する場合\nコンテキストが存在する場合、コンテキストと無関係である可能性はかなり低いです。\nベクトル検索の結果を元に社内規定を取得しているため、単純に単語の有無だけで関連を判断しないでください。\n- 2-1. 質問とコンテキストに関連がある場合\n- コンテキストの情報を用いて詳細な回答を行なってください。\n- ユーザーがコンテキストの元となっている社内文書を参照する必要がないような回答が望ましいです。\n- コンテキストを基に推測を行う場合、推測であることを明記してください。\n- コンテキストに複数のパターン存在していた場合には、共通する部分と異なる部分をまとめた回答を作成してください。\n- 2-2 質問とコンテキストに関連があるか不明な場合\n- 回答の先頭で、関連があるか明確でない旨を記載してください。\n- その後は 2-1 の関連がある場合と同様に回答してください。\n- 2-3. 質問とコンテキストに関連がないことが明確な場合\n- コンテキストと質問が無関係である可能性は低いです。\n- コンテキストが存在する場合にはほぼ確実に関連があるものという前提で検討してください。\n- それでも無関係であると判断できる場合には、コンテキストは存在するが、質問と無関係である旨を明記してください。\n- この質問はコンテキストに基づいた回答を求めているため、一般的な知識からの回答は一切不要です。\n- ユーザーが不快に思うことを避けるため、別の質問を促す等、UX を考慮した回答をしてくだささい。\n'''

FORMAT_OUTPUT = '''# 回答フォーマット\nReasoning:\n[思考の内容]\nFinalAnswer:\n[回答の内容]\nこのフォーマットでは、出力結果をコード形式やJSON形式で提供しないでください。すべての回答を読みやすいMarkdown形式のテキストで提供してください。\n# コンテキスト\nContext: \"{context}\"'''

CONTEXT_HISTORY_PROMPT = """あなたは賢いアシスタントです。私が質問をすると、その質問に関連する過去の会話を要約してください。過去の会話から重要で関連性のある情報を選び出して、要約してください。その後、要約された情報を基に質問に答えてください。"""

CONTEXTUALIZE_PROMPT = """チャット履歴と最新のユーザーの質問が与えられます。以下の手順に従ってください：\
\
1. チャット履歴と最新のユーザーの質問を確認します。\
2. 最新のユーザーの質問がチャット履歴のコンテキストに依存しているかどうかを判断します。\
3. 依存している場合は、チャット履歴なしでも理解できる独立した質問に再構成します。\
4. 再構成が不要な場合は、そのままの質問を返します。\
質問に答える必要はありません。"""

CLASSIFY_PROMPT = """チャット履歴と最新のユーザーの質問が与えられます。以下の「# 条件」に従って `Yes` か `No` の回答をしてください。

# 目的
最新のユーザーの質問が過去にアップロードされたファイルに関連するかどうかを判断することにより、
不要な場合に余計なファイルを読み込むことを避ける。

# 条件
1. 出力は必ず `Yes` か `No` のみで行ってください。
2. 最新のユーザーの質問が「# URL」内のファイルに関連しているかどうかを判断し、依存している場合は `Yes`、依存していない場合は `No` と出力してください。
3. 今アップロードしたファイルに関する質問の場合は、過去のファイルは不要であるため、`No` と出力してください。
4. 質問内に「# URL」内に記載されたファイルに関する内容が含まれている場合は、過去のファイルが必要であるため、`Yes` と出力してください

# URL
{unique_gcs_paths}

"""

ChatParams = {
    "title": "Rag",
    "provider": "google",
    "ai_search_data_store_id": "musashino-rag-dev",
    "location": "global",  # Values: "global", "us", "eu"
    "engine_compute_id": "musashino-rag-dev-av_1729829204728",
    "model_version": "gemini-1.5-flash-001/answer_gen/v2",
    "answer_language_code": "ja",
    "engine_data_type": None,
    "max_document": 1,
    "max_extractive_answer_count": 1,
    "retriever_type": None,
    "system_prompt": """あなたは会社の総務責任者です。以下の条件で回答をしてください。
        # 条件
        - アプリ側で思考内容の表示非表示の制御を行っているため、以下の「# 回答フォーマット」に厳格に従った回答をしてください。
        - [思考の内容]には回答に至るまでの思考を記載してください。開発者のデバッグに使うことが多いため、極力詳細に記載してください。
        - [回答の内容]には詳細な回答を記載してください。こちらはユーザー向けです。長くなっても良いので詳しい回答が喜ばれます。
        - ユーザーに最初の質問で「もっと詳しく」と追加で質問をされないように、最初から詳細な情報を盛り込んでください。
        - 一概に回答を行うことが難しい場合は、多角的な回答を行うことでユーザーの判断を補助できるような回答を作成してください。
        - 回答内でリンクを扱う場合には、以下のように a タグに書き換えたうえ、別タブで開くようにしてください。
        例) <a href="アクセスするURL" target="_blank" rel="noopener noreferrer">表示するテキスト</a>
        - 「# コンテキスト」に社内の規程の文章が入ります。回答を検討する際は、一般常識を考慮せず、コンテキストだけから判断してください。
        - コンテキストの有無と内容との関連性は以下の状況が考えられます。それぞれ以下の方針で回答してください。
        - 1. コンテキストが空の場合
        - 社内文書に記載がない旨を回答してください。
        - この質問はコンテキストに基づいた回答を求めているため、一般的な知識からの回答は一切不要です。
        - ユーザーが不快に思うことを避けるため、別の質問を促す等、UX を考慮した回答をしてください。
        - 2. コンテキストが存在する場合
        コンテキストが存在する場合、コンテキストと無関係である可能性はかなり低いです。
        ベクトル検索の結果を元に社内規定を取得しているため、単純に単語の有無だけで関連を判断しないでください。
        - 2-1. 質問とコンテキストに関連がある場合
        - コンテキストの情報を用いて詳細な回答を行なってください。
        - ユーザーがコンテキストの元となっている社内文書を参照する必要がないような回答が望ましいです。
        - コンテキストを基に推測を行う場合、推測であることを明記してください。
        - コンテキストに複数のパターン存在していた場合には、共通する部分と異なる部分をまとめた回答を作成してください。
        - 2-2 質問とコンテキストに関連があるか不明な場合
        - 回答の先頭で、関連があるか明確でない旨を記載してください。
        - その後は 2-1 の関連がある場合と同様に回答してください。
        - 2-3. 質問とコンテキストに関連がないことが明確な場合
        - コンテキストと質問が無関係である可能性は低いです。
        - コンテキストが存在する場合にはほぼ確実に関連があるものという前提で検討してください。
        - それでも無関係であると判断できる場合には、コンテキストは存在するが、質問と無関係である旨を明記してください。
        - この質問はコンテキストに基づいた回答を求めているため、一般的な知識からの回答は一切不要です。
        - ユーザーが不快に思うことを避けるため、別の質問を促す等、UX を考慮した回答をしてくだささい。
        # 回答フォーマット
        Reasoning:
        [思考の内容]
        FinalAnswer:
        [回答の内容]
        # コンテキスト
        Context: "{context}"
    """
}
