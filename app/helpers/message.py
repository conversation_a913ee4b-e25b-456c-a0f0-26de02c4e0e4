from app.schemas.base import Message

MESSAGES = {
    "bad_request": Message(text={
        "en": "Bad Request.",
        "jp": "要求の形式が正しくありません。"
    }),
    "user_exists": Message(text={
        "en": "User information already exists!",
        "jp": "ユーザー情報はすでに存在します。"
    }),
    "invalid_email": Message(text={
        "en": "Invalid email address.",
        "jp": "無効なメールアドレスです。"
    }),
    "password_too_weak": Message(text={
        "en": "The password is too weak.",
        "jp": "パスワードが弱すぎます。"
    }),
    "password_incorrect": Message(text={
        "en": "Confirm password incorrect!",
        "jp": "パスワードが間違っていることを確認してください!"
    }),
    "your_account_incorrect": Message(text={
        "en": "Your account information is incorrect.",
        "jp": "アカウント情報が正しくありません。"
    }),
    "active_successfully": Message(text={
        "en": "Active successfully!",
        "jp": "無事にアクティブになりました！"
    }),
    "user_not_found": Message(text={
        "en": "User not found.",
        "jp": "ユーザーが見つかりません。"
    }),
    "de_active_successfully": Message(text={
        "en": "Deactivated successfully!",
        "jp": "無効化に成功しました!"
    }),
    "something_wrong": Message(text={
        "en": "Something went wrong.",
        "jp": "問題が発生しました。"
    }),
    "no_results_found": Message(text={
        "en": "No results found!",
        "jp": "結果が見つかりませんでした!"
    }),
    "role_not_found": Message(text={
        "en": "Role not found !",
        "jp": "役割が見つかりません!"
    }),
    "group_not_found": Message(text={
        "en": "Group not found!",
        "jp": "グループが見つかりません!"
    }),
    "validate_file_excel": Message(text={
        "en": "Only  supports Excel files (.xlsx or .xls).",
        "jp": "Excel ファイル (.xlsx または .xls) のみをサポートします。"
    }),
    "validate_number_row": Message(text={
        "en": "Number of rows in sheets do not match.",
        "jp": "シートの行数が一致しません。"
    }),
    "validate_file_csv": Message(text={
        "en": "Only supports CSV files.",
        "jp": "CSV ファイルのみをサポートします。"
    }),
    "validate_file_csv_header": Message(text={
        "en": "Invalid CSV file headers.",
        "jp": "CSV ファイル ヘッダーが無効です。"
    }),
    "user_exist_role": Message(text={
        "en": "User already exists in role!",
        "jp": "ユーザーはすでにロールに存在します。"
    }),
    "user_exist_group": Message(text={
        "en": "User already exists in group!",
        "jp": "ユーザーはすでにグループに存在します!"
    }),
    "role_exist": Message(text={
        "en": "Role already exists.",
        "jp": "ロールはすでに存在します。"
    }),
    "permission_exist": Message(text={
        "en": "Permission name already exists!",
        "jp": "権限名はすでに存在します!"
    }),
    "group_exist": Message(text={
        "en": "Group name already exists",
        "jp": "グループ名はすでに存在します!"
    }),
    "use_case_not_found": Message(text={
        "en": "Usecase does not exist!",
        "jp": "ユースケースが存在しません!"
    }),
    "usecase_exist_group": Message(text={
        "en": "Usecase already exists in group!",
        "jp": "ユースケースはすでにグループに存在します!"
    }),
    "permission_exist_group": Message(text={
        "en": "Already exists in group!",
        "jp": "すでにグループ内に存在しています!"
    }),
    "logo_type_required": Message(text={
        "en": "Logo type is required !",
        "jp": "ロゴタイプは必須です!"
    }),
    "logo_type_invalid": Message(text={
        "en": "Invalid logo type !",
        "jp": "ロゴタイプが無効です!"
    }),
    "logo_file_required": Message(text={
        "en": "Logo file is required !",
        "jp": "ロゴファイルが必要です!"
    }),
    "logo_not_found": Message(text={
        "en": "Logo not found !",
        "jp": "ロゴが見つかりません!"
    }),
    "admin_privileges": Message(text={
        "en": "You cannot log in because you do not have super admin or admin privileges.",
        "jp": "スーパー管理者または管理者権限がないため、ログインできません。"
    }),
    "account_incorrect": Message(text={
        "en": "Your account information is incorrect.",
        "jp": "アカウント情報が正しくありません。"
    }),
    "account_deleted": Message(text={
        "en": "This account has been deleted. If you wish to use it again, please contact the Musashino system administrator.",
        "jp": "削除済みのアカウントです。再度利用する際は、武蔵野システム管理者に問い合わせをお願いします。"
    }),
    "error_auth_server": Message(text={
        "en": "Error connecting to authentication server.",
        "jp": "認証サーバーへの接続中にエラーが発生しました。"
    }),
    "error_logout": Message(text={
        "en": "An error occurred during logout.",
        "jp": "ログアウト中にエラーが発生しました。"
    }),
    "password_not_created": Message(text={
        "en": "Password not created!",
        "jp": "パスワードが作成されませんでした。"
    }),
    "confirm_incorrect": Message(text={
        "en": "Confirm incorrect information!",
        "jp": "間違った情報を確認しました！"
    }),
    "new_pass_not_old_pass": Message(text={
        "en": "New password cannot be the same as old password!",
        "jp": "新しいパスワードは古いパスワードと同じであってはなりません。"
    }),
    "password_update_success": Message(text={
        "en": "Password updated successfully!",
        "jp": "パスワードが正常に更新されました。"
    }),
    "email_not_found": Message(text={
        "en": "Email is incorrect or does not exist in the system.",
        "jp": "電子メールが間違っているか、システム上に存在しません。"
    }),
    "email_send": Message(text={
        "en": "Request information has been sent to your email.",
        "jp": "要求された情報があなたの電子メールに送信されました。"
    }),
    "invalid_token": Message(text={
        "en": "Token is invalid.",
        "jp": "トークンが無効です。"
    }),
    "password_not_confirm_password": Message(text={
        "en": "Password and confirm password do not match.",
        "jp": "パスワードと確認用パスワードが一致しません。"
    }),
    "database_error": Message(text={
        "en": "Database Error.",
        "jp": "データベース エラー。"
    }),
    "chatroom_name_exist": Message(text={
        "en": "Chatroom name already exists!",
        "jp": "チャットルーム名はすでに存在します。"
    }),
    "datasource_exist": Message(text={
        "en": "Datasource has existed !",
        "jp": "データソースが存在しました!"
    }),
    "usecase_exist": Message(text={
        "en": "Usecase already exists!",
        "jp": "ユースケースはすでに存在します!"
    }),
    "usecase_title_exist": Message(text={
        "en": "Usecase title already exists!",
        "jp": "ユースケースタイトルはすでに存在します!"
    }),
    "old_password_incorrect": Message(text={
        "en": "Old password is incorrect!",
        "jp": "古いパスワードが間違っています!"
    }),
    "bucket_exist": Message(text={
        "en": "Bucket already exists.",
        "jp": "バケットはすでに存在します。"
    }),
    "bucket_set_status": Message(text={
        "en": "Bucket already has uniform access control enabled.",
        "jp": "バケットでは、統一アクセス制御がすでに有効になっています。"
    }),
    "file_size_exceeds": Message(text={
        "en": "File size exceeds the 2048MB limit.",
        "jp": "ファイルサイズが5MBの制限を超えています。"
    }),
    "unsupported_file_type": Message(text={
        "en": "Unsupported file type. Only PDF and DOCX are allowed.",
        "jp": "サポートされていないファイル形式です。PDFとDOCXのみが許可されています。"
    }),
    "company_exist": Message(text={
        "en": "Duplicate company or unique slug.",
        "jp": "重複した会社名または固有のスラッグです。"
    }),
    "record_exist": Message(text={
        "en": "Record already exists!",
        "jp": "レコードはすでに存在します!"
    }),
    "user_exceed": Message(text={
        "en": "You cannot register because the maximum number of users has been exceeded.",
        "jp": "最大ユーザー数を超えているため、登録できません。"
    }),
    "package_not_exist": Message(text={
        "en": "Service package does not exist.",
        "jp": "サービス パッケージが存在しません。"
    }),
    "package_exist": Message(text={
        "en": "Service package already exists.",
        "jp": "サービス パッケージは既に存在します。"
    }),
    "has_company_registered": Message(text={
        "en": "This service package has been registered by a company and cannot be deleted.",
        "jp": "このサービス パッケージは企業によって登録されているため、削除できません。"
    }),
    "permission_not_exist": Message(text={
        "en": "Permission not found.",
        "jp": "権限が見つかりません。"
    }),
    "chatroom_code_required": Message(text={
        "en": "chatroom_code is required to query documents.",
        "jp": "ドキュメントをクエリするには chatroom_code が必要です。"
    }),
    "admin_exceed": Message(text={
        "en": "You can only create one admin per company.",
        "jp": "会社ごとに管理者を 1 人だけ作成できます。"
    }),
    "max_user_exceed": Message(text={
        "en": "You must deactivate other accounts, currently the number of active accounts is more than the number of users allowed by the package.",
        "jp": "他のアカウントを非アクティブ化する必要があります。現在、アクティブなアカウントの数は、パッケージで許可されているユーザー数を超えています。"
    }),
    "max_file_upload": Message(text={
        "en": "Only allow uploading 1 file at a time.",
        "jp": "一度にアップロードできるファイルは 1 つだけです。"
    }),
    "start_date_must_be_less_than_end_date": Message(text={
        "en": "The start date cannot be greater than the end date.",
        "jp": "開始日は終了日より後には設定できません。"
    }),
    "user_privileges": Message(text={
        "en": "You are not authorized to access this page.",
        "jp": "このページにアクセスする権限がありません。"
    }),
    "end_date_must_be_greater_than_current_date": Message(text={
        "en": "The end date cannot be less than the current date.",
        "jp": "終了日は現在の日付より前にすることはできません。"
    }),
    "cannot_create_this_role": Message(text={
        "en": "You cannot name this role, as it is a system role.",
        "jp": "このロールはシステム ロールであるため、名前を付けることはできません。"
    }),
    "duplicate_folder": Message(text={
        "en": "Cannot create a new folder that duplicates an existing folder.",
        "jp": "既存のフォルダーを複製する新しいフォルダーを作成することはできません。"
    }),
    "error_trigger_file": Message(text={
        "en": "This folder does not have file to trigger.",
        "jp": "このフォルダーにはトリガーするファイルがありません。"
    }),
    "start_date_must_be_greater_than_current_date": Message(text={
        "en": "The start date cannot be less than the current date.",
        "jp": "開始日は現在の日付より前にすることはできません。"
    }),
    "old_password_need_doesnt_match_new_password": Message(text={
        "en": "The new password cannot be the same as the old password.",
        "jp": "新しいパスワードは古いパスワードと同じにすることはできません。"
    }),
    "slug_too_long": Message(text={
        "en": "Company slug must not exceed 30 characters.",
        "jp": "会社のスラッグは 30 文字を超えてはなりません。"
    }),
    "user_cannot_created": Message(text={
        "en": "New users cannot be created because the company is shutting down.",
        "jp": "会社が閉鎖されるため、新しいユーザーを作成できません。"
    }),
    "company_not_found": Message(text={
        "en": "Company not found or company has ceased operations.",
        "jp": "会社が見つからないか、または会社が営業を停止しています。"
    }),
    "change_status_success": Message(text={
        "en": "Notification status change successful.",
        "jp": "通知ステータスの変更に成功しました。"
    }),
    "notification_not_found": Message(text={
        "en": "The notice has been deleted or does not exist.",
        "jp": "通知は削除されたか、存在しません。"
    }),
    "package_not_found": Message(text={
        "en": "Package has been deleted or does not exist.",
        "jp": "パッケージは削除されたか、存在しません。"
    }),
    "invalid_file_name": Message(text={
        "en": "The file name is incorrect compared to the system or is not in the required format.",
        "jp": "ファイル名がシステムと比較して間違っているか、必要な形式ではありません。"
    }),
    "max_datastore_exceed": Message(text={
        "en": "Datastore creation limit exceeded.",
        "jp": "データストアの作成制限を超えました。"
    }),
    "max_agent_builder_exceed": Message(text={
        "en": "Agent Builder creation limit exceeded.",
        "jp": "エージェント ビルダーの作成制限を超えました。"
    }),
    "required_import_users": Message(text={
        "en": "Please set the Active field to blank or 1.",
        "jp": "アクティブ欄には、空白または1を設定してください。"
    }),
    "max_datastore_less": Message(text={
        "en": "Do not edit the package's data store count to be less than the created data store count.",
        "jp": "パッケージのデータ ストア数を、作成されたデータ ストア数より少なく編集しないでください。"
    }),
    "max_agent_builder_less": Message(text={
        "en": "Do not edit the package's agent builder count to be less than the created agent builder count.",
        "jp": "パッケージのエージェント ビルダー数を、作成されたエージェント ビルダー数より少なく編集しないでください。"
    }),
    "gcp_project_not_found": Message(text={
        "en": "No projects found.",
        "jp": "プロジェクトが見つかりません。"
    }),
    "project_not_found_on_gcp": Message(text={
        "en": "No projects found on GCP.",
        "jp": "GCP にプロジェクトが見つかりません。"
    }),
    "config_not_found": Message(text={
        "en": "Configuration not found or can not delete.",
        "jp": "構成が見つからないか、削除できません。"
    }),
    "not_allow_downgrade_important": Message(text={
        "en": "Do not change the importance level of this record.",
        "jp": "このレコードの重要度レベルを変更しないでください。"
    }),
    "not_allow_change_key_for_important": Message(text={
        "en": "Do not change the config key for this record.",
        "jp": "このレコードの設定キーを変更しないでください。"
    }),
    "password_not_strong": Message(text={
        "en": "Password is not strong enough, please set a stronger password.",
        "jp": "パスワードの強度が十分ではありません。より強力なパスワードを設定してください。"
    }),
    "expired_token": Message(text={
        "en": "Token is expired.",
        "jp": "トークンの有効期限が切れています。"
    }),
    "email_password_incorrect": Message(text={
        "en": "The email address or password does not match.",
        "jp": "メールアドレスまたはパスワードが一致しません。"
    }),

    "provider_is_used": Message(text={
        "en": "Provider is in use, cannot be deleted, if deleted will affect existing conversations.",
        "jp": "プロバイダーは使用中なので削除できません。削除すると既存の会話に影響します。"
    }),

    "provider_id_required": Message(text={
        "en": "provider cannot be left blank.",
        "jp": "プロバイダーを空白のままにすることはできません。"
    }),

    "provider_not_valid": Message(text={
        "en": "Provider does not exist or is not active.",
        "jp": "プロバイダーが存在しないか、アクティブではありません。"
    }),

    "invalid_open_ai_key": Message(text={
        "en": "Openai Key is incorrect, please check.",
        "jp": "OpenAIキーが正しくありません。チェックしてください。"
    }),

}
