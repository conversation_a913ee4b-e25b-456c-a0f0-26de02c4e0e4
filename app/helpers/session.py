from requests import Session
from requests.adapters import HTT<PERSON>dapter, <PERSON><PERSON>


def requests_retry_session(retries=3, backoff_factor=3, status_forcelist=(500, 502, 504, 503, 429), session=None):
    session = session or Session()

    retry_strategy = Retry(
        total=retries,
        read=retries,
        connect=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session
