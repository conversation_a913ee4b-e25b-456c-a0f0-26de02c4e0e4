from __future__ import annotations
import enum
from typing import ClassVar, Union, List, Type, Dict
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException


__all__ = ["add_exception_handlers", "GatewayException"]


class BaseAPIException(Exception):
    status_code: int = 500
    detail: Union[str, List[str]] = "An unexpected error occurred."

    def __init__(self, detail: Union[str, List[str]] = None, status_code: int = None):
        if detail:
            self.detail = detail
        if status_code:
            self.status_code = status_code

    def to_response(self) -> Dict:
        return {
            "status_code": self.status_code,
            "message": self.detail,
            "success": False
        }


class AuthJWTException(Exception):
    pass


class InvalidHeaderError(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class JWTDecodeError(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class CSRFError(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class MissingTokenError(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class RevokedTokenError(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class AccessTokenRequired(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class RefreshTokenRequired(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class FreshTokenRequired(AuthJWTException):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message


class ExceptionType(enum.Enum):
    MS_UNAVAILABLE = 500, '990', 'Hệ thống đang bảo trì, quý khách vui lòng thử lại sau'
    MS_INVALID_API_PATH = 500, '991', 'Hệ thống đang bảo trì, quý khách vui lòng thử lại sau'
    DATA_RESPONSE_MALFORMED = 500, '992', 'Có lỗi xảy ra, vui lòng liên hệ admin!'

    def __new__(cls, *args, **kwds):
        value = len(cls.__members__) + 1
        obj = object.__new__(cls)
        obj._value_ = value
        return obj

    def __init__(self, http_code, code, message):
        self.http_code = http_code
        self.code = code
        self.message = message


class RemoteException(Exception):
    def __init__(self, exception, traceback):
        self.exception = exception
        self.traceback = traceback

    def __str__(self):
        return str(self.exception) + "\n\nTraceback\n---------\n" + self.traceback

    def __dir__(self):
        return sorted(set(dir(type(self)) + list(self.__dict__) + dir(self.exception)))

    def __getattr__(self, key):
        try:
            return object.__getattribute__(self, key)
        except AttributeError:
            return getattr(self.exception, key)


class CustomException(Exception):
    http_code: int
    code: str
    message: str

    def __init__(self, http_code: int = None, code: str = None, message: str = None):
        self.http_code = http_code if http_code else 500
        self.code = code if code else str(self.http_code)
        self.message = message


class ServiceException(Exception):
    def __init__(self, status_code: int, detail: Union[str, List[str]]):
        self.status_code = status_code
        self.detail = detail


class GatewayException(BaseAPIException):
    def __init__(self, status_code: int, detail: Union[str, List[str]]):
        self.status_code = status_code
        self.detail = detail


class NotFoundException(BaseAPIException):
    def __init__(self, detail: Union[str, List[str]] = "Resource not found"):
        super().__init__(status_code=404, detail=detail)

class ValidationException(BaseAPIException):
    def __init__(self, detail: Union[str, List[str]] = "Validation error"):
        super().__init__(status_code=400, detail=detail)



exceptions_registry: dict[Type[Exception], Type[BaseAPIException]] = {
    GatewayException: GatewayException,

}


def add_exception_handlers(application: FastAPI):
    @application.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return JSONResponse(
            status_code=400,
            content={
                "statusCode": 400,
                "status": False,
                "error": [exc.errors()] if isinstance(exc.errors(), str) else exc.errors(),
                "data": None
            }
        )

    @application.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "statusCode": exc.status_code,
                "status": False,
                "error": [exc.detail] if isinstance(exc.detail, str) else exc.detail,
                "data": None
            }
        )

    @application.exception_handler(GatewayException)
    async def service_exception_handler(request: Request, exc: GatewayException):
        custom_exception = exceptions_registry.get(type(exc), BaseAPIException)(status_code=exc.status_code, detail=exc.detail)

        return JSONResponse(
            status_code=custom_exception.status_code,
            content={
                "statusCode": custom_exception.status_code,
                "status": False,
                "error": [custom_exception.detail] if isinstance(custom_exception.detail,
                                                                 str) else custom_exception.detail,
                "data": None
            }
        )