import logging
from typing import Annotated, List, Any, Generator, Dict
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, status, Request, Response, HTTPException

logger = logging.getLogger()
router = APIRouter()


class ConnectionManager:
    def __init__(self):
        self.clients: dict[int, dict[str, WebSocket]] = {}

    async def connect(self, websocket, customer_id, device_hash: str | None = None):
        client_connections = self.clients.get(customer_id)
        if client_connections:
            client_device_connection = client_connections.get(device_hash)
            if client_device_connection:
                await websocket.close(code=1008, reason="Another connection with the same user_id already exists.")
                return
        else:
            self.clients[customer_id] = {}

        await websocket.accept()
        websocket.customer_id = customer_id
        websocket.device_hash = device_hash

        self.clients[customer_id][device_hash] = websocket
        print(list(self.clients.values()))

    async def disconnect(self, websocket):
        customer_id = getattr(websocket, 'customer_id', None)
        device_hash = getattr(websocket, 'device_hash', None)
        client_connections = self.clients.get(customer_id)
        if client_connections:
            if client_connections.get(device_hash):
                client_connections.pop(device_hash)

    async def broadcast(self, message):
        for client_connections in self.clients.values():
            for websocket in client_connections.values():
                await websocket.send_json(message)

    async def broadcast_to_customer(self, customer_id, message):
        client_connections = self.clients.get(customer_id)
        if client_connections:
            for connection in client_connections.values():
                await connection.send_json(message)


connection_manager = ConnectionManager()


class WSRoute:

    def __init__(self, websocket: WebSocket):
        self._websocket = websocket

    def __await__(self) -> Generator:
        return self.dispatch().__await__()

    async def dispatch(self) -> None:
        # Websocket lifecycle
        await self._on_connect()

        close_code: int = status.WS_1000_NORMAL_CLOSURE
        try:
            while True:
                data = await self._websocket.receive_text()
                await self._on_receive(data)
        except WebSocketDisconnect:
            # Handle client normal disconnect here
            pass
        except Exception as exc:
            # Handle other types of errors here
            close_code = status.WS_1011_INTERNAL_ERROR
            raise exc from None
        finally:
            await self._on_disconnect(close_code)

    async def _on_connect(self):
        # Handle your new connection here
        await self._websocket.accept()
        pass

    async def _on_disconnect(self, close_code: int):
        # Handle client disconnect here
        pass

    async def _on_receive(self, msg: Any):
        # Handle client messaging here
        pass
