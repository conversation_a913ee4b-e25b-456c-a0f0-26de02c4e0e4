from dataclasses import dataclass
from typing import ClassVar
from app.schemas.base import GrantEndpoint, Access


@dataclass
class AccessEndpoint:
    CHAT_AGENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Allow Chat',
        description='Allow the use of the Chat Agent feature',
        endpoint_name='chat_agent_completions',
        endpoint_path='/chat/completions',
        component='chat',
        grant_type=[Access.ALLOW]
    )
    CREATE_CHAT_HISTORY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Chat History',
        description=None,
        endpoint_name='create_chat_history',
        endpoint_path='/chat_hists',
        grant_type=[Access.CREATE]
    )

    # TODO: Users Endpoint
    CREATE_USER_ADMIN: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create User Admin',
        description='Allow the creation of a new user with admin role',
        endpoint_name="create_admin_user",
        endpoint_path="/users/admin",
        grant_type=[Access.CREATE]
    )
    CREATE_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create User',
        description='Allow the creation of a new user',
        endpoint_name="create_user",
        endpoint_path="/users",
        grant_type=[Access.CREATE]
    )
    UPDATE_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update User',
        description='Allow the update of user information',
        endpoint_name="update_user",
        endpoint_path="/users/{user_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete User',
        description='Allow the deletion of a user',
        endpoint_name="delete_user",
        endpoint_path="/users",
        grant_type=[Access.ALLOW]
    )
    GET_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User',
        description='Allow the retrieval of user information',
        endpoint_name="get_user",
        endpoint_path="/users/{user_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USERS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Users',
        description='Allow the retrieval of users information',
        endpoint_name="get_all_users",
        endpoint_path="/users",
        grant_type=[Access.ALLOW]
    )
    ACTIVE_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Active User',
        description='Allow the activation of a user',
        endpoint_name="active_user",
        endpoint_path="/user/{user_id}/active",
        grant_type=[Access.ALLOW]
    )
    DE_ACTIVE_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='De-Active User',
        description='Allow the deactivation of a user',
        endpoint_name="deactive_user",
        endpoint_path="/user/{user_id}/deactive",
        grant_type=[Access.ALLOW]
    )
    GET_YOUR_INFORMATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Your Information',
        description='Allow the retrieval of your information',
        endpoint_name="your_information",
        endpoint_path="/user/me",
        grant_type=[Access.ALLOW]
    )
    UPDATE_PROFILE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Your Information',
        description='Allow the update of your information',
        endpoint_name="update_profile",
        endpoint_path="/users/me/update",
        grant_type=[Access.ALLOW]
    )
    DOWNLOAD_TEMPLATE_CREATE_USERS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Download Template Create Users',
        description='Allow the download of the template for creating users',
        endpoint_name="download_template_create_users",
        endpoint_path="/user/download-template-create-users",
        grant_type=[Access.ALLOW]
    )
    DOWNLOAD_TEMPLATE_UPDATE_USERS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Download Template Update Users',
        description='Allow the download of the template for updating users',
        endpoint_name="download_template_update_users",
        endpoint_path="/user/download-template-update-users",
        grant_type=[Access.ALLOW]
    )
    UPLOAD_USERS_EXCEL: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Upload Users Excel',
        description='Allow the upload of users excel file',
        endpoint_name="upload_users_excel",
        endpoint_path="/user/upload-file-users",
        grant_type=[Access.ALLOW]
    )
    SYNC_USER_ZOHO: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Sync User Zoho',
        description='Allow the sync of users from Zoho',
        endpoint_name="sync_users_zoho",
        endpoint_path="/user/sync-users-zoho",
        grant_type=[Access.ALLOW]
    )
    DOWNLOAD_CSV_TEMPLATE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Download CSV Template',
        description='Allow the download of the template for creating users',
        endpoint_name="download_csv_template",
        endpoint_path="/user/download-csv-template",
        grant_type=[Access.ALLOW]
    )
    DOWNLOAD_TO_CSV: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Download to CSV',
        description='Allow the download of the users to CSV',
        endpoint_name="download_to_csv",
        endpoint_path="/user/download-csv",
        grant_type=[Access.ALLOW]
    )
    UPLOAD_BY_CSV: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Upload by CSV',
        description='Allow the upload of users by CSV',
        endpoint_name="upload_by_csv",
        endpoint_path="/user/upload-csv",
        grant_type=[Access.ALLOW]
    )
    SET_PASSWORD_SUPER_ADMIN: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Set Password Super Admin',
        description='Allow the set password for super admin',
        endpoint_name="set_password_super_admin",
        endpoint_path="/user/set-password-super-admin",
        grant_type=[Access.ALLOW]
    )
    SET_PASSWORD_SEMINAR: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Set Password Seminar',
        description='Allow the set password for seminar',
        endpoint_name="set_password_seminar",
        endpoint_path="/user/set-password-seminar",
        grant_type=[Access.ALLOW]
    )
    GET_USERS_DELETED: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Users Deleted',
        description='Allow the retrieval of users deleted',
        endpoint_name="get_all_users_deleted",
        endpoint_path="/users-deleted",
        grant_type=[Access.ALLOW]
    )
    ACTIVE_USER_DELETED: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Active User Deleted',
        description='Allow the activation of a user deleted',
        endpoint_name="active_user_deleted",
        endpoint_path="/active-user-deleted",
        grant_type=[Access.ALLOW]
    )

    # TODO: Roles Endpoint
    CREATE_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Role',
        description='Allow the creation of a new role',
        endpoint_name="create_role",
        endpoint_path="/roles",
        grant_type=[Access.ALLOW]
    )
    UPDATE_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Role',
        description='Allow the update of role information',
        endpoint_name="update_role",
        endpoint_path="/roles/{role_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Role',
        description='Allow the deletion of a role',
        endpoint_name="delete_role",
        endpoint_path="/roles/{role_id}",
        grant_type=[Access.ALLOW]
    )
    GET_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Role',
        description='Allow the retrieval of role information',
        endpoint_name="get_role",
        endpoint_path="/roles/{role_id}",
        grant_type=[Access.ALLOW]
    )
    GET_ROLES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Roles',
        description='Allow the retrieval of roles information',
        endpoint_name="get_all_roles",
        endpoint_path="/roles",
        grant_type=[Access.ALLOW]
    )

    # TODO: UserRoles Endpoint
    CREATE_USER_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create User Role',
        description='Allow the creation of a new user role',
        endpoint_name="create_permission",
        endpoint_path="/user/roles",
        grant_type=[Access.ALLOW]
    )
    UPDATE_USER_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update User Role',
        description='Allow the update of user role information',
        endpoint_name="update_permissions",
        endpoint_path="/user/roles/{u_role_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_USER_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete User Role',
        description='Allow the deletion of a user role',
        endpoint_name="delete_user_role",
        endpoint_path="/user/roles/{u_role_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_ROLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User Role',
        description='Allow the retrieval of user role information',
        endpoint_name="get_permissions",
        endpoint_path="/user/roles/{u_role_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_ROLES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User Roles',
        description='Allow the retrieval of user roles information',
        endpoint_name="get_all_permissions",
        endpoint_path="/user/roles",
        grant_type=[Access.ALLOW]
    )

    # TODO: Groups Endpoint
    CREATE_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Group',
        description='Allow the creation of a new group',
        endpoint_name="create_group",
        endpoint_path="/groups",
        grant_type=[Access.ALLOW]
    )
    UPDATE_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Group',
        description='Allow the update of group information',
        endpoint_name="update_groups",
        endpoint_path="/groups/{group_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Group',
        description='Allow the deletion of a group',
        endpoint_name="delete_groups",
        endpoint_path="/groups/{group_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Group',
        description='Allow the retrieval of group information',
        endpoint_name="get_group",
        endpoint_path="/groups/{group_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUPS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Groups',
        description='Allow the retrieval of groups information',
        endpoint_name="get_all_groups",
        endpoint_path="/groups",
        grant_type=[Access.ALLOW]
    )

    # TODO: UserGroups Endpoint
    CREATE_USER_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create User Group',
        description='Allow the creation of a new user group',
        endpoint_name="create_user_group",
        endpoint_path="/user/groups",
        grant_type=[Access.ALLOW]
    )
    UPDATE_USER_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update User Group',
        description='Allow the update of user group information',
        endpoint_name="update_user_group",
        endpoint_path="/user/group/{u_group_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_USER_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete User Group',
        description='Allow the deletion of a user group',
        endpoint_name="delete_user_group",
        endpoint_path="/user/group/{u_group_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_GROUP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User Group',
        description='Allow the retrieval of user group information',
        endpoint_name="get_user_group",
        endpoint_path="/user/group/{u_group_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_GROUPS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User Groups',
        description='Allow the retrieval of user groups information',
        endpoint_name="get_all_user_group",
        endpoint_path="/user/groups",
        grant_type=[Access.ALLOW]
    )

    # TODO: Permission Endpoint
    CREATE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Permission',
        description='Allow the creation of a new permission',
        endpoint_name="create_permission",
        endpoint_path="/permissions",
        grant_type=[Access.ALLOW]
    )
    UPDATE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Permission',
        description='Allow the update of permission information',
        endpoint_name="update_permissions",
        endpoint_path="/permissions/{permission_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Permission',
        description='Allow the deletion of a permission',
        endpoint_name="delete_permissions",
        endpoint_path="/permissions/{permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Permission',
        description='Allow the retrieval of permission information',
        endpoint_name="get_permissions",
        endpoint_path="/permissions/{permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_PERMISSIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Permissions',
        description='Allow the retrieval of permissions information',
        endpoint_name="get_all_permissions",
        endpoint_path="/permissions",
        grant_type=[Access.ALLOW]
    )

    # TODO: Groups Permission Endpoint
    CREATE_GROUP_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Group Permission',
        description='Allow the creation of a new group permission',
        endpoint_name="create_permission",
        endpoint_path="/group/permissions",
        grant_type=[Access.ALLOW]
    )
    UPDATE_GROUP_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Group Permission',
        description='Allow the update of group permission information',
        endpoint_name="update_permissions",
        endpoint_path="/group/permissions/{g_permission_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_GROUP_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Group Permission',
        description='Allow the deletion of a group permission',
        endpoint_name="delete_permissions",
        endpoint_path="/group/permissions/{g_permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUP_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Group Permission',
        description='Allow the retrieval of group permission information',
        endpoint_name="get_permissions",
        endpoint_path="/group/permissions/{g_permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUP_PERMISSIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Group Permissions',
        description='Allow the retrieval of group permissions information',
        endpoint_name="get_all_permissions",
        endpoint_path="/group/permissions",
        grant_type=[Access.ALLOW]
    )

    # TODO: Groups Usecase Endpoint
    CREATE_GROUP_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Group Usecase',
        description='Allow the creation of a new group usecase',
        endpoint_name="create_group_usecase",
        endpoint_path="/group/usecases",
        grant_type=[Access.ALLOW]
    )
    UPDATE_GROUP_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Group Usecase',
        description='Allow the update of group usecase information',
        endpoint_name="update_group_usecase",
        endpoint_path="/group/usecases/{g_usecase_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_GROUP_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Group Usecase',
        description='Allow the deletion of a group usecase',
        endpoint_name="delete_group_usecase",
        endpoint_path="/group/usecases/{g_usecase_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUP_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Group Usecase',
        description='Allow the retrieval of group usecase information',
        endpoint_name="get_group_usecase",
        endpoint_path="/group/usecases/{g_usecase_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GROUP_USECASES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Group Usecases',
        description='Allow the retrieval of group usecases information',
        endpoint_name="get_all_group_usecases",
        endpoint_path="/group/usecases",
        grant_type=[Access.ALLOW]
    )

    # TODO: Audio Endpoint
    CREATE_AUDIO_TTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Audio TTS',
        description='Allow the creation of a new audio TTS',
        endpoint_name="create_audio_tts",
        endpoint_path="/audio/tts",
        grant_type=[Access.ALLOW]
    )
    TRANSCRIBE_AUDIO: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Transcribe Audio',
        description='Transcribe Audio',
        endpoint_name="transcribe_audio",
        endpoint_path="/audio/transcribe",
        grant_type=[Access.ALLOW]
    )
    TRANSCRIBE_AUDIO_V2: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Transcribe Audio V2',
        description='Transcribe Audio V2',
        endpoint_name="transcribe_audio_v2",
        endpoint_path="/audio/transcribe-v2",
        grant_type=[Access.ALLOW]
    )

    # TODO: Chat history Endpoint
    GET_CHAT_HISTORIES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Chat Histories',
        description='Allow the retrieval of chat history',
        endpoint_name="get_all_chat_history",
        endpoint_path="/chat_hists",
        grant_type=[Access.ALLOW]
    )
    GET_CHAT_HISTORY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Chat History by ID',
        description='Allow the retrieval of chat history by ID',
        endpoint_name="get_chat_history",
        endpoint_path="/chat_hists/{chat_hist_id}",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CHAT_HISTORY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Chat History',
        description='Allow the update of chat history',
        endpoint_name="update_chat_hist",
        endpoint_path="/chat_hists/{chat_hist_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_CHAT_HISTORY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Chat History',
        description='Allow the deletion of chat history',
        endpoint_name="delete_chat_hist",
        endpoint_path="/chat_hists/{chat_hist_id}",
        grant_type=[Access.ALLOW]
    )
    GET_YOUR_CHAT_HISTORIES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Your Chat Histories',
        description='Allow the retrieval of your chat history',
        endpoint_name="get_all_your_chat_history",
        endpoint_path="/self/chat_hists",
        grant_type=[Access.ALLOW]
    )
    REACTION_FOR_CHAT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Reaction for Chat',
        description='Allow the reaction for chat',
        endpoint_name="create_reaction_for_chat",
        endpoint_path="/chat_hists/reaction",
        grant_type=[Access.ALLOW]
    )

    # TODO: Chatroom Endpoint
    CREATE_CHATROOM: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Chatroom',
        description='Allow the creation of a new chatroom',
        endpoint_name="create_chatroom",
        endpoint_path="/chatroom",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CHATROOM: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Chatroom',
        description='Allow the update of chatroom information',
        endpoint_name="update_chatroom",
        endpoint_path="/chatroom/{chatroom_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_CHATROOM: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Chatroom',
        description='Allow the deletion of a chatroom',
        endpoint_name="delete_chatroom",
        endpoint_path="/chatroom/{chatroom_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CHATROOM: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Chatroom',
        description='Allow the retrieval of chatroom information',
        endpoint_name="get_chatroom",
        endpoint_path="/chatroom/{chatroom_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CHATROOMS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Chatrooms',
        description='Allow the retrieval of chatrooms information',
        endpoint_name="get_all_chatroom",
        endpoint_path="/chatroom",
        grant_type=[Access.ALLOW]
    )
    GET_YOUR_CHATROOM: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Your Chatroom',
        description='Allow the retrieval of your chatroom information',
        endpoint_name="get_your_chatroom",
        endpoint_path="/self/chatroom",
        grant_type=[Access.ALLOW]
    )

    # TODO: Datasource Endpoint
    CREATE_DATASOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Datasource',
        description='Allow the creation of a new datasource',
        endpoint_name="create_datasource",
        endpoint_path="/datasource",
        grant_type=[Access.ALLOW]
    )
    UPDATE_DATASOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Datasource',
        description='Allow the update of datasource information',
        endpoint_name="update_datasource",
        endpoint_path="/datasource/{datasource_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_DATASOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Datasource',
        description='Allow the deletion of a datasource',
        endpoint_name="delete_datasource",
        endpoint_path="/datasource/{datasource_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DATASOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Datasource',
        description='Allow the retrieval of datasource information',
        endpoint_name="get_datasource",
        endpoint_path="/datasource/{datasource_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DATASOURCES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Datasources',
        description='Allow the retrieval of datasources information',
        endpoint_name="get_all_datasource",
        endpoint_path="/datasource",
        grant_type=[Access.ALLOW]
    )
    GET_ALL_YOUR_DATASOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Your Datasource',
        description='Allow the retrieval of your datasources information',
        endpoint_name="get_all_your_datasource",
        endpoint_path="/self/datasource",
        grant_type=[Access.ALLOW]
    )
    GET_ALL_DATASOURCE_FOR_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Datasource for User',
        description='Allow the retrieval of datasources information for user',
        endpoint_name="get_all_datasource_for_user",
        endpoint_path="/datasource-user",
        grant_type=[Access.ALLOW]
    )
    GOOGLE_CREATE_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Google Create Datastore',
        description='Allow the creation of a new Google Datastore',
        endpoint_name="google_create_datastore",
        endpoint_path="/google/create/datastore",
        grant_type=[Access.ALLOW]
    )
    GET_ALL_GOOGLE_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Google Datastore',
        description='Allow the retrieval of Google Datastore',
        endpoint_name="get_all_google_datastore",
        endpoint_path="/google/datastore",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_DOCUMENT_IN_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List Document in Datastore',
        description='Allow the retrieval of list document in Google Datastore',
        endpoint_name="get_list_documents_in_datastore",
        endpoint_path="/google/datastore/documents",
        grant_type=[Access.ALLOW]
    )
    GET_TREE_DOCUMENTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Tree Documents',
        description='Allow the retrieval of tree documents in Google Datastore',
        endpoint_name="get_tree_documents",
        endpoint_path="/google/datastore/documents/get-tree",
        grant_type=[Access.ALLOW]
    )
    GET_GOOGLE_DETAIL_DATASTORE_ID: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Google Detail Datastore ID',
        description='Allow the retrieval of Google Datastore by ID',
        endpoint_name="get_google_detail_datastore_id",
        endpoint_path="/google/datastore/{datastore_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GOOGLE_DETAIL_DATASTORE_ID_STATUS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Google Detail Datastore ID Status',
        description='Allow the retrieval of Google Datastore by ID and Status',
        endpoint_name="get_google_datastore_id_status",
        endpoint_path="/google/datastore_id/status",
        grant_type=[Access.ALLOW]
    )
    GENERATE_GOOGLE_SIGNED_URL: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Generate Google Signed URL',
        description='Allow the generation of Google Signed URL',
        endpoint_name="generate_google_signed_url",
        endpoint_path="/google/signed_url",
        component='signed_url',
        grant_type=[Access.ALLOW]
    )
    GENERATE_GOOGLE_SIGNED_URL_V2: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Generate Google Signed URL V2',
        description='Allow the generation of Google Signed URL V2',
        endpoint_name="generate_google_signed_url_v2",
        endpoint_path="/google/signed_url_v2",
        component='signed_url',
        grant_type=[Access.ALLOW]
    )
    GET_ALL_PROJECT_ID: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Project ID',
        description='Allow the retrieval of all project ID',
        endpoint_name="get_all_project_id",
        endpoint_path="/google/list-projects",
        grant_type=[Access.ALLOW]
    )
    CREATE_FOLDER_IN_COMPANY_OF_BUCKET: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Folder in Company of Bucket',
        description='Allow the creation of a new folder in company of bucket',
        endpoint_name="google_cloud_create_folder_in_bucket",
        endpoint_path="/google-cloud/bucket/create-folder",
        grant_type=[Access.ALLOW]
    )
    DELETE_FOLDER_IN_COMPANY_OF_BUCKET: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='DELETE Folder in Company of Bucket',
        description='Allow the delete folder in company of bucket',
        endpoint_name="google_cloud_delete_folder_in_bucket",
        endpoint_path="/google-cloud/bucket/delete-folder",
        grant_type=[Access.ALLOW]
    )
    UPLOAD_DOCUMENT_IN_FOLDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Upload Document in Folder',
        description='Allow the upload document in folder',
        endpoint_name="google_cloud_upload_document",
        endpoint_path="/google-cloud/upload-document",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_FILE_IN_BUCKET: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List File in Bucket',
        description='Allow the get list file in bucket',
        endpoint_name="google_cloud_get_list_file",
        endpoint_path="/google-cloud/list-file",
        grant_type=[Access.ALLOW]
    )

    # TODO: Provider Endpoint
    CREATE_PROVIDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Provider',
        description='Allow the creation of a new provider',
        endpoint_name="create_provider",
        endpoint_path="/providers",
        grant_type=[Access.ALLOW]
    )
    UPDATE_PROVIDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Provider',
        description='Allow the update of provider information',
        endpoint_name="update_provider",
        endpoint_path="/providers/{provider_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_PROVIDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Provider',
        description='Allow the deletion of a provider',
        endpoint_name="delete_provider",
        endpoint_path="/providers/{provider_id}",
        grant_type=[Access.ALLOW]
    )
    GET_PROVIDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Provider',
        description='Allow the retrieval of provider information',
        endpoint_name="get_provider",
        endpoint_path="/providers/{provider_id}",
        grant_type=[Access.ALLOW]
    )
    GET_PROVIDERS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Providers',
        description='Allow the retrieval of providers information',
        endpoint_name="get_all_providers",
        endpoint_path="/providers",
        grant_type=[Access.ALLOW]
    )
    UPDATE_PROVIDER_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Provider Usecase',
        description='Allow the update of provider usecase information',
        endpoint_name="update_provider_usecase",
        endpoint_path="/provider/usecase",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_MODEL_AI: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List Model AI',
        description='Allow the retrieval of list model in AI',
        endpoint_name="get_list_model_ai",
        endpoint_path="/provider/list-model-ai",
        grant_type=[Access.ALLOW]
    )
    GET_PROVIDERS_PUBLIC: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Providers Public',
        description='Allow the retrieval of public providers information',
        endpoint_name="get_all_providers_public",
        endpoint_path="/provider/list-providers-public",
        grant_type=[Access.ALLOW]
    )
    SET_PROVIDER_PUBLIC_OR_NOT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Set Provider Public or Not',
        description='Allow the setting of provider public or not',
        endpoint_name="set_provider_public_or_not",
        endpoint_path="/provider/set-public/{provider_id}",
        grant_type=[Access.ALLOW]
    )

    # TODO: Use case Endpoint
    CREATE_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Usecase',
        description='Allow the creation of a new usecase',
        endpoint_name="create_usecase",
        endpoint_path="/usecase",
        grant_type=[Access.ALLOW]
    )
    UPDATE_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Usecase',
        description='Allow the update of usecase information',
        endpoint_name="update_usecase",
        endpoint_path="/usecase/{usecase_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Usecase',
        description='Allow the deletion of a usecase',
        endpoint_name="delete_usecase",
        endpoint_path="/usecase/{usecase_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Usecase',
        description='Allow the retrieval of usecase information',
        endpoint_name="get_usecase",
        endpoint_path="/usecase/{usecase_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USECASES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Usecases',
        description='Allow the retrieval of usecases information',
        endpoint_name="get_all_usecase",
        endpoint_path="/usecase",
        grant_type=[Access.ALLOW]
    )
    GET_ALL_USECASE_FOR_USER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Usecase for User',
        description='Allow the retrieval of usecases information for user',
        endpoint_name="get_all_usecase_for_user",
        endpoint_path="/usecase-user",
        grant_type=[Access.ALLOW]
    )
    UPDATE_STATUS_USECASE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Status Usecase',
        description='Allow the update of status usecase',
        endpoint_name="update_status_usecase",
        endpoint_path="/usecase/update-status/{usecase_id}",
        grant_type=[Access.ALLOW]
    )
    EXPORT_USECASE_CSV: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Export Usecase CSV',
        description='Allow the export of usecases to CSV format',
        endpoint_name="export_usecase_csv",
        endpoint_path="/usecase-export-csv",
        grant_type=[Access.ALLOW]
    )

    # TODO: Audit Log Endpoint
    CREATE_AUDIT_LOG: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Audit Log',
        description='Allow the creation of a new audit log',
        endpoint_name="create_log",
        endpoint_path="/audit-logs",
        grant_type=[Access.ALLOW]
    )
    GET_AUDIT_LOGS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Audit Logs',
        description='Allow the retrieval of audit logs',
        endpoint_name="get_all_log",
        endpoint_path="/audit-logs",
        grant_type=[Access.ALLOW]
    )
    GET_AUDIT_LOG: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Audit Log by ID',
        description='Allow the retrieval of audit log by ID',
        endpoint_name="get_log",
        endpoint_path="/audit-logs/{log_id}",
        grant_type=[Access.ALLOW]
    )
    UPDATE_AUDIT_LOG: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Audit Log',
        description='Allow the update of audit log',
        endpoint_name="update_log",
        endpoint_path="/audit-logs/{log_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_AUDIT_LOG: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Audit Log',
        description='Allow the deletion of audit log',
        endpoint_name="delete_log",
        endpoint_path="/audit-logs/{log_id}",
        grant_type=[Access.ALLOW]
    )

    # TODO: Company Endpoint
    CREATE_COMPANY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Company',
        description='Allow the creation of a new company',
        endpoint_name="create_company",
        endpoint_path="/companies",
        grant_type=[Access.ALLOW]
    )
    UPDATE_COMPANY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Company',
        description='Update Company',
        endpoint_name="update_company",
        endpoint_path="/companies/{company_id}",
        grant_type=[Access.ALLOW]
    )
    GET_COMPANIES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Company',
        description='Get All Company',
        endpoint_name="get_all_companies",
        endpoint_path="/companies",
        grant_type=[Access.ALLOW]
    )
    DELETE_COMPANY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Company',
        description='Delete Company',
        endpoint_name="delete_company",
        endpoint_path="/companies/{company_id}",
        grant_type=[Access.ALLOW]
    )
    GET_COMPANY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Company',
        description='Get Detail Company',
        endpoint_name="get_company",
        endpoint_path="/companies/{company_id}",
        grant_type=[Access.ALLOW]
    )

    # TODO: Company Subscription Endpoint
    CREATE_COMPANY_SUBSCRIPTION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Company Subscription',
        description='Create Company Subscription',
        endpoint_name="create_company_subscription",
        endpoint_path="/company-subscriptions",
        grant_type=[Access.ALLOW]
    )
    UPDATE_COMPANY_SUBSCRIPTION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Company Subscription',
        description='Update Company Subscription',
        endpoint_name="update_company_subscription",
        endpoint_path="/company-subscriptions/{company_sub_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_COMPANY_SUBSCRIPTION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Company Subscription',
        description='Delete Company Subscription',
        endpoint_name="delete_company_subscription",
        endpoint_path="/company-subscriptions/{company_sub_id}",
        grant_type=[Access.ALLOW]
    )
    GET_COMPANY_SUBSCRIPTION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Company Subscription',
        description='Get Company Subscription',
        endpoint_name="get_company_subscription",
        endpoint_path="/company-subscriptions/{company_sub_id}",
        grant_type=[Access.ALLOW]
    )
    GET_COMPANY_SUBSCRIPTIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Company Subscription',
        description='Get All Company Subscription',
        endpoint_name="get_all_company_subscription",
        endpoint_path="/company-subscriptions",
        grant_type=[Access.ALLOW]
    )

    # TODO: Configuration Endpoint
    UPDATE_LOGO: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Logo',
        description='Update Logo',
        endpoint_name="update_logo",
        endpoint_path="/configuration/logo/update",
        grant_type=[Access.ALLOW]
    )
    GET_LOGO: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Logo',
        description='Get Logo',
        endpoint_name="get_logo",
        endpoint_path="/configuration/logo",
        grant_type=[Access.ALLOW]
    )

    # TODO: Contracts Endpoint
    CREATE_CONTRACT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Contract',
        description='Create Contract',
        endpoint_name="create_contract",
        endpoint_path="/contracts",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CONTRACT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Contract',
        description='Update Contract',
        endpoint_name="update_contract",
        endpoint_path="/contracts/{contract_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_CONTRACT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Contract',
        description='Delete Contract',
        endpoint_name="delete_contract",
        endpoint_path="/contracts/{contract_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CONTRACT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Contract',
        description='Get Contract',
        endpoint_name="get_contract",
        endpoint_path="/contracts/{contract_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CONTRACTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Contract',
        description='Get All Contract',
        endpoint_name="get_all_contracts",
        endpoint_path="/contracts",
        grant_type=[Access.ALLOW]
    )

    # TODO: Datastores Endpoint
    CREATE_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Datastore',
        description='Create Datastore',
        endpoint_name="create_datastores",
        endpoint_path="/datastores",
        grant_type=[Access.ALLOW]
    )
    UPDATE_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Datastore',
        description='Update Datastore',
        endpoint_name="update_data_store",
        endpoint_path="/datastores/{datastore_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Datastore',
        description='Delete Datastore',
        endpoint_name="delete_data_store",
        endpoint_path="/datastores/{datastore_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DATASTORE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Datastore',
        description='Get Datastore',
        endpoint_name="get_datastore",
        endpoint_path="/datastores/{datastore_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DATASTORES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Datastore',
        description='Get All Datastore',
        endpoint_name="get_all_datastore",
        endpoint_path="/datastores",
        grant_type=[Access.ALLOW]
    )

    # TODO: Department datastore access Endpoint
    CREATE_DEPARTMENT_DATASTORE_ACCESS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Department Datastore Access',
        description='Create Department Datastore Access',
        endpoint_name="create_department_datastore_access",
        endpoint_path="/department-datastore-access",
        grant_type=[Access.ALLOW]
    )
    UPDATE_DEPARTMENT_DATASTORE_ACCESS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Department Datastore Access',
        description='Update Department Datastore Access',
        endpoint_name="update_department_datastore_access",
        endpoint_path="/department-datastore-access/{department_datastore_access_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_DEPARTMENT_DATASTORE_ACCESS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Department Datastore Access',
        description='Delete Department Datastore Access',
        endpoint_name="delete_department_datastore_access",
        endpoint_path="/department-datastore-access/{department_datastore_access_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DEPARTMENT_DATASTORE_ACCESS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Department Datastore Access',
        description='Get Department Datastore Access',
        endpoint_name="get_department_datastore_access",
        endpoint_path="/department-datastore-access/{department_datastore_access_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DEPARTMENT_DATASTORE_ACCESS_LIST: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Department Datastore Access',
        description='Get All Department Datastore Access',
        endpoint_name="get_all_department_datastore_access",
        endpoint_path="/department-datastore-access",
        grant_type=[Access.ALLOW]
    )

    # TODO: Department Endpoint
    CREATE_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Department',
        description='Create Department',
        endpoint_name="create_department",
        endpoint_path="/departments",
        grant_type=[Access.ALLOW]
    )
    UPDATE_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Department',
        description='Update Department',
        endpoint_name="update_department",
        endpoint_path="/departments/{department_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Department',
        description='Delete Department',
        endpoint_name="delete_department",
        endpoint_path="/departments/{department_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Department',
        description='Get Department',
        endpoint_name="get_department",
        endpoint_path="/departments/{department_id}",
        grant_type=[Access.ALLOW]
    )
    GET_DEPARTMENTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Department',
        description='Get All Department',
        endpoint_name="get_all_department",
        endpoint_path="/departments",
        grant_type=[Access.ALLOW]
    )

    # TODO: Resource Usages Endpoint
    CREATE_RESOURCE_USAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Resource Usage',
        description='Create Resource Usage',
        endpoint_name="create_resource_usage",
        endpoint_path="/resource-usages",
        grant_type=[Access.ALLOW]
    )
    UPDATE_RESOURCE_USAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Resource Usage',
        description='Update Resource Usage',
        endpoint_name="update_resource_usage",
        endpoint_path="/resource-usages/{resource_usage_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_RESOURCE_USAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Resource Usage',
        description='Delete Resource Usage',
        endpoint_name="delete_resource_usage",
        endpoint_path="/resource-usages/{resource_usage_id}",
        grant_type=[Access.ALLOW]
    )
    GET_RESOURCE_USAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Resource Usage',
        description='Get Resource Usage',
        endpoint_name="get_resource_usage",
        endpoint_path="/resource-usages/{resource_usage_id}",
        grant_type=[Access.ALLOW]
    )
    GET_RESOURCE_USAGES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Resource Usage',
        description='Get All Resource Usage',
        endpoint_name="get_all_resource_usage",
        endpoint_path="/resource-usages",
        grant_type=[Access.ALLOW]
    )

    # TODO: Resources Endpoint
    CREATE_RESOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Resource',
        description='Create Resource',
        endpoint_name="create_resource",
        endpoint_path="/resources",
        grant_type=[Access.ALLOW]
    )
    UPDATE_RESOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Resource',
        description='Update Resource',
        endpoint_name="update_resource",
        endpoint_path="/resources/{resource_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_RESOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Resource',
        description='Delete Resource',
        endpoint_name="delete_resource",
        endpoint_path="/resources/{resource_id}",
        grant_type=[Access.ALLOW]
    )
    GET_RESOURCE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Resource',
        description='Get Resource',
        endpoint_name="get_resource",
        endpoint_path="/resources/{resource_id}",
        grant_type=[Access.ALLOW]
    )
    GET_RESOURCES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Resource',
        description='Get All Resource',
        endpoint_name="get_all_resource",
        endpoint_path="/resources",
        grant_type=[Access.ALLOW]
    )

    # TODO: Role Permission Endpoint
    CREATE_ROLE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Role Permission',
        description='Create Role Permission',
        endpoint_name="create_role_permission",
        endpoint_path="/role-permissions",
        grant_type=[Access.ALLOW]
    )
    UPDATE_ROLE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Role Permission',
        description='Update Role Permission',
        endpoint_name="update_role_permission",
        endpoint_path="/role-permissions/{role_permission_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_ROLE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Role Permission',
        description='Delete Role Permission',
        endpoint_name="delete_role_permission",
        endpoint_path="/role-permissions/{role_permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_ROLE_PERMISSION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Role Permission',
        description='Get Role Permission',
        endpoint_name="get_role_permission",
        endpoint_path="/role-permissions/{role_permission_id}",
        grant_type=[Access.ALLOW]
    )
    GET_ROLE_PERMISSIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Role Permission',
        description='Get All Role Permission',
        endpoint_name="get_all_role_permission",
        endpoint_path="/role-permissions",
        grant_type=[Access.ALLOW]
    )

    # TODO: Service Packages Endpoint
    CREATE_SERVICE_PACKAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Service Package',
        description='Create Service Package',
        endpoint_name="create_service_package",
        endpoint_path="/service-packages",
        grant_type=[Access.ALLOW]
    )
    UPDATE_SERVICE_PACKAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Service Package',
        description='Update Service Package',
        endpoint_name="update_service_packages",
        endpoint_path="/service-packages/{service_package_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_SERVICE_PACKAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Service Package',
        description='Delete Service Package',
        endpoint_name="delete_service_packages",
        endpoint_path="/service-packages/{service_package_id}",
        grant_type=[Access.ALLOW]
    )
    GET_SERVICE_PACKAGE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Service Package',
        description='Get Service Package',
        endpoint_name="get_service_package",
        endpoint_path="/service-packages/{service_package_id}",
        grant_type=[Access.ALLOW]
    )
    GET_SERVICE_PACKAGES: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Service Package',
        description='Get All Service Package',
        endpoint_name="get_all_service",
        endpoint_path="/service-packages",
        grant_type=[Access.ALLOW]
    )

    # TODO: User Departments Endpoint
    CREATE_USER_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create User Department',
        description='Create User Department',
        endpoint_name="create_user_department",
        endpoint_path="/user-departments",
        grant_type=[Access.ALLOW]
    )
    UPDATE_USER_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update User Department',
        description='Update User Department',
        endpoint_name="update_user_department",
        endpoint_path="/user-departments/{user_department_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_USER_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete User Department',
        description='Delete User Department',
        endpoint_name="delete_user_department",
        endpoint_path="/user-departments/{user_department_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_DEPARTMENT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get User Department',
        description='Get User Department',
        endpoint_name="get_user_department",
        endpoint_path="/user-departments/{user_department_id}",
        grant_type=[Access.ALLOW]
    )
    GET_USER_DEPARTMENTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All User Department',
        description='Get All User Department',
        endpoint_name="get_all_user_department",
        endpoint_path="/user-departments",
        grant_type=[Access.ALLOW]
    )

    # TODO: Company Endpoint
    CREATE_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Datastore Agent Builder',
        description='Create Datastore Agent Builder',
        endpoint_name="agent_builder_create_data_store",
        endpoint_path="/agent-builder/create-data-store",
        grant_type=[Access.ALLOW]
    )
    DELETE_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Datastore Agent Builder',
        description='Delete Datastore Agent Builder',
        endpoint_name="agent_builder_delete_data_store",
        endpoint_path="/agent-builder/delete-data-store",
        grant_type=[Access.ALLOW]
    )
    VECTOR_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Vector Datastore Agent Builder',
        description='Vector Datastore Agent Builder',
        endpoint_name="agent_builder_vector_data_store",
        endpoint_path="/agent-builder/vector-data-store",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List Datastore Agent Builder',
        description='Get List Datastore Agent Builder',
        endpoint_name="agent_builder_list_data_store",
        endpoint_path="/agent-builder/list-data-store",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_APP_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List App Agent Builder',
        description='Get List App Agent Builder',
        endpoint_name="agent_builder_list_app",
        endpoint_path="/agent-builder/list-app",
        grant_type=[Access.ALLOW]
    )
    CREATE_APP_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create App Agent Builder',
        description='Create App Agent Builder',
        endpoint_name="agent_builder_create_app",
        endpoint_path="/agent-builder/create-app",
        grant_type=[Access.ALLOW]
    )
    DELETE_APP_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete App Agent Builder',
        description='Delete App Agent Builder',
        endpoint_name="agent_builder_delete_app",
        endpoint_path="/agent-builder/delete-app",
        grant_type=[Access.ALLOW]
    )
    TRIGGER_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Trigger Datastore Agent Builder',
        description='Trigger Datastore Agent Builder',
        endpoint_name="agent_builder_trigger_data_store",
        endpoint_path="/agent-builder/trigger-data-store",
        grant_type=[Access.ALLOW]
    )
    IMPORT_DATASTORE_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Import Datastore Agent Builder',
        description='Import Datastore Agent Builder',
        endpoint_name="agent_builder_import_data_store",
        endpoint_path="/agent-builder/import-data-store",
        grant_type=[Access.ALLOW]
    )
    IMPORT_DOCUMENT_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Import Document Agent Builder',
        description='Import Document Agent Builder',
        endpoint_name="agent_builder_import_document",
        endpoint_path="/agent-builder/import-document",
        grant_type=[Access.ALLOW]
    )
    GET_LIST_DOCUMENT_INDEXED_AGENT_BUILDER: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get List Document Indexed Agent Builder',
        description='Get List Document Indexed Agent Builder',
        endpoint_name="agent_builder_get_list_document_indexed",
        endpoint_path="/agent-builder/list-document",
        grant_type=[Access.ALLOW]
    )

    # TODO: Notification Endpoint
    CREATE_NOTIFICATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Notification',
        description='Create Notification',
        endpoint_name="create_notification",
        endpoint_path="/notifications",
        grant_type=[Access.ALLOW]
    )
    UPDATE_NOTIFICATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Notification',
        description='Update Notification',
        endpoint_name="update_notification",
        endpoint_path="/notifications/{notification_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_NOTIFICATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Notification',
        description='Delete Notification',
        endpoint_name="delete_notification",
        endpoint_path="/notifications/{notification_id}",
        grant_type=[Access.ALLOW]
    )
    GET_NOTIFICATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Notification',
        description='Get Notification',
        endpoint_name="get_notification",
        endpoint_path="/notifications/{notification_id}",
        grant_type=[Access.ALLOW]
    )
    GET_NOTIFICATIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Notification',
        description='Get All Notification',
        endpoint_name="get_all_notification",
        endpoint_path="/notifications",
        grant_type=[Access.ALLOW]
    )
    GET_NOTIFICATIONS_EXPIRED: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Notification Expired',
        description='Get All Notification Expired',
        endpoint_name="get_all_notification_expired",
        endpoint_path="/notifications-expired",
        grant_type=[Access.ALLOW]
    )
    CHANGE_STATUS_NOTIFICATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Change Status Notification',
        description='Change Status Notification',
        endpoint_name="change_status_notification",
        endpoint_path="/notifications-change-status",
        grant_type=[Access.ALLOW]
    )

    # TODO: GCP Projects Endpoint
    CREATE_GCP_PROJECT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Gcp Project',
        description='Create Gcp Project',
        endpoint_name="create_gcp_project",
        endpoint_path="/gcp-projects",
        grant_type=[Access.ALLOW]
    )
    UPDATE_GCP_PROJECT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Gcp Project',
        description='Update Gcp Project',
        endpoint_name="update_gcp_project",
        endpoint_path="/gcp-projects/{gcp_project_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_GCP_PROJECT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Gcp Project',
        description='Delete Gcp Project',
        endpoint_name="delete_gcp_project",
        endpoint_path="/gcp-projects/{gcp_project_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GCP_PROJECT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Gcp Project',
        description='Get Gcp Project',
        endpoint_name="get_gcp_project",
        endpoint_path="/gcp-projects/{gcp_project_id}",
        grant_type=[Access.ALLOW]
    )
    GET_GCP_PROJECTS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Gcp Project',
        description='Get All Gcp Project',
        endpoint_name="get_all_gcp_project",
        endpoint_path="/gcp-projects",
        grant_type=[Access.ALLOW]
    )
    GET_PROJECTS_ON_GCP: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Projects on GCP',
        description='Get Projects on GCP',
        endpoint_name="get_all_projects_on_gcp",
        endpoint_path="/gcp-projects-on-gcp",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CURRENT_PROJECT: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Current Project',
        description='Update Current Project',
        endpoint_name="update_current_project",
        endpoint_path="/update-current-project",
        grant_type=[Access.ALLOW]
    )

    # TODO: Setting Endpoint
    CREATE_CONFIGURATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Create Configuration',
        description='Create Configuration',
        endpoint_name="create_configuration",
        endpoint_path="/configurations",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CONFIGURATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Configuration',
        description='Update Configuration',
        endpoint_name="update_configuration",
        endpoint_path="/configurations/{configuration_id}",
        grant_type=[Access.ALLOW]
    )
    DELETE_CONFIGURATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Delete Configuration',
        description='Delete Configuration',
        endpoint_name="delete_configuration",
        endpoint_path="/configurations/{configuration_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CONFIGURATION: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Configuration',
        description='Get Configuration',
        endpoint_name="get_configuration",
        endpoint_path="/configurations/{configuration_id}",
        grant_type=[Access.ALLOW]
    )
    GET_CONFIGURATIONS: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get All Configuration',
        description='Get All Configuration',
        endpoint_name="get_all_configuration",
        endpoint_path="/configurations",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CONFIGURATION_USER_CHAT_TITLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Configuration User Chat Title',
        description='Update Configuration User Chat Title',
        endpoint_name="update_configuration_user_chat_title",
        endpoint_path="/configuration/update-user-chat-title",
        grant_type=[Access.ALLOW]
    )
    GET_CONFIGURATION_USER_CHAT_TITLE: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Configuration User Chat Title',
        description='Get Configuration User Chat Title',
        endpoint_name="get_configuration_user_chat_title",
        endpoint_path="/configuration/get-user-chat-title",
        grant_type=[Access.ALLOW]
    )
    UPDATE_CONFIGURATION_MODEL_KEY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Update Configuration Model Key',
        description='Update Configuration Model Key',
        endpoint_name="update_configuration_model_key",
        endpoint_path="/configuration/update-model-key",
        grant_type=[Access.ALLOW]
    )
    GET_CONFIGURATION_MODEL_KEY: ClassVar[GrantEndpoint] = GrantEndpoint(
        name='Get Configuration Model Key',
        description='Get Configuration Model Key',
        endpoint_name="get_configuration_model_key",
        endpoint_path="/configuration/get-model-key",
        grant_type=[Access.ALLOW]
    )
