from typing import Callable, Any
from functools import wraps
from sqlalchemy.orm import Session
from fastapi import Request, status as http_status
from app.helpers.exceptions import GatewayException
from app.models.action_logs import ActionLog
from fastapi import Depends
from app.db.base import get_db
import json


def track_action(action_name: str) -> Callable:
    def decorator(func: Callable) -> Callable:

        @wraps(func)
        async def inner(*args, **kwargs) -> Any:
            try:
                request: Request = kwargs.get("request")
                if not request or not hasattr(request, "state"):
                    raise GatewayException(
                        status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Request object or state is missing"
                    )

                x_request_id = getattr(request.state, "x_request_id", None)
                if not x_request_id:
                    raise GatewayException(
                        status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="x_request_id is missing from request state"
                    )

                x_request_id = request.state.x_request_id
                auth_info = request.state.user.to_dict()
                language = kwargs.get("language")
                db: Session = kwargs.get("db")
                if not db:
                    raise GatewayException(
                        status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Database session is missing from function arguments"
                    )

                request_data = {key: value for key, value in kwargs.items() if key not in ['request', 'db', 'files']}
                request_data = json.dumps(request_data, default=lambda o: o.__dict__)
                user_agent = request.headers.get("user-agent")
                ip_address = request.client.host if request.client else None
                log_entry = ActionLog(
                    user_id=auth_info.get("client_id"),
                    company_id=auth_info.get("company_id"),
                    action_name=action_name,
                    endpoint=request.url.path,
                    method=request.method,
                    request_data=request_data or None,
                    error_message=None,
                    x_request_id=x_request_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    language=language
                )

                resp = await func(*args, **kwargs)
                log_entry.status_code = http_status.HTTP_200_OK
                log_entry.is_success = True
                db.add(log_entry)
                db.commit()

                return resp

            except GatewayException as exc:
                db: Session = kwargs.get("db")
                db.rollback()
                log_entry = ActionLog(
                    user_id=auth_info.get("client_id"),
                    company_id=auth_info.get("company_id"),
                    action_name=action_name,
                    endpoint=request.url.path,
                    method=request.method,
                    request_data=request_data or None,
                    error_message=None,
                    x_request_id=x_request_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    language=language
                )
                log_entry.status_code = exc.status_code
                log_entry.is_success = False
                log_entry.error_message = exc.detail if isinstance(exc.detail, list) else [exc.detail]
                db.add(log_entry)
                db.commit()
                raise

        return inner

    return decorator
