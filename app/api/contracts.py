import traceback
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.db.base import get_db
from app.services.base import CRUDBase
from app.schemas.base import DataResponse
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.models.contracts import Contracts
from app.schemas.contracts import ContractCreate, ContractReps, ContractUpdate
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_CONTRACT.endpoint_name,
    path=AccessEndpoint.CREATE_CONTRACT.endpoint_path,
    response_model=DataResponse[ContractReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_contract(
        contract: ContractCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_contract = Contracts(**contract.dict())
        db.add(db_contract)
        db.commit()
        db.refresh(db_contract)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_contract)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CONTRACT.endpoint_name,
    path=AccessEndpoint.GET_CONTRACT.endpoint_path,
    response_model=DataResponse[ContractReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_contract(
        contract_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_contract = db.query(Contracts).filter(Contracts.id == contract_id,
                                                 Contracts.is_deleted == is_deleted).first()
        if not db_contract:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_contract)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CONTRACTS.endpoint_name,
    path=AccessEndpoint.GET_CONTRACTS.endpoint_path,
    response_model=DataResponse[Page[ContractReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_contracts(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Contracts).filter(Contracts.is_deleted == is_deleted)
        contracts = CRUDBase(Contracts).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=contracts)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_CONTRACT.endpoint_name,
    path=AccessEndpoint.UPDATE_CONTRACT.endpoint_path,
    response_model=DataResponse[ContractReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_contract(
        contract_id: str,
        contracts: ContractUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_contract = db.query(Contracts).filter(Contracts.id == contract_id).first()
        if not db_contract:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_contract.start_date = contracts.start_date
        db_contract.end_date = contracts.end_date
        db_contract.total_price = contracts.total_price
        db_contract.status = contracts.status

        db.commit()
        db.refresh(db_contract)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_contract)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_CONTRACT.endpoint_name,
    path=AccessEndpoint.DELETE_CONTRACT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_contract(
        contract_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_contract = db.query(Contracts).filter(Contracts.id == contract_id).first()
        if not db_contract:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_contract.is_deleted = True
        db_contract.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_contract)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
