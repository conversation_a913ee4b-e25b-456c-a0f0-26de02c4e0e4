import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.departments import DepartmentUpdate, DepartmentReps, DepartmentCreate
from app.models.departments import Departments
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.CREATE_DEPARTMENT.endpoint_path,
    response_model=DataResponse[DepartmentReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_department(
        department: DepartmentCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        department_exist = db.query(exists().where(Departments.department_name == department.department_name)).scalar()
        if department_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        department_exist = Departments(**department.dict())
        db.add(department_exist)
        db.commit()
        db.refresh(department_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=department_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.GET_DEPARTMENT.endpoint_path,
    response_model=DataResponse[DepartmentReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_department(
        department_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_department = db.query(Departments).filter(Departments.id == department_id,
                                                     Departments.is_deleted == is_deleted).first()
        if not db_department:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_department)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DEPARTMENTS.endpoint_name,
    path=AccessEndpoint.GET_DEPARTMENTS.endpoint_path,
    response_model=DataResponse[Page[DepartmentReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_department(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Departments).filter(Departments.is_deleted == is_deleted)
        department = CRUDBase(Departments).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=department)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.UPDATE_DEPARTMENT.endpoint_path,
    response_model=DataResponse[DepartmentReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_department(
        department_id: str,
        department: DepartmentUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_department = db.query(Departments).filter(Departments.id == department_id).first()
        if not db_department:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_department.company_id = department.company_id
        db_department.department_name = department.department_name
        db_department.manager_id = department.manager_id

        db.commit()
        db.refresh(db_department)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_department)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.DELETE_DEPARTMENT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_department(
        department_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_department = db.query(Departments).filter(Departments.id == department_id).first()
        if not db_department:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_department.is_deleted = True
        db_department.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_department)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
