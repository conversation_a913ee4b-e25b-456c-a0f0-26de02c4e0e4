import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.user_departments import UserDepartmentCreate, UserDepartmentReps, UserDepartmentUpdate
from app.models.user_departments import UserDepartment
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_USER_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.CREATE_USER_DEPARTMENT.endpoint_path,
    response_model=DataResponse[UserDepartmentReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermission<PERSON><PERSON><PERSON>(validate_admin))]
)
async def create_user_department(
        user_department: UserDepartmentCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        is_exist = db.query(exists().where(UserDepartment.user_id == user_department.user_id,
                                           UserDepartment.department_id == user_department.department_id)).scalar()
        if is_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        is_exist = UserDepartment(**user_department.dict())
        db.add(is_exist)
        db.commit()
        db.refresh(is_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=is_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.GET_USER_DEPARTMENT.endpoint_path,
    response_model=DataResponse[UserDepartmentReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_user_department(
        user_department_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_user_department = db.query(UserDepartment).filter(UserDepartment.id == user_department_id,
                                                             UserDepartment.is_deleted == is_deleted).first()
        if not db_user_department:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_user_department)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_DEPARTMENTS.endpoint_name,
    path=AccessEndpoint.GET_USER_DEPARTMENTS.endpoint_path,
    response_model=DataResponse[Page[UserDepartmentReps]],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_user_department(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(UserDepartment).filter(UserDepartment.is_deleted == is_deleted)
        user_department = CRUDBase(UserDepartment).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_department)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_USER_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.UPDATE_USER_DEPARTMENT.endpoint_path,
    response_model=DataResponse[UserDepartmentReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_user_department(
        user_department_id: str,
        userDepartmentUpdate: UserDepartmentUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_user_department = db.query(UserDepartment).filter(UserDepartment.id == user_department_id).first()
        if not db_user_department:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_user_department.user_id = userDepartmentUpdate.user_id
        db_user_department.department_id = userDepartmentUpdate.department_id

        db.commit()
        db.refresh(db_user_department)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_user_department)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_USER_DEPARTMENT.endpoint_name,
    path=AccessEndpoint.DELETE_USER_DEPARTMENT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_user_department(
        company_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(UserDepartment).filter(UserDepartment.id == company_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
