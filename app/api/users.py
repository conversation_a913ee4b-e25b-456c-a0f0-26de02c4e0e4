import uuid
import traceback
import openpyxl
import os
from datetime import datetime
from typing import Optional
from google.cloud import storage
from fastapi import (APIRouter, Depends, Request, Response, File, UploadFile, Form, Query, BackgroundTasks,
                     status as http_status)
from sqlalchemy import func, update, desc, asc
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.config import settings
from app.helpers.exceptions import GatewayException
from app.helpers.security import (check_password_strength, hash_password, validate_user, validate_admin,
                                  AuthPermissionChecker, validate_super_admin)
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.users import (UserCreate, UserReps, UserUpdate, RoleSchema, UserFilter, UserUpdateExcel,
                               UserDeleteList, ListUserActive)
from app.schemas.groups import GroupReps
from app.models.users import Users, Role, UserRole, Group, UserGroup, Permission
from app.models.companies import Companies
from app.models.company_subscriptions import CompanySubscriptions
from app.models.service_packages import ServicePackage
from app.services.base import CRUDBase
from app.services.user_service import ZohoPlatform, template_update_users, fonfun_csv_template, \
    fonfun_download_to_csv, fonfun_upload_by_csv, upload_users_and_process
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.models.role_permissions import RolePermission
from app.schemas.permissions import PermissionReps
from app.models.token import Token

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_USER_ADMIN.endpoint_name,
    path=AccessEndpoint.CREATE_USER_ADMIN.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_admin_user(
        user: UserCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_user: Users = db.query(Users).filter(Users.email == user.email, Users.is_deleted == False,
                                                Users.user_name == user.user_name).first()
        if db_user:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_exists", language))

        db_company = db.query(Companies).filter(Companies.id == user.company_id, Companies.is_active == True,
                                                Companies.is_deleted == False).first()
        if db_company is None:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_cannot_created", language))

        is_valid = user.password == user.confirm_pw
        if not is_valid:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("password_incorrect", language))

        is_strong, score, message = check_password_strength(user.password, language)
        if not is_strong:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=message)

        hashed_password = hash_password(user.password)
        db_user = Users(
            **user.dict(exclude={"password", "confirm_pw", "use_case", "roles"}),
            hashed_password=hashed_password,
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        admin_role = db.query(Role).filter(func.lower(Role.name) == "admin", Role.is_deleted == False).first()
        if not admin_role:
            admin_role = Role(name="Admin", description="Administrator role")
            db.add(admin_role)
            db.commit()
            db.refresh(admin_role)

        user.roles = [admin_role.id]
        if user.roles:
            user_roles = [UserRole(user_id=db_user.id, role_id=role_id) for role_id in user.roles]
            db.bulk_save_objects(user_roles)
            db.commit()

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_user)

    except GatewayException:
        raise

    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.CREATE_USER.endpoint_name,
    path=AccessEndpoint.CREATE_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_user(
        company: int,
        user: UserCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_user: Users = db.query(Users).filter(Users.email == user.email, Users.user_name == user.user_name).first()
        if db_user:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_exists", language))

        db_company = db.query(Companies).filter(Companies.id == user.company_id, Companies.is_active == True,
                                                Companies.is_deleted == False).first()
        if db_company is None:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("company_not_found", language))

        if db_company.company_name.upper() != "MUSASHINO" and db_company.company_type != 'parent' and company == 0:
            package = db.query(ServicePackage).join(CompanySubscriptions,
                                                    ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == user.company_id, CompanySubscriptions.status == 1,
                CompanySubscriptions.is_active == True).first()
            count_user = db.query(Users).join(Companies, Users.company_id == Companies.id).join(CompanySubscriptions,
                                                                                                Companies.id == CompanySubscriptions.company_id).join(
                ServicePackage, ServicePackage.id == CompanySubscriptions.package_id).filter(
                Users.company_id == user.company_id,
                Users.is_active == True,
                Users.is_deleted == False,
                CompanySubscriptions.is_active == True,
                CompanySubscriptions.status == 1
            ).count()
            if (package.max_users + 2) <= count_user:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("user_exceed", language))

            count_admin = db.query(Users).join(
                UserRole, Users.id == UserRole.user_id
            ).join(
                Role, UserRole.role_id == Role.id
            ).filter(
                Users.company_id == user.company_id,
                Users.is_active == True,
                Users.is_deleted == False,
                Role.name == 'Admin'
            ).count()
            for role in user.roles:
                cRole = db.query(Role).filter(Role.id == role).first()
                if cRole.name == 'Admin' and count_admin >= 1:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("admin_exceed", language))
        elif company != 0:
            package = db.query(ServicePackage).join(CompanySubscriptions,
                                                    ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == user.company_id, CompanySubscriptions.status == 1,
                CompanySubscriptions.is_active == True).first()
            count_user = db.query(Users).join(Companies, Users.company_id == Companies.id).join(CompanySubscriptions,
                                                                                                Companies.id == CompanySubscriptions.company_id).join(
                ServicePackage, ServicePackage.id == CompanySubscriptions.package_id).filter(
                Users.company_id == user.company_id,
                Users.is_musashino == False,
                Users.is_active == True,
                Users.is_deleted == False,
                CompanySubscriptions.is_active == True,
                CompanySubscriptions.status == 1
            ).count()
            if package.max_users <= count_user:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("user_exceed", language))
        is_valid = user.password == user.confirm_pw
        if not is_valid:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("password_incorrect", language))

        is_strong, score, message = check_password_strength(user.password, language)
        if not is_strong:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=message)

        hashed_password = hash_password(user.password)
        db_user = Users(
            **user.dict(exclude={"password", "confirm_pw", "use_case", "roles", "groups"}),
            hashed_password=hashed_password,
        )
        db_user.uid = uuid.uuid4().hex.upper()
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        if not user.roles:
            user_role = db.query(Role).filter(func.lower(Role.name) == "user", Role.is_deleted == False).first()
            if not user_role:
                user_role = Role(name="User", description="Default user role")
                db.add(user_role)
                db.commit()
                db.refresh(user_role)

            user.roles = [user_role.id]

        if user.roles:
            user_roles = [UserRole(user_id=db_user.id, role_id=role_id) for role_id in user.roles]
            db.bulk_save_objects(user_roles)
            db.commit()

        if user.groups:
            user_groups = [UserGroup(user_id=db_user.id, group_id=group_id) for group_id in user.groups]
            db.bulk_save_objects(user_groups)
            db.commit()

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_user)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.ACTIVE_USER.endpoint_name,
    path=AccessEndpoint.ACTIVE_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def active_user(
        user_id: int,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False).first()

        if user is None:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("your_account_incorrect", language))

        user.active = True
        db.commit()
        db.refresh(user)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    detail=get_message("active_successfully", language))

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.DE_ACTIVE_USER.endpoint_name,
    path=AccessEndpoint.DE_ACTIVE_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def deactive_user(
        user_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        user = db.query(Users).filter(Users.id == user_id).first()

        if user is None:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))

        user.active = False
        db.commit()
        db.refresh(user)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    detail=get_message("de_active_successfully", language))

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_YOUR_INFORMATION.endpoint_name,
    path=AccessEndpoint.GET_YOUR_INFORMATION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def your_information(
        request: Request,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = request.state.user.to_dict()
        user_id = auth_info.get('client_id')
        if not user_id:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("something_wrong", language))

        user = db.query(Users).filter(Users.id == user_id, Users.is_active == True, Users.is_deleted == False).first()
        if not user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))
        slug = db.query(Companies).join(Users, Users.company_id == Companies.id).filter(
            Users.id == user.id).first()
        user_roles = (
                         db.query(Role)
                         .join(UserRole, UserRole.role_id == Role.id)
                         .filter(UserRole.user_id == user.id)
                         .all()
                     ) or []

        permissions = db.query(Permission).join(
            RolePermission, Permission.id == RolePermission.permission_id
        ).join(
            UserRole, UserRole.role_id == RolePermission.role_id
        ).filter(
            UserRole.user_id == user.id
        ).all()

        user_groups = db.query(UserGroup).filter(
            UserGroup.user_id == user_id,
            UserGroup.is_deleted == False,
            UserGroup.is_active == True
        ).all()
        groups = []
        for u_g in user_groups:
            detail_group = db.query(Group).filter(Group.id == u_g.group_id).first()
            if detail_group and detail_group.company_id is not None:
                groups.append(detail_group)
        user_data = UserReps(
            **user.to_dict(),
            groups=[GroupReps(**group.to_dict()) for group in groups],
            roles=[RoleSchema(**role.to_dict()) for role in user_roles],
            permissions=[PermissionReps(**permission.to_dict()) for permission in permissions]
        )
        user_data.company_slug = slug.company_slug
        user_data.company_name = slug.company_name

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER.endpoint_name,
    path=AccessEndpoint.GET_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_user(
        company: int,
        user_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if company not in [0]:
            user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False,
                                          Users.company_id == company).first()
            if not user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("no_results_found", language))
        else:
            user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False).first()
            if not user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("no_results_found", language))

        db_company = db.query(Companies).filter(Companies.id == user.company_id, Companies.is_active == True,
                                                Companies.is_deleted == False).first()
        if not db_company:
            company_active = False
        else:
            company_active = True

        user_roles = (
                         db.query(Role)
                         .join(UserRole, UserRole.role_id == Role.id)
                         .filter(UserRole.user_id == user_id)
                         .all()
                     ) or []
        # TODO: Get user group
        user_groups = db.query(UserGroup).filter(
            UserGroup.user_id == user.id,
            UserGroup.is_deleted == False,
            UserGroup.is_active == True
        ).all()
        u_group = []
        for us in user_groups:
            group = db.query(Group).filter(Group.id == us.group_id).first()
            if group:
                u_group.append(GroupReps(**group.__dict__))

        user_company = db.query(Companies).filter(Companies.id == user.company_id).first()
        user.company_active = company_active
        user_dict = user.to_dict()
        user_dict["company_active"] = company_active
        user_data = UserReps(
            **user_dict,
            roles=[RoleSchema(**role.to_dict()) for role in user_roles],
            groups=u_group
        )
        user_data.company = user_company

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USERS.endpoint_name,
    path=AccessEndpoint.GET_USERS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[Page[UserReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_users(
        company: int,
        request: Request,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: UserFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by Chat Room Title, User Name, or User Email"),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        user_roles = []
        user_groups = []

        # Base query with companies join for potential sorting
        statement = db.query(Users).join(Companies, Companies.id == Users.company_id)

        if filters.is_active == "true":
            if company not in [0]:
                statement = statement.filter(
                    Users.is_active == True,
                    Users.is_deleted == False,
                    Users.company_id == company,
                    Users.is_musashino == False,
                    Users.email.not_like('%superadmin%')
                )
            else:
                statement = statement.filter(
                    Users.is_active == True,
                    Users.is_deleted == False,
                    Users.is_musashino == True
                )
        elif filters.is_active == "false":
            if company not in [0]:
                statement = statement.filter(
                    Users.is_active == False,
                    Users.is_deleted == False,
                    Users.company_id == company,
                    Users.is_musashino == False,
                    Users.email.not_like('%superadmin%')
                )
            else:
                statement = statement.filter(
                    Users.is_active == False,
                    Users.is_deleted == False,
                    Users.is_musashino == True
                )
        else:
            if company not in [0]:
                statement = statement.filter(
                    Users.is_deleted == False,
                    Users.company_id == company,
                    Users.is_musashino == False,
                    Users.email.not_like('%superadmin%')
                )
            else:
                statement = statement.filter(
                    Users.is_deleted == False,
                    Users.is_musashino == True
                )

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (Users.full_name.ilike(f"%{search}%")) |
                (Users.email.ilike(f"%{search}%")) |
                (Companies.company_name.ilike(f"%{search}%"))
            )

        if filters.role_name:
            statement = statement.join(UserRole, UserRole.user_id == Users.id).join(Role,
                                                                                    Role.id == UserRole.role_id).filter(
                Role.name == filters.role_name
            )
            user_roles = db.query(Role).join(UserRole, UserRole.role_id == Role.id).filter(
                Role.name == filters.role_name).all()

        if filters.group_name:
            statement = statement.join(UserGroup, UserGroup.user_id == Users.id).join(Group,
                                                                                      Group.id == UserGroup.group_id).filter(
                Group.name.ilike(f"%{filters.group_name}%")
            )
            user_groups = db.query(Group).join(UserGroup, UserGroup.group_id == Group.id).filter(
                Group.name.ilike(f"%{filters.group_name}%")).all()

        # Handle sorting by company name
        if page.sort_by == 'company_name':
            # Apply manual ordering for company name
            direction = desc if page.order == 'desc' else asc
            statement = statement.order_by(direction(Companies.company_name))
            # Create new pagination params without sort_by to avoid double sorting
            page_params = PaginationParams(
                page_size=page.page_size,
                page=page.page,
                sort_by='id',  # Default sort to prevent errors
                order='asc'
            )
            users = CRUDBase(Users).list(db=db, query=statement, params=page_params)
        else:
            users = CRUDBase(Users).list(db=db, query=statement, params=page)
        user_data_list = []

        for user in users.items:
            if not filters.role_name:
                user_roles = db.query(Role).join(UserRole, UserRole.role_id == Role.id).join(Users,
                                                                                             Users.id == UserRole.user_id).filter(
                    Role.is_active == True, Role.is_deleted == False,
                    UserRole.user_id == user.id).all()
            if not filters.group_name:
                user_groups = db.query(Group).join(UserGroup, UserGroup.group_id == Group.id).join(Users,
                                                                                                   Users.id == UserGroup.user_id).filter(
                    Group.is_active == True, Group.is_deleted == False,
                    UserGroup.user_id == user.id).all()
            user_company = db.query(Companies).filter(Companies.id == user.company_id).first()
            user_data = UserReps(
                **user.to_dict(),
                roles=[RoleSchema(**role.to_dict()) for role in user_roles],
                groups=[GroupReps(**group.to_dict()) for group in user_groups]
            )
            user_data.company = user_company
            user_data_list.append(user_data)
        paginated_response = Page[UserReps].create(items=user_data_list, metadata=users.metadata)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=paginated_response)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_USER.endpoint_name,
    path=AccessEndpoint.UPDATE_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def update_user(
        company: int,
        user_id: str,
        user: UserUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        #   Check user exist
        if company not in [0]:
            db_user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False,
                                             Users.company_id == company).first()

            if not db_user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("user_not_found", language))
            count_user = db.query(Users).filter(
                Users.company_id == company,
                Users.is_active == True,
                Users.is_deleted == False,
                Users.is_musashino == False
            ).count()
            package = db.query(ServicePackage).join(CompanySubscriptions,
                                                    ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == user.company_id, CompanySubscriptions.status == 1,
                CompanySubscriptions.is_active == True).first()

            if user.is_active == True and db_user.is_active == False:
                if package.max_users <= count_user:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("user_exceed", language))

        else:
            db_user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False).first()

            if not db_user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("user_not_found", language))

            count_user = db.query(Users).filter(Users.id == user_id, Users.is_deleted == False).count()
            package = db.query(ServicePackage).join(CompanySubscriptions,
                                                    ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == user.company_id, CompanySubscriptions.status == 1,
                CompanySubscriptions.is_active == True).first()
            if (package.max_users + 2) <= count_user:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("user_exceed", language))
            count_admin = db.query(Users).join(
                UserRole, Users.id == UserRole.user_id
            ).join(
                Role, UserRole.role_id == Role.id
            ).filter(
                Users.company_id == user.company_id,
                Users.is_active == True,
                Users.is_deleted == False,
                Role.name == 'Admin'
            ).count()
            for role in user.roles:
                cRole = db.query(Role).filter(Role.id == role).first()
                if cRole.name == 'Admin' and count_admin >= 1 and db_user.is_active == False and user.is_active == True:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("admin_exceed", language))

        #  Get list role in DB
        list_role = db.query(UserRole).filter(UserRole.user_id == user_id).all()
        request_roles = set(user.roles)

        #   Delete role not in request
        for current_role in list_role:
            if current_role.role_id not in request_roles:
                db.delete(current_role)
                db.commit()

        #   Add new role
        for role in user.roles:
            #   Check role exist
            db_role = db.query(Role).filter(Role.id == role).first()
            if not db_role.id:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("role_not_found", language))
            else:
                #   Check role exist
                db_user_role = db.query(UserRole).filter(UserRole.user_id == user_id, UserRole.role_id == role).first()
                #   Add new role
                if not db_user_role:
                    db_user_role = UserRole(user_id=user_id, role_id=role)
                    db.add(db_user_role)
                    db.commit()
                    db.refresh(db_user_role)

        if user.groups:
            #   Get list group in DB
            list_group = db.query(UserGroup).filter(UserGroup.user_id == user_id).all()
            request_groups = set(user.groups)

            #   Delete group not in request
            for current_group in list_group:
                if current_group.group_id not in request_groups:
                    db.delete(current_group)
                    db.commit()

            #   Add new group
            for group in user.groups:
                #   Check group exist
                db_group = db.query(Group).filter(Group.id == group).first()
                if not db_group.id:
                    raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                           detail=get_message("group_not_found", language))
                else:
                    db_user_group = db.query(UserGroup).filter(UserGroup.user_id == user_id,
                                                               UserGroup.group_id == group).first()

                    if not db_user_group:
                        db_user_group = UserGroup(user_id=user_id, group_id=group)
                        db.add(db_user_group)
                        db.commit()
                        db.refresh(db_user_group)
        else:
            #   Get list group in DB
            list_group = db.query(UserGroup).filter(UserGroup.user_id == user_id).all()

            #   Delete group not in request
            for current_group in list_group:
                db.delete(current_group)
                db.commit()
        # for group in user.groups:
        #     db_group = db.query(Group).filter(Group.id == group).first()
        #     if not db_group.id:
        #         raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
        #                                detail="グループが見つかりません。" if language == "jp" else "Group not found.")
        #     else:
        #         db_user_group = db.query(UserGroup).filter(UserGroup.user_id == user_id,
        #                                                    UserGroup.group_id == group).first()
        #
        #         if not db_user_group:
        #             db_user_group = UserGroup(user_id=user_id, group_id=group)
        #             db.add(db_user_group)
        #             db.commit()
        #             db.refresh(db_user_group)

        #   Update user
        if not user.is_active:
            stmt = update(Token).where(Token.user_id == user_id).values(
                is_active=False, updated_at=datetime.now())
            db.execute(stmt)
            db.commit()
        else:
            stmt = update(Token).where(Token.user_id == user_id).values(
                is_active=True, updated_at=datetime.now())
            db.execute(stmt)
            db.commit()
        db_user.full_name = user.full_name
        db_user.bio = user.bio
        db_user.avatar_url = user.avatar_url
        db_user.is_superuser = user.is_superuser
        db_user.validated = user.validated
        db_user.is_active = user.is_active
        db_user.additional_info = user.additional_info
        db_user.manager_id = user.manager_id
        db_user.business_type = user.business_type

        db.commit()
        db.refresh(db_user)

        #   Get user roles
        user_roles = (
                         db.query(Role)
                         .join(UserRole, UserRole.role_id == Role.id)
                         .filter(UserRole.user_id == user_id)
                         .all()
                     ) or []
        user_group = (
                         db.query(Group)
                         .join(UserGroup, UserGroup.group_id == Group.id)
                         .filter(UserGroup.user_id == user_id)
                         .all()
                     ) or []

        #   Response
        db_user = UserReps(
            **db_user.to_dict(),
            roles=[RoleSchema(**role.to_dict()) for role in user_roles],
            groups=[GroupReps(**group.to_dict()) for group in user_group]
        )

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_user)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_USER.endpoint_name,
    path=AccessEndpoint.DELETE_USER.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_user(
        company: int,
        list_user: UserDeleteList,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        for user in list_user.list_user:
            if company not in [0]:
                db_user = db.query(Users).filter(Users.id == user, Users.is_deleted == False,
                                                 Users.company_id == company).first()
                if not db_user:
                    raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                           detail=get_message("user_not_found", language))
            else:
                db_user = db.query(Users).filter(Users.id == user, Users.is_deleted == False).first()
                if not db_user:
                    raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                           detail=get_message("user_not_found", language))

            #  TODO : Delete user group
            db_user_groups = db.query(UserGroup).filter(UserGroup.user_id == user).all()
            for user_group in db_user_groups:
                user_group.is_deleted = True
                user_group.deleted_at = datetime.now()
                db.commit()
                db.refresh(user_group)
            stmt = update(Token).where(Token.user_id == user).values(
                is_active=False, is_deleted=True, updated_at=datetime.now())
            db.execute(stmt)
            db_user.is_deleted = True
            db_user.is_active = False
            db_user.deleted_at = datetime.now()
            db.commit()
            db.refresh(db_user)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.UPDATE_PROFILE.endpoint_name,
    path=AccessEndpoint.UPDATE_PROFILE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[UserReps],
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def update_profile(
        request: Request,
        name: Optional[str] = Form(None),
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        file: Optional[UploadFile] = File(None),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        #   Check user exist
        logo_url = None
        auth_info = request.state.user.to_dict()
        uid = auth_info["uid"]
        if not uid:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("something_wrong", language))

        #  Update user
        users = db.query(Users).filter(Users.uid == uid, Users.is_active == True).first()
        if file:
            storage_client = storage.Client.from_service_account_json(settings.GOOGLE_APPLICATION_CREDENTIALS)
            bucket_name = settings.BUCKET_NAME
            # storage_client = storage.Client.from_service_account_json('app/helpers/credentials-prod.json')
            # bucket_name = "musashino-storage-saved"
            destination_blob_name = f"uploads/logo/{file.filename.strip()}"
            filename = file.filename
            file_extension = filename.split('.')[-1].lower()
            if file_extension in ["jpg", "jpeg"]:
                content_type = "image/jpeg"
            elif file_extension == "png":
                content_type = "image/png"
            else:
                content_type = "application/octet-stream"
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_file(file.file, content_type=content_type)
            blob.content_type = file.content_type
            blob.content_disposition = "inline"
            logo_url = blob.public_url
        if not name:
            users.full_name = users.full_name
        else:
            users.full_name = name

        if file:
            users.avatar_url = logo_url

        db.commit()
        db.refresh(users)
        user_data = {key: value for key, value in users.__dict__.items() if key != 'is_deleted'}
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.DOWNLOAD_TEMPLATE_CREATE_USERS.endpoint_name,
    path=AccessEndpoint.DOWNLOAD_TEMPLATE_CREATE_USERS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def download_template_create_users(
        company: int,
        language: Optional[str] = "jp"
):
    try:
        if language == "jp":
            template_path = "templates/スタッフ追加.xlsx"
        else:
            template_path = "templates/ins_staff.xlsx"

        workbook = openpyxl.load_workbook(template_path)
        sheet = workbook.active

        data = [
            ["DEMO", "<EMAIL>", "T123456aA@", ""]
        ]

        for i, row in enumerate(data, start=3):
            sheet.cell(row=i, column=1, value=row[0])
            sheet.cell(row=i, column=2, value=row[1])
            sheet.cell(row=i, column=3, value=row[2])
            sheet.cell(row=i, column=4, value=row[3])

        # Ensure the directory exists
        os.makedirs("uploads", exist_ok=True)

        # Save the file to the server
        now = datetime.now().strftime("%Y%m%d%H%M%S")
        if language == "jp":
            file_path = os.path.join("uploads", f"スタッフ追加.xlsx")
        else:
            file_path = os.path.join("uploads", f"ins_staff.xlsx")
        workbook.save(file_path)
        # file_path = template_create_users(language, company)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data={"file_path": file_path})

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.DOWNLOAD_TEMPLATE_UPDATE_USERS.endpoint_name,
    path=AccessEndpoint.DOWNLOAD_TEMPLATE_UPDATE_USERS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def download_template_update_users(
        company: int,
        userUpdateExcel: UserUpdateExcel,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
):
    try:
        file_path = template_update_users(userUpdateExcel, language, db, company)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data={"file_path": file_path})
    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


# TODO: Update user with role and group


@router.post(
    name=AccessEndpoint.UPLOAD_USERS_EXCEL.endpoint_name,
    path=AccessEndpoint.UPLOAD_USERS_EXCEL.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def upload_users_excel(
        company: int,
        file: UploadFile = File(...),
        language: Optional[str] = "jp",
        db: Session = Depends(get_db),
):
    if not file.filename.endswith((".xlsx", ".xls")):
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("validate_file_excel", language))

    try:
        data = upload_users_and_process(file, language, db, company)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.SYNC_USER_ZOHO.endpoint_name,
    path=AccessEndpoint.SYNC_USER_ZOHO.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def sync_users_zoho(
        background_tasks: BackgroundTasks,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db),
) -> DataResponse:
    try:
        user_service = ZohoPlatform()
        background_tasks.add_task(user_service.get_list_users_zoho, db)

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data="Zoho からデータを同期しています。" if language == "jp" else "Syncing data from Zoho."
        )

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.DOWNLOAD_CSV_TEMPLATE.endpoint_name,
    path=AccessEndpoint.DOWNLOAD_CSV_TEMPLATE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def download_csv_template(
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
):
    try:
        file_path = fonfun_csv_template(db)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data={"file_path": file_path})

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.DOWNLOAD_TO_CSV.endpoint_name,
    path=AccessEndpoint.DOWNLOAD_TO_CSV.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def download_to_csv(
        userUpdateExcel: UserUpdateExcel,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
):
    try:
        file_path = fonfun_download_to_csv(userUpdateExcel, language, db)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data={"file_path": file_path})

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.UPLOAD_BY_CSV.endpoint_name,
    path=AccessEndpoint.UPLOAD_BY_CSV.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def upload_by_csv(
        file: UploadFile = File(...),
        language: Optional[str] = "jp",
        db: Session = Depends(get_db),
):
    if not file.filename.endswith(".csv"):
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("validate_file_csv", language))
    try:
        fonfun_upload_by_csv(file, language, db)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.SET_PASSWORD_SUPER_ADMIN.endpoint_name,
    path=AccessEndpoint.SET_PASSWORD_SUPER_ADMIN.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def set_password_super_admin(
        email: str,
        newPassword: str,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        is_strong, score, message = check_password_strength(newPassword, language)
        if not is_strong:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[message])

        users = db.query(Users).filter(Users.is_deleted == False, Users.is_active == True,
                                       Users.email == email).first()
        if not users:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))
        for user in users:
            user.hashed_password = hash_password(newPassword)
            db.commit()
            db.refresh(user)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("password_update_success", language))

    except GatewayException:
        raise

    except Exception:
        print(f"[POST][GATEWAY ERROR] AUTH CHANGE PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.SET_PASSWORD_SEMINAR.endpoint_name,
    path=AccessEndpoint.SET_PASSWORD_SEMINAR.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def set_password_seminar(
        newPassword: str,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        is_strong, score, message = check_password_strength(newPassword, language)
        if not is_strong:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[message])

        users = db.query(Users).filter(Users.is_deleted == False, Users.is_active == True,
                                       Users.email.like('%test%')).all()
        for user in users:
            user.hashed_password = hash_password(newPassword)
            db.commit()
            db.refresh(user)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("password_update_success", language))

    except GatewayException:
        raise

    except Exception:
        print(f"[POST][GATEWAY ERROR] AUTH CHANGE PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USERS_DELETED.endpoint_name,
    path=AccessEndpoint.GET_USERS_DELETED.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[Page[UserReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_users_deleted(
        company: int,
        request: Request,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        search: Optional[str] = Query(None, description="Search by User Name, or User Email"),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        statement = db.query(Users).filter(
            Users.is_deleted == True,
            Users.company_id == company,
            Users.is_musashino == False
        )

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (Users.full_name.ilike(f"%{search}%")) |
                (Users.email.ilike(f"%{search}%"))
            )

        users = CRUDBase(Users).list(db=db, query=statement.order_by(Users.id.desc()), params=page)
        user_data_list = []

        for user in users.items:
            user_company = db.query(Companies).filter(Companies.id == user.company_id).first()
            user_data = UserReps(
                **user.to_dict()
            )
            user_data.company = user_company
            user_data_list.append(user_data)
        paginated_response = Page[UserReps].create(items=user_data_list, metadata=users.metadata)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=paginated_response)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.ACTIVE_USER_DELETED.endpoint_name,
    path=AccessEndpoint.ACTIVE_USER_DELETED.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def active_user_deleted(
        company: int,
        listUser: ListUserActive,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        for user in listUser.list_user:
            db_user = db.query(Users).filter(Users.id == user, Users.is_deleted == True,
                                             Users.company_id == company).first()
            if not db_user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("user_not_found", language))
        for users in listUser.list_user:
            db_user = db.query(Users).filter(Users.id == users, Users.is_deleted == True).first()
            db_user.is_deleted = False
            db_user.is_active = False
            db.commit()
            db.refresh(db_user)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("active_successfully", language))

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
