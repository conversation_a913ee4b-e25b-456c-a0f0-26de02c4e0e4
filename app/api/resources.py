import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.helpers.exceptions import GatewayException
from app.helpers.paging import Page, PaginationParams
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.resouces import ResourceReps, ResourceCreate, ResourceUpdate
from app.models.resources import Resources
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_RESOURCE.endpoint_name,
    path=AccessEndpoint.CREATE_RESOURCE.endpoint_path,
    response_model=DataResponse[ResourceReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_resource(
        createRequest: ResourceCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_exist = Resources(**createRequest.dict())
        db.add(data_exist)
        db.commit()
        db.refresh(data_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_RESOURCE.endpoint_name,
    path=AccessEndpoint.GET_RESOURCE.endpoint_path,
    response_model=DataResponse[ResourceReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_resource(
        resource_usage_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Resources).filter(Resources.id == resource_usage_id,
                                             Resources.is_deleted == is_deleted).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_RESOURCES.endpoint_name,
    path=AccessEndpoint.GET_RESOURCES.endpoint_path,
    response_model=DataResponse[Page[ResourceReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_resource(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if search and all(char in "%_" for char in search):
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )
        statement = db.query(Resources).filter(Resources.is_deleted == is_deleted)
        data = CRUDBase(Resources).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_RESOURCE.endpoint_name,
    path=AccessEndpoint.UPDATE_RESOURCE.endpoint_path,
    response_model=DataResponse[ResourceReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_resource(
        resource_usage_id: str,
        updateRequest: ResourceUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Resources).filter(
            Resources.id == resource_usage_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        db_data.name = updateRequest.name
        db_data.type = updateRequest.type
        db_data.description = updateRequest.description
        db_data.description = updateRequest.resource_metadata

        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_RESOURCE.endpoint_name,
    path=AccessEndpoint.DELETE_RESOURCE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_resource(
        resource_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Resources).filter(Resources.id == resource_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
