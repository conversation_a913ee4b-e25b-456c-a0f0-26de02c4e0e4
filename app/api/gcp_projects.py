import traceback
import os
from datetime import datetime, time, date
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_super_admin, AuthPermission<PERSON>hecker
from app.schemas.gcp_projects import GcpProjectCreate, GcpProjectReps, GcpProjectUpdate, UpdateCurrentProject
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.models.gcp_projects import GCPProject
from app.helpers.paging import Page, PaginationParams
from fastapi import Query
from app.services.base import CRUDBase
from app.models.datastores_information import DatastoresInformation
from app.models.agent_builders_information import AgentBuildersInformation
from app.core.cloud_engine.google import GoogleCloudPlatform
from app.models.config import Configuration
from app.helpers.constants import Project
from app.schemas.configuration import ConfigResp

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_GCP_PROJECT.endpoint_name,
    path=AccessEndpoint.CREATE_GCP_PROJECT.endpoint_path,
    response_model=DataResponse[GcpProjectReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def create_gcp_project(
        create: GcpProjectCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        google = GoogleCloudPlatform()
        project_exists = db.query(GCPProject).filter(
            GCPProject.project_id == create.project_id,
            GCPProject.is_deleted == False
        ).first()
        if project_exists:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("record_exist", language)
            )
        google_projects = google.fetch_projects_from_gcp()
        google_project_ids = [p["project_id"] for p in google_projects]

        if create.project_id not in google_project_ids:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("project_not_found_on_gcp", language)
            )
        db_project = GCPProject(**create.dict())
        db.add(db_project)
        db.commit()
        db.refresh(db_project)

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data=db_project
        )

    except GatewayException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_GCP_PROJECT.endpoint_name,
    path=AccessEndpoint.GET_GCP_PROJECT.endpoint_path,
    response_model=DataResponse[GcpProjectReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def get_gcp_project(
        gcp_project_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        google = GoogleCloudPlatform()
        project = db.query(GCPProject).filter(
            GCPProject.id == gcp_project_id,
            GCPProject.is_deleted == False
        ).first()

        if not project:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("gcp_project_not_found", language)
            )
        get_data_store_usage = db.query(DatastoresInformation).filter(
            DatastoresInformation.project_id == project.project_id,
            DatastoresInformation.is_deleted == False
        ).count()
        get_agent_builder_usage = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.project_id == project.project_id,
            AgentBuildersInformation.is_deleted == False
        ).count()

        current_datastore_usage_percentage = get_data_store_usage / project.datastore_quota * 100 if project.datastore_quota else 0
        current_agent_builder_percentage = get_agent_builder_usage / project.agent_builder_quota * 100 if project.agent_builder_quota else 0

        project.datastore_usage = get_data_store_usage
        project.agent_builder_usage = get_agent_builder_usage
        project.current_datastore_usage_percentage = round(current_datastore_usage_percentage, 2)
        project.current_agent_builder_percentage = round(current_agent_builder_percentage, 2)
        # try:
        #     gcp_quota = google.get_discovery_usage(project.project_id, "global")
        #     project.datastore_usage_on_gcp = gcp_quota.get("datastores", {})
        #     project.agent_builder_usage_on_gcp = gcp_quota.get("engines", {})
        # except Exception as e:
        #     print(f"[Quota] Failed to get quota for project {project.project_id}: {e}")
        #     project.datastore_usage_on_gcp = None
        #     project.agent_builder_usage_on_gcp = None
        db_config = db.query(Configuration).filter(Configuration.config_key == Project.key,
                                                   Configuration.is_active == True,
                                                   Configuration.is_deleted == False).first()
        if db_config and db_config.config_value == project.project_id:
            project.in_use = True
        else:
            project.in_use = False
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=project)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_GCP_PROJECTS.endpoint_name,
    path=AccessEndpoint.GET_GCP_PROJECTS.endpoint_path,
    response_model=DataResponse[Page[GcpProjectReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def get_all_gcp_project(
        db: Session = Depends(get_db),
        is_active: Optional[bool] = True,
        is_deleted: Optional[bool] = False,
        search: Optional[str] = Query(None, description="Search by project name, project id"),
        page: PaginationParams = Depends(),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        google = GoogleCloudPlatform()
        statement = db.query(GCPProject).filter(
            GCPProject.is_active == is_active,
            GCPProject.is_deleted == is_deleted
        )
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                GCPProject.project_name.ilike(f"%{search}%", escape='\\'),
                GCPProject.project_id.ilike(f"%{search}%", escape='\\')
            )
        projects = CRUDBase(GCPProject).list(db=db, query=statement, params=page)
        for project in projects.items:
            get_data_store_usage = db.query(DatastoresInformation).filter(
                DatastoresInformation.project_id == project.project_id,
                DatastoresInformation.is_deleted == False
            ).count()
            get_agent_builder_usage = db.query(AgentBuildersInformation).filter(
                AgentBuildersInformation.project_id == project.project_id,
                AgentBuildersInformation.is_deleted == False
            ).count()

            current_datastore_usage_percentage = get_data_store_usage / project.datastore_quota * 100 if project.datastore_quota else 0
            current_agent_builder_percentage = get_agent_builder_usage / project.agent_builder_quota * 100 if project.agent_builder_quota else 0

            project.datastore_usage = get_data_store_usage
            project.agent_builder_usage = get_agent_builder_usage
            project.current_datastore_usage_percentage = round(current_datastore_usage_percentage, 2)
            project.current_agent_builder_percentage = round(current_agent_builder_percentage, 2)
            # try:
            #     gcp_quota = google.get_discovery_usage(project.project_id, "global")
            #     project.datastore_usage_on_gcp = gcp_quota.get("datastores", {})
            #     project.agent_builder_usage_on_gcp = gcp_quota.get("engines", {})
            # except Exception as e:
            #     print(f"[Quota] Failed to get quota for project {project.project_id}: {e}")
            #     project.datastore_usage_on_gcp = None
            #     project.agent_builder_usage_on_gcp = None

            db_config = db.query(Configuration).filter(Configuration.config_key == Project.key,
                                                       Configuration.is_active == True,
                                                       Configuration.is_deleted == False).first()
            if db_config and db_config.config_value == project.project_id:
                project.in_use = True
            else:
                project.in_use = False

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=projects)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_GCP_PROJECT.endpoint_name,
    path=AccessEndpoint.UPDATE_GCP_PROJECT.endpoint_path,
    response_model=DataResponse[GcpProjectReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def update_gcp_project(
        gcp_project_id: int,
        update: GcpProjectUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        project = db.query(GCPProject).filter(
            GCPProject.id == gcp_project_id,
            GCPProject.is_deleted == False
        ).first()

        if not project:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("not_found", language)
            )

        for key, value in update.dict(exclude_unset=True).items():
            setattr(project, key, value)

        db.commit()
        db.refresh(project)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=project)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.delete(
    name=AccessEndpoint.DELETE_GCP_PROJECT.endpoint_name,
    path=AccessEndpoint.DELETE_GCP_PROJECT.endpoint_path,
    response_model=DataResponse[str],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def delete_gcp_project(
        gcp_project_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        project = db.query(GCPProject).filter(
            GCPProject.id == gcp_project_id,
            GCPProject.is_deleted == False
        ).first()

        if not project:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("not_found", language)
            )

        project.is_deleted = True
        project.is_active = False
        project.deleted_at = datetime.utcnow()

        db.commit()
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_PROJECTS_ON_GCP.endpoint_name,
    path=AccessEndpoint.GET_PROJECTS_ON_GCP.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def get_all_projects_on_gcp(
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        google = GoogleCloudPlatform()
        projects = google.fetch_projects_from_gcp()
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=projects)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_CURRENT_PROJECT.endpoint_name,
    path=AccessEndpoint.UPDATE_CURRENT_PROJECT.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def update_current_project(
        update: UpdateCurrentProject,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        project = db.query(Configuration).filter(
            Configuration.config_key == Project.key,
            Configuration.is_deleted == False
        ).first()

        if not project:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("not_found", language)
            )
        project.config_value = update.current_project
        db.commit()
        db.refresh(project)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=project)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )
