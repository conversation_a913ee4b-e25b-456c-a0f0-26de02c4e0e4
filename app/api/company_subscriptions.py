import traceback
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.db.base import get_db
from app.services.base import CRUDBase
from app.schemas.base import DataResponse
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermissionChecker
from app.models.company_subscriptions import CompanySubscriptions
from app.schemas.company_subscription import CompanySubReps, CompanySubUpdate, CompanySubCreate
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_COMPANY_SUBSCRIPTION.endpoint_name,
    path=AccessEndpoint.CREATE_COMPANY_SUBSCRIPTION.endpoint_path,
    response_model=DataResponse[CompanySubReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermission<PERSON>he<PERSON>(validate_admin))]
)
async def create_company_subscription(
        companySub: CompanySubCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        company_sub = db.query(exists().where(CompanySubscriptions.company_id == companySub.company_id,
                                              CompanySubscriptions.package_id == companySub.package_id)).scalar()
        if company_sub:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        company_sub = CompanySubscriptions(**companySub.dict())
        db.add(company_sub)
        db.commit()
        db.refresh(company_sub)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=company_sub)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_COMPANY_SUBSCRIPTION.endpoint_name,
    path=AccessEndpoint.GET_COMPANY_SUBSCRIPTION.endpoint_path,
    response_model=DataResponse[CompanySubReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_company_subscription(
        company_sub_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_company_sub = db.query(CompanySubscriptions).filter(CompanySubscriptions.id == company_sub_id,
                                                               CompanySubscriptions.is_deleted == is_deleted).first()
        if not db_company_sub:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_company_sub)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_COMPANY_SUBSCRIPTIONS.endpoint_name,
    path=AccessEndpoint.GET_COMPANY_SUBSCRIPTIONS.endpoint_path,
    response_model=DataResponse[Page[CompanySubReps]],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_company_subscription(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(CompanySubscriptions).filter(CompanySubscriptions.is_deleted == is_deleted)
        companies = CRUDBase(CompanySubscriptions).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=companies)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_COMPANY_SUBSCRIPTION.endpoint_name,
    path=AccessEndpoint.UPDATE_COMPANY_SUBSCRIPTION.endpoint_path,
    response_model=DataResponse[CompanySubReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_company_subscription(
        company_sub_id: str,
        companies: CompanySubUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_company_sub = db.query(CompanySubscriptions).filter(CompanySubscriptions.id == company_sub_id).first()
        if not db_company_sub:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_company_sub.company_id = companies.company_id
        db_company_sub.package_id = companies.package_id
        db_company_sub.start_date = companies.start_date
        db_company_sub.end_date = companies.end_date
        db_company_sub.status = companies.status

        db.commit()
        db.refresh(db_company_sub)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_company_sub)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_COMPANY_SUBSCRIPTION.endpoint_name,
    path=AccessEndpoint.DELETE_COMPANY_SUBSCRIPTION.endpoint_path,
    response_model=DataResponse,
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_company_subscription(
        company_sub_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_company_sub = db.query(CompanySubscriptions).filter(CompanySubscriptions.id == company_sub_id).first()
        if not db_company_sub:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_company_sub.is_deleted = True
        db_company_sub.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_company_sub)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
