import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.db.base import get_db
from app.services.base import CRUDBase
from app.schemas.base import DataResponse
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.models.datastores import Datastores
from app.schemas.datastores import DatastoreCreate, DatastoreUpdate, DatastoreReps
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_DATASTORE.endpoint_name,
    path=AccessEndpoint.CREATE_DATASTORE.endpoint_path,
    response_model=DataResponse[DatastoreReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_datastores(
        datastore: DatastoreCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_exist = db.query(exists().where(Datastores.datastore_name == Datastores.datastore_name)).scalar()
        if data_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        data_exist = Datastores(**datastore.dict())
        db.add(data_exist)
        db.commit()
        db.refresh(data_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DATASTORE.endpoint_name,
    path=AccessEndpoint.GET_DATASTORE.endpoint_path,
    response_model=DataResponse[DatastoreReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_datastore(
        department_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Datastores).filter(Datastores.id == department_id,
                                              Datastores.is_deleted == is_deleted).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DATASTORES.endpoint_name,
    path=AccessEndpoint.GET_DATASTORES.endpoint_path,
    response_model=DataResponse[Page[DatastoreReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_datastore(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Datastores).filter(Datastores.is_deleted == is_deleted)
        datastores = CRUDBase(Datastores).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datastores)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_DATASTORE.endpoint_name,
    path=AccessEndpoint.UPDATE_DATASTORE.endpoint_path,
    response_model=DataResponse[DatastoreReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_data_store(
        data_store_id: str,
        dataStore: DatastoreUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Datastores).filter(Datastores.id == data_store_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.datastore_name = dataStore.datastore_name
        db_data.description = dataStore.description
        db_data.status = dataStore.status
        db_data.storage_used = dataStore.storage_used
        db_data.document_count = dataStore.document_count
        db_data.is_active = dataStore.is_active

        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_DATASTORE.endpoint_name,
    path=AccessEndpoint.DELETE_DATASTORE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_data_store(
        data_store_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(Datastores).filter(Datastores.id == data_store_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
