import os
import traceback
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthP<PERSON><PERSON><PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.companies import CompanyCreate, CompanyReps, CompanyUpdate, CompanyFilter
from app.models.companies import Companies
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
import os
from app.helpers.config import settings
from google.cloud import storage
from app.utils.common import generate_id_code
from app.models.service_packages import ServicePackage
from app.models.company_subscriptions import CompanySubscriptions
from app.models.users import Users, UserRole, UserGroup
from fastapi import Query
from app.models.datastores_information import DatastoresInformation
from app.models.agent_builders_information import AgentBuildersInformation
from app.models.notifications import Notifications
from app.models.ai_services import UseCases
from sqlalchemy import update
from app.models.token import Token
from app.models.gcp_projects import GCP<PERSON>roject
from sqlalchemy import func

router = APIRouter()

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.GOOGLE_APPLICATION_CREDENTIALS
client = storage.Client()


@router.post(
    name=AccessEndpoint.CREATE_COMPANY.endpoint_name,
    path=AccessEndpoint.CREATE_COMPANY.endpoint_path,
    response_model=DataResponse[CompanyReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_company(
        company: CompanyCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
        bucket_name: str = "musashino-rag",
        root_folder: str = "customers"
) -> DataResponse:
    try:
        # TODO : GET max_data_store and max_agent_builder from gcp project
        max_data_store = db.query(func.sum(GCPProject.datastore_quota)).filter(GCPProject.is_active == True,
                                                                               GCPProject.is_deleted == False).scalar()

        max_agent_builder = db.query(func.sum(GCPProject.agent_builder_quota)).filter(GCPProject.is_active == True,
                                                                                      GCPProject.is_deleted == False).scalar()
        # max_data_store = int(os.getenv('MAX_DATASTORES', 1000))
        # max_agent_builder = int(os.getenv('MAX_AGENT_BUILDERS', 1000))
        total_data_store = db.query(DatastoresInformation).filter(DatastoresInformation.is_active == True,
                                                                  DatastoresInformation.is_deleted == False).count()
        total_agent_builder = db.query(AgentBuildersInformation).filter(AgentBuildersInformation.is_active == True,
                                                                        AgentBuildersInformation.is_deleted == False).count()
        if language == "en":
            title = "Notification limit reached."
            ds_limit = (f"Data Store is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
            ab_limit = (f"Agent Builder is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
        else:
            title = "通知制限に達しました。"
            ds_limit = (f"Data Store のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")
            ab_limit = (f"Agent Builder のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")

        if ((total_data_store + company.max_datastores) / max_data_store) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ds_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        if ((total_agent_builder + company.max_agent_builders) / max_agent_builder) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ab_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        # if ((total_data_store + company.max_datastores) / max_data_store) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_datastore_exceed", language))
        # if ((total_agent_builder + company.max_agent_builders) / max_agent_builder) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_agent_builder_exceed", language))

        if len(company.company_slug) > 30:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("slug_too_long", language))
        # package_exist = db.query(ServicePackage).filter(ServicePackage.id == company.package_id,
        #                                                 ServicePackage.is_active == True,
        #                                                 ServicePackage.is_deleted == False).first()
        # if package_exist is None:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("package_not_exist", language))
        company_name_exist = db.query(Companies).filter(Companies.company_name == company.company_name,
                                                        Companies.is_active == True,
                                                        Companies.is_deleted == False).first()
        company_slug_exist = db.query(Companies).filter(Companies.company_slug == company.company_slug,
                                                        Companies.is_active == True,
                                                        Companies.is_deleted == False).first()
        if company_slug_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("company_exist", language))

        package_exist = db.query(ServicePackage).filter(ServicePackage.package_name == company.package_name,
                                                        ServicePackage.is_active == True,
                                                        ServicePackage.is_deleted == False).first()
        if package_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("package_exist", language))

        db_company = Companies(**company.dict(
            exclude={"package_id", "package_name", "description", "max_datastores", 'max_agent_builders',
                     "max_queries_per_day",
                     "max_documents_per_query",
                     "max_users", "max_admins", "max_managers", "storage_limit", "credits_package_limit",
                     "llm_model_type", "max_tokens_per_month", "conversation_retention_days",
                     "max_concurrent_conversations", "encryption_level"}), )
        db_company.company_code = generate_id_code(length=10)
        db.add(db_company)
        db.commit()
        db.refresh(db_company)

        db_package = ServicePackage(**company.dict(
            exclude={"company_name", "company_slug", "company_type", "parent_company_id", "tax_code",
                     "registration_number", "address", "ward", "province", "city",
                     "phone", "website_url", "email",
                     "industry", "number_of_employees", "number_of_employees"}))
        db.add(db_package)
        db.commit()
        db.refresh(db_package)

        subscription = CompanySubscriptions(
            company_id=db_company.id,
            package_id=db_package.id,
            start_date=datetime.utcnow(),
            end_date=None,
            status=1
        )
        db.add(subscription)
        db.commit()
        bucket = client.bucket(bucket_name)

        if not bucket.exists():
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        if not bucket.iam_configuration.uniform_bucket_level_access_enabled:
            bucket.iam_configuration.uniform_bucket_level_access_enabled = True
            bucket.patch()

        if not bucket.iam_configuration.public_access_prevention == 'enforced':
            bucket.iam_configuration.public_access_prevention = 'enforced'
            bucket.patch()
        folder_name = f"{root_folder}/COMPANY_" + str(db_company.company_slug)
        blob = bucket.blob(f"{folder_name}/")
        blob.upload_from_string('')
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_company)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_COMPANY.endpoint_name,
    path=AccessEndpoint.GET_COMPANY.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_company(
        company_id: int,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        # Lấy thông tin công ty
        db_company = (
            db.query(Companies)
            .filter(
                Companies.id == company_id,
                Companies.is_deleted == is_deleted
            )
            .first()
        )
        if not db_company:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        # Tính số lượng người dùng
        user_count = (
            db.query(Users)
            .filter(
                Users.company_id == company_id, Users.is_active == True,
                Users.is_deleted == False, Users.is_musashino == False
            )
            .count()
        )

        # Lấy thông tin gói dịch vụ
        registered_pk = (
            db.query(ServicePackage)
            .join(CompanySubscriptions, ServicePackage.id == CompanySubscriptions.package_id)
            .filter(
                CompanySubscriptions.company_id == company_id,
                CompanySubscriptions.is_active == True,
                CompanySubscriptions.is_deleted == False
            )
            .first()
        )

        company_data = vars(db_company)  # Chuyển db_company thành dictionary
        package_data = vars(registered_pk) if registered_pk else {}

        # Loại bỏ các khóa nội bộ của SQLAlchemy (bắt đầu bằng '_sa_')
        company_data = {k: v for k, v in company_data.items() if not k.startswith('_sa_')}
        package_data = {k: v for k, v in package_data.items() if not k.startswith('_sa_')}

        data_store_count = db.query(DatastoresInformation).filter(DatastoresInformation.company_id == company_id,
                                                                  DatastoresInformation.is_active == True,
                                                                  DatastoresInformation.is_deleted == False).count()
        agent_builder_count = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company_id,
            AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False).count()
        use_case_count = db.query(UseCases).filter(UseCases.company_id == company_id,
                                                   UseCases.is_deleted == False).count()
        merged_data = {
            **company_data,
            "package_registered": package_data,
            "user_count": user_count,
            "number_of_use_cases": use_case_count,
            "number_of_data_stores": data_store_count,
            "number_of_agent_builders": agent_builder_count,
        }

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=merged_data
        )

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_COMPANIES.endpoint_name,
    path=AccessEndpoint.GET_COMPANIES.endpoint_path,
    response_model=DataResponse[Page[CompanyReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_companies(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: CompanyFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by company name or phone"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Companies).filter(Companies.is_deleted == is_deleted,
                                               Companies.company_name != "Musashino", Companies.id != 1)
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (Companies.company_name.ilike(f"%{search}%")) |
                (Companies.phone.ilike(f"%{search}%"))
            )

        if filters.company_name:
            statement = statement.filter(
                Companies.company_name.ilike(f"%{filters.company_name}%")
            )

        if filters.phone:
            statement = statement.filter(
                Companies.phone.ilike(f"%{filters.phone}%")
            )
        if filters.is_active == "true":
            statement = statement.filter(
                Companies.is_active == True
            )
        elif filters.is_active == "false":
            statement = statement.filter(
                Companies.is_active == False
            )
        else:
            statement = statement
        companies = CRUDBase(Companies).list(db=db, query=statement, params=page)
        for company in companies.items:
            user_count = db.query(Users).filter(Users.company_id == company.id, Users.is_deleted == False,
                                                Users.is_active == True,
                                                Users.is_musashino == False).count()
            registered_pk = db.query(ServicePackage).join(CompanySubscriptions,
                                                          ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == company.id).first()
            if registered_pk:
                company.registered_package = registered_pk
                company.max_users = registered_pk.max_users
                company.created_users = user_count
            data_store_count = db.query(DatastoresInformation).filter(DatastoresInformation.company_id == company.id,
                                                                      DatastoresInformation.is_active == True,
                                                                      DatastoresInformation.is_deleted == False).count()
            agent_builder_count = db.query(AgentBuildersInformation).filter(
                AgentBuildersInformation.company_id == company.id,
                AgentBuildersInformation.is_active == True,
                AgentBuildersInformation.is_deleted == False).count()
            use_case_count = db.query(UseCases).filter(UseCases.company_id == company.id,
                                                       UseCases.is_deleted == False).count()
            company.number_of_use_cases = use_case_count
            company.number_of_data_stores = data_store_count
            company.number_of_agent_builders = agent_builder_count
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=companies)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_COMPANY.endpoint_name,
    path=AccessEndpoint.UPDATE_COMPANY.endpoint_path,
    response_model=DataResponse[CompanyReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_company(
        company_id: int,
        companies: CompanyUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if len(companies.company_slug) > 30:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("slug_too_long", language))
        db_company = db.query(Companies).filter(
            Companies.id == company_id,
            Companies.is_deleted == False
        ).first()
        if not db_company:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        company_name_exist = db.query(Companies).filter(
            Companies.company_name == companies.company_name,
            Companies.id != company_id,
            Companies.is_active == True,
            Companies.is_deleted == False
        ).first()

        company_slug_exist = db.query(Companies).filter(
            Companies.company_slug == companies.company_slug,
            Companies.id != company_id,
            Companies.is_active == True,
            Companies.is_deleted == False
        ).first()

        if company_slug_exist:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("company_exist", language)
            )

        package_registered = db.query(CompanySubscriptions).filter(CompanySubscriptions.company_id == company_id,
                                                                   CompanySubscriptions.is_active == True,
                                                                   CompanySubscriptions.is_deleted == False).first()
        if not package_registered:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        count_user = db.query(Users).join(Companies, Users.company_id == Companies.id).join(CompanySubscriptions,
                                                                                            Companies.id == CompanySubscriptions.company_id).join(
            ServicePackage, ServicePackage.id == CompanySubscriptions.package_id).filter(
            Users.company_id == company_id,
            Users.is_deleted == False,
            Users.is_active == True,
            CompanySubscriptions.is_active == True,
            CompanySubscriptions.status == 1
        ).count()

        if count_user > (companies.max_users + 1):
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_user_exceed", language))

        db_package = db.query(ServicePackage).filter(ServicePackage.id == package_registered.package_id,
                                                     # ServicePackage.is_active == True,
                                                     ServicePackage.is_deleted == False).first()

        # TODO : GET max_data_store and max_agent_builder from gcp project
        max_data_store = db.query(func.sum(GCPProject.datastore_quota)).filter(GCPProject.is_active == True,
                                                                               GCPProject.is_deleted == False).scalar()

        max_agent_builder = db.query(func.sum(GCPProject.agent_builder_quota)).filter(GCPProject.is_active == True,
                                                                                      GCPProject.is_deleted == False).scalar()
        # max_data_store = int(os.getenv('MAX_DATASTORES', 1000))
        # max_agent_builder = int(os.getenv('MAX_AGENT_BUILDERS', 1000))

        total_data_store = db.query(DatastoresInformation).filter(DatastoresInformation.is_active == True,
                                                                  DatastoresInformation.is_deleted == False).count()
        total_agent_builder = db.query(AgentBuildersInformation).filter(AgentBuildersInformation.is_active == True,
                                                                        AgentBuildersInformation.is_deleted == False).count()
        if language == "en":
            title = "Notification limit reached."
            ds_limit = (f"Data Store is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
            ab_limit = (f"Agent Builder is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
        else:
            title = "通知制限に達しました。"
            ds_limit = (f"Data Store のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")
            ab_limit = (f"Agent Builder のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")

        if ((total_data_store + companies.max_datastores) / max_data_store) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ds_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        if ((total_agent_builder + companies.max_agent_builders) / max_agent_builder) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ab_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        # if ((total_data_store + companies.max_datastores) / max_data_store) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_datastore_exceed", language))
        # if ((total_agent_builder + companies.max_agent_builders) / max_agent_builder) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_agent_builder_exceed", language))
        data_store_per_company = db.query(DatastoresInformation).filter(DatastoresInformation.company_id == company_id,
                                                                        DatastoresInformation.is_active == True,
                                                                        DatastoresInformation.is_deleted == False).count()
        agent_builder_per_company = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company_id,
            AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False).count()
        if companies.max_datastores < data_store_per_company:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_datastore_less", language))
        if companies.max_agent_builders < agent_builder_per_company:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_agent_builder_less", language))
        # TODO: Update company
        db_company.company_name = companies.company_name
        db_company.company_slug = companies.company_slug
        db_company.company_type = companies.company_type
        db_company.parent_company_id = companies.parent_company_id
        db_company.tax_code = companies.tax_code
        db_company.registration_number = companies.registration_number
        db_company.address = companies.address
        db_company.ward = companies.ward
        db_company.province = companies.province
        db_company.city = companies.city
        db_company.phone = companies.phone
        db_company.website_url = companies.website_url
        db_company.email = companies.email
        db_company.industry = companies.industry
        db_company.number_of_employees = companies.number_of_employees
        db_company.is_active = companies.is_active

        # TODO: Update package
        db_package.package_name = companies.package_name
        db_package.description = companies.description
        db_package.max_datastores = companies.max_datastores
        db_package.max_agent_builders = companies.max_agent_builders
        db_package.max_queries_per_day = companies.max_queries_per_day
        db_package.max_documents_per_query = companies.max_documents_per_query
        db_package.max_users = companies.max_users
        db_package.max_admins = companies.max_admins
        db_package.max_managers = companies.max_managers
        db_package.storage_limit = companies.storage_limit
        db_package.credits_package_limit = companies.credits_package_limit
        db_package.llm_model_type = companies.llm_model_type
        db_package.max_tokens_per_month = companies.max_tokens_per_month
        db_package.conversation_retention_days = companies.conversation_retention_days
        db_package.max_concurrent_conversations = companies.max_concurrent_conversations
        db_package.encryption_level = companies.encryption_level
        db_package.is_active = companies.is_active
        db_package.note = companies.note

        list_user_ids = [uid for (uid,) in db.query(Users.id)
        .filter(Users.company_id == company_id)
        .all()]
        if not companies.is_active:
            db.query(Users).filter(Users.company_id == company_id).update(
                {"is_active": False},
                synchronize_session=False
            )
            for user in list_user_ids:
                stmt = update(Token).where(Token.user_id == user).values(
                    is_active=False, is_deleted=True, updated_at=datetime.now())
                db.execute(stmt)

        else:
            db.query(Users).filter(Users.company_id == company_id).update(
                {"is_active": True},
                synchronize_session=False
            )
            for user in list_user_ids:
                stmt = update(Token).where(Token.user_id == user).values(
                    is_active=True, is_deleted=False, updated_at=datetime.now())
                db.execute(stmt)

        db.commit()
        db.refresh(db_company)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_company)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_COMPANY.endpoint_name,
    path=AccessEndpoint.DELETE_COMPANY.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_company(
        company_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_company = db.query(Companies).filter(
            Companies.id == company_id,
            # Companies.is_active == True,
            Companies.is_deleted == False
        ).first()

        if not db_company:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("company_not_found", language)
            )

        db_company.is_deleted = True
        db_company.deleted_at = datetime.now()

        db.query(Users).filter(Users.company_id == company_id).update(
            {"is_deleted": True},
            synchronize_session=False
        )

        list_user_ids = [uid for (uid,) in db.query(Users.id)
        .filter(Users.company_id == company_id)
        .all()]

        for user in list_user_ids:
            stmt = update(Token).where(Token.user_id == user).values(
                is_active=False, is_deleted=True, updated_at=datetime.now())
            db.execute(stmt)
        if list_user_ids:
            db.query(UserRole).filter(
                UserRole.user_id.in_(list_user_ids)
            ).delete(synchronize_session=False)
            db.query(UserGroup).filter(
                UserGroup.user_id.in_(list_user_ids)
            ).delete(synchronize_session=False)
        package_id_registered = db.query(CompanySubscriptions).filter(
            CompanySubscriptions.company_id == company_id).first()
        db_package = db.query(ServicePackage).filter(ServicePackage.id == package_id_registered.package_id).first()
        db_package.is_deleted = True
        db_package.deleted_at = datetime.now()

        db.commit()
        db.refresh(db_company)

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=None
        )

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )
