import traceback
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from fastapi import Query
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthP<PERSON><PERSON><PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.roles import RoleResp, RoleCreate, RoleUpdate, RoleFilter, RoleRespV2
from app.models.users import Role, UserRole, Users, Permission
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.helpers.constants import RoleIgnore
from sqlalchemy import func, or_
from app.models.role_permissions import RolePermission
from collections import defaultdict

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_ROLE.endpoint_name,
    path=AccessEndpoint.CREATE_ROLE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[RoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_role(
        roles: RoleCreate,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        is_role_exists = db.query(
            exists().where(Role.name == roles.name, Role.company_id == roles.company_id, Role.important == False,
                           Role.is_deleted == False)).scalar()
        if is_role_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("role_exist", language))
        if roles.name.upper() == "SUPERADMIN" or roles.name.upper() == "ADMIN" or roles.name.upper() == "MANAGER" or roles.name.upper() == "USER":
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("cannot_create_this_role", language))
        list_permission = roles.permission_ids
        for permission in list_permission:
            is_permission_exists = db.query(Permission).filter(Permission.id == permission,
                                                               Permission.is_deleted == False).first()
            if not is_permission_exists:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("permission_not_exist", language))
        db_role = Role(**roles.dict(exclude={"permission_ids"}))
        db.add(db_role)
        db.commit()

        # TODO: Add new role permission in list_permission
        for permission in list_permission:
            db_permission = RolePermission(role_id=db_role.id, permission_id=permission)
            db.add(db_permission)
            db.commit()
        db.refresh(db_role)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_role)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ROLE.endpoint_name,
    path=AccessEndpoint.GET_ROLE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[RoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_role(
        company: int,
        role_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_role = db.query(Role).filter(Role.id == role_id, Role.is_deleted == is_deleted).first()
        if not db_role:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        list_permission = db.query(Permission).join(RolePermission,
                                                    Permission.id == RolePermission.permission_id).filter(
            Permission.is_deleted == False, Permission.is_active == True, RolePermission.role_id == role_id).all()
        db_role.list_permission = list_permission

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_role)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ROLES.endpoint_name,
    path=AccessEndpoint.GET_ROLES.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[Page[RoleRespV2]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_roles(
        company: int,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: RoleFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by Role Name"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        base_filters = [
            Role.is_active == filters.is_active,
            Role.is_deleted == filters.is_deleted
        ]

        if company == 0:
            statement = db.query(Role).filter(*base_filters)
        else:
            statement = db.query(Role).filter(
                *base_filters,
                Role.company_id == company,
                func.upper(Role.name) != RoleIgnore.SUPER_ADMIN,
                func.upper(Role.name) != RoleIgnore.ADMIN
            )

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(Role.name.ilike(f"%{search}%"))

        roles = CRUDBase(Role).list(db=db, query=statement, params=page)

        # Nếu là trang đầu tiên, thêm fixed_roles (Admin và User)
        if page.page == 1:
            fixed_roles_query = db.query(Role).filter(
                *base_filters,
                or_(Role.name == 'Admin', Role.name == 'User')
            )
            if search:
                fixed_roles_query = fixed_roles_query.filter(Role.name.ilike(f"%{search}%"))
            fixed_roles = fixed_roles_query.all()

            # Loại bỏ các fixed role đã có trong roles.items để tránh trùng lặp
            existing_role_ids = {role.id for role in roles.items}
            fixed_roles_to_prepend = [role for role in fixed_roles if role.id not in existing_role_ids]

            # Kết hợp fixed_roles và các role tìm được rồi giới hạn theo page size
            roles.items = (fixed_roles_to_prepend + roles.items)[:page.page_size]

        role_ids = [role.id for role in roles.items]
        if role_ids:
            user_row_number = func.row_number().over(
                partition_by=UserRole.role_id,
                order_by=UserRole.id
            ).label("row_number")

            subq = (
                db.query(
                    UserRole.role_id.label("role_id"),
                    Users.id.label("user_id"),
                    Users.full_name,
                    Users.avatar_url,
                    user_row_number
                )
                .join(Users, Users.id == UserRole.user_id)
                .filter(UserRole.role_id.in_(role_ids), Users.company_id == company, Users.is_musashino == False)
                .subquery()
            )

            user_results = db.query(
                subq.c.role_id,
                subq.c.user_id,
                subq.c.full_name,
                subq.c.avatar_url
            ).filter(subq.c.row_number <= 5).all()

            users_by_role = defaultdict(list)
            for row in user_results:
                users_by_role[row.role_id].append({
                    "id": row.user_id,
                    "full_name": row.full_name,
                    "avatar_url": row.avatar_url,
                })
        else:
            users_by_role = {}

        for role in roles.items:
            role.list_user_id = users_by_role.get(role.id, [])

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=roles)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_ROLE.endpoint_name,
    path=AccessEndpoint.UPDATE_ROLE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[RoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_role(
        company: int,
        role_id: str,
        role: RoleUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        db_role = db.query(Role).filter(Role.id == role_id, Role.is_deleted == False).first()
        if not db_role:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("role_not_found", language))

        if role.name.upper() == "SUPERADMIN" or role.name.upper() == "ADMIN" or role.name.upper() == "MANAGER" or role.name.upper() == "USER":
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("cannot_create_this_role", language))

        list_permission = role.permission_ids
        for permission in list_permission:
            is_permission_exists = db.query(Permission).filter(Permission.id == permission,
                                                               Permission.is_deleted == False).first()
            if not is_permission_exists:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("permission_not_exist", language))

        # TODO: Delete all role permission in list_permission
        if list_permission:
            db.query(RolePermission).filter(RolePermission.permission_id.in_(list_permission),
                                            RolePermission.role_id == role_id).delete(
                synchronize_session=False)
            db.commit()
        else:
            db.query(RolePermission).filter(RolePermission.role_id == role_id).delete(synchronize_session=False)
            db.commit()

        for permission in list_permission:
            db_permission = RolePermission(role_id=db_role.id, permission_id=permission)
            db.add(db_permission)
            db.commit()

        db_role.description = role.description
        db_role.name = role.name

        db.commit()
        db.refresh(db_role)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_role)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_ROLE.endpoint_name,
    path=AccessEndpoint.DELETE_ROLE.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_role(
        company: int,
        role_id: str,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        db_role = db.query(Role).filter(Role.id == role_id).first()
        if not db_role:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("role_not_found", language))

        db.query(RolePermission).filter(RolePermission.role_id == role_id).delete(synchronize_session=False)
        db.commit()

        db_role.is_deleted = True
        db_role.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_role)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
