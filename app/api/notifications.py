import traceback
from datetime import datetime, time, date
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.db.base import get_db
from app.services.base import CRUDBase
from app.schemas.base import DataResponse
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPer<PERSON><PERSON>he<PERSON>
from app.schemas.notifications import NotificationsReps, NotificationsUpdate, NotificationsCreate, NotificationFilter
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.models.notifications import Notifications
from fastapi import Query

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_NOTIFICATION.endpoint_name,
    path=AccessEndpoint.CREATE_NOTIFICATION.endpoint_path,
    response_model=DataResponse[NotificationsReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermission<PERSON>hecker(validate_admin))]
)
async def create_notification(
        create: NotificationsCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        # notification_db = db.query(exists().where(Notifications.title == create.title,
        #                                           Notifications.is_active == True,
        #                                           Notifications.is_deleted == False)).scalar()
        # if notification_db:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("record_exist", language))
        if create.start_date > create.end_date:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("start_date_must_be_less_than_end_date", language))
        if create.end_date < date.today():
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("end_date_must_be_greater_than_current_date", language))
        if create.start_date < date.today():
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("start_date_must_be_greater_than_current_date", language))
        notification_db = Notifications(**create.dict(exclude={"start_date", "end_date"}))
        fixed_time_start = time(0, 0, 1, 665295)
        fixed_time_end = time(23, 59, 59, 665295)
        notification_db.start_date = datetime.combine(create.start_date, fixed_time_start)
        notification_db.end_date = datetime.combine(create.end_date, fixed_time_end)
        db.add(notification_db)
        db.commit()
        db.refresh(notification_db)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=notification_db)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_NOTIFICATION.endpoint_name,
    path=AccessEndpoint.GET_NOTIFICATION.endpoint_path,
    response_model=DataResponse[NotificationsReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_notification(
        notification_id: int,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_notification = db.query(Notifications).filter(Notifications.id == notification_id,
                                                         Notifications.is_active == True,
                                                         Notifications.is_deleted == is_deleted).first()
        if not db_notification:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("notification_not_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_notification)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_NOTIFICATIONS.endpoint_name,
    path=AccessEndpoint.GET_NOTIFICATIONS.endpoint_path,
    response_model=DataResponse[Page[NotificationsReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_notification(
        company: int,
        level: int,
        is_deleted: bool = False,
        search: Optional[str] = Query(None, description="Search by package name"),
        filters: NotificationFilter = Depends(),
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        fixed_time_end = time(23, 59, 59, 665295)
        statement = db.query(Notifications).filter(
            Notifications.company_id == company,
            Notifications.level == level,
            Notifications.is_deleted == is_deleted
        )
        if filters.type:
            statement = statement.filter(
                Notifications.type == filters.type
            )
        if filters.is_active == "true" and filters.expired == False:
            statement = statement.filter(
                Notifications.is_active == True,
                Notifications.end_date > datetime.combine(date.today(), fixed_time_end)
            )
        elif filters.is_active == "false" and filters.expired == False:
            statement = statement.filter(
                Notifications.is_active == False
            )
        elif filters.is_active == "true" and filters.expired == True:
            statement = statement.filter(
                Notifications.is_active == True,
                Notifications.end_date < datetime.combine(date.today(), fixed_time_end),
            )

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                Notifications.title.ilike(f"%{search}%", escape='\\')
            )
        companies = CRUDBase(Notifications).list(db=db, query=statement, params=page)

        now = datetime.now()
        for notification in companies.items:
            notification.is_expired = True if notification.end_date and notification.end_date < now else False

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=companies)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_NOTIFICATIONS_EXPIRED.endpoint_name,
    path=AccessEndpoint.GET_NOTIFICATIONS_EXPIRED.endpoint_path,
    response_model=DataResponse[Page[NotificationsReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_notification_expired(
        company: int,
        level: int,
        is_deleted: bool = False,
        search: Optional[str] = Query(None, description="Search by notification name"),
        filters: NotificationFilter = Depends(),
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:

        statement = db.query(Notifications).filter(Notifications.company_id == company,
                                                   Notifications.level == level,
                                                   Notifications.end_date > datetime.now(),
                                                   Notifications.start_date <= datetime.now(),
                                                   Notifications.is_active == True,
                                                   Notifications.is_deleted == is_deleted)
        if filters.type:
            statement = statement.filter(
                Notifications.type == filters.type
            )
        else:
            statement = statement
        if search:
            statement = statement.filter(
                (Notifications.title.ilike(f"%{search}%"))
            )
        companies = CRUDBase(Notifications).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=companies)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_NOTIFICATION.endpoint_name,
    path=AccessEndpoint.UPDATE_NOTIFICATION.endpoint_path,
    response_model=DataResponse[NotificationsReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_notification(
        notification_id: int,
        update: NotificationsUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_notification = db.query(Notifications).filter(Notifications.id == notification_id,
                                                         Notifications.is_deleted == False).first()
        if not db_notification:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("notification_not_found", language))

        if update.start_date > update.end_date:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("start_date_must_be_less_than_end_date", language))

        if update.end_date < date.today():
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("end_date_must_be_greater_than_current_date", language))

        if update.start_date < date.today():
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("start_date_must_be_greater_than_current_date", language))

        fixed_time_start = time(0, 0, 1, 665295)
        fixed_time_end = time(23, 59, 59, 665295)
        db_notification.title = update.title
        db_notification.content = update.content
        db_notification.start_date = datetime.combine(update.start_date, fixed_time_start)
        db_notification.end_date = datetime.combine(update.end_date, fixed_time_end)
        db_notification.type = update.type
        db_notification.is_active = update.is_active

        db.commit()
        db.refresh(db_notification)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_notification)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_NOTIFICATION.endpoint_name,
    path=AccessEndpoint.DELETE_NOTIFICATION.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_notification(
        notification_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_notification = db.query(Notifications).filter(Notifications.id == notification_id).first()
        if not db_notification:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("notification_not_found", language))

        db_notification.is_deleted = True
        db_notification.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_notification)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.CHANGE_STATUS_NOTIFICATION.endpoint_name,
    path=AccessEndpoint.CHANGE_STATUS_NOTIFICATION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def change_status_notification(
        notification_id: int,
        status: bool,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        noti = db.query(Notifications).filter(Notifications.id == notification_id,
                                              Notifications.is_deleted == False).first()

        if noti is None:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("notification_not_found", language))

        noti.is_active = status
        db.commit()
        db.refresh(noti)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
