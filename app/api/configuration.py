import traceback
import httpx
from typing import Optional
from google.cloud import storage
from fastapi import APIRouter, Depends, status as http_status
from fastapi import File, UploadFile
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from app.helpers.config import settings
from app.helpers.exceptions import GatewayException
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.configuration import ConfigResp, ConfigCreateUpdate, UserChatTitleUpdate, ConfigUserChatResp, \
    ModelKeyUpdate, ModelKeyResponse
from app.models.config import Configuration, ConfigurationUserChatTitle, ConfigurationModelKey
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.helpers.security import validate_super_admin, AuthPermissionChecker, validate_admin
from app.helpers.paging import Page, PaginationParams
from fastapi import Query, Request
from app.services.base import CRUDBase
from datetime import datetime, time, date
import logging
import json

router = APIRouter()
logger = logging.getLogger("mrag.app")
logger.setLevel(logging.INFO)


@router.post(
    name=AccessEndpoint.CREATE_CONFIGURATION.endpoint_name,
    path=AccessEndpoint.CREATE_CONFIGURATION.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def create_configuration(
        create: ConfigCreateUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config = db.query(Configuration).filter(
            Configuration.config_key == create.config_key,
            Configuration.is_deleted == False
        ).first()
        if config:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("record_exist", language)
            )
        db_config = Configuration(**create.dict())
        db.add(db_config)
        db.commit()
        db.refresh(db_config)

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data=db_config
        )

    except GatewayException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_CONFIGURATION.endpoint_name,
    path=AccessEndpoint.GET_CONFIGURATION.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def get_configuration(
        configuration_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config = db.query(Configuration).filter(
            Configuration.id == configuration_id,
            Configuration.is_deleted == False
        ).first()

        if not config:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("config_not_found", language)
            )

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=config)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_CONFIGURATIONS.endpoint_name,
    path=AccessEndpoint.GET_CONFIGURATIONS.endpoint_path,
    response_model=DataResponse[Page[ConfigResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def get_all_configuration(
        db: Session = Depends(get_db),
        is_active: Optional[bool] = True,
        is_deleted: Optional[bool] = False,
        search: Optional[str] = Query(None, description="Search by config key"),
        page: PaginationParams = Depends(),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Configuration).filter(
            Configuration.is_active == is_active,
            Configuration.is_deleted == is_deleted
        )
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                Configuration.config_key.ilike(f"%{search}%", escape='\\'),
            )
        configs = CRUDBase(Configuration).list(db=db, query=statement, params=page)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=configs)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_CONFIGURATION.endpoint_name,
    path=AccessEndpoint.UPDATE_CONFIGURATION.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def update_configuration(
        configuration_id: int,
        update: ConfigCreateUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config = db.query(Configuration).filter(
            Configuration.id == configuration_id,
            Configuration.is_deleted == False
        ).first()

        if not config:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("not_found", language)
            )
        if config.important == True and update.important == False:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("not_allow_downgrade_important", language)
            )
        if config.important is True and update.config_key is not None and update.config_key != config.config_key:
            raise GatewayException(
                status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=get_message("not_allow_change_key_for_important", language)
            )
        for key, value in update.dict(exclude_unset=True).items():
            setattr(config, key, value)

        db.commit()
        db.refresh(config)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=config)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.delete(
    name=AccessEndpoint.DELETE_CONFIGURATION.endpoint_name,
    path=AccessEndpoint.DELETE_CONFIGURATION.endpoint_path,
    response_model=DataResponse[str],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_super_admin))]
)
async def delete_configuration(
        configuration_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config = db.query(Configuration).filter(
            Configuration.id == configuration_id,
            Configuration.important == False,
            Configuration.is_deleted == False
        ).first()

        if not config:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("config_not_found", language)
            )

        config.is_deleted = True
        config.is_active = False
        config.deleted_at = datetime.utcnow()

        db.commit()
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.post(
    name=AccessEndpoint.UPDATE_LOGO.endpoint_name,
    path=AccessEndpoint.UPDATE_LOGO.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    description="CONFIG_LOGO_CLIENT: for client logo <br>CONFIG_LOGO_ADMIN: for admin logo <br> CONFIG_LOGO_BOT: for bot logo <br> CONFIG_FAVICON: for favicon <br> CONFIG_ICON_USER: for user interface icon <br> CONFIG_ICON_CHAT: for chat interface icon",
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_logo(
        logo_type: str,
        db: Session = Depends(get_db),
        file: Optional[UploadFile] = File(None),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        # Check if logo type is provided
        if not logo_type:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("logo_type_required", language))

        valid_logo_types = [
            "CONFIG_LOGO_ADMIN",
            "CONFIG_LOGO_CLIENT",
            "CONFIG_LOGO_BOT",
            "CONFIG_FAVICON",
            "CONFIG_ICON_USER",
            "CONFIG_ICON_CHAT"
        ]
        if logo_type not in valid_logo_types:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("logo_type_invalid", language))

        if not file:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("logo_file_required", language))
        # Check if logo file is provided
        config = db.query(Configuration).filter_by(config_key=logo_type).first()
        # Check if logo config exists
        if not config:
            config = Configuration(config_key=logo_type, config_value="")
            db.add(config)
            db.commit()
            db.refresh(config)
        if file:
            storage_client = storage.Client.from_service_account_json(settings.GOOGLE_APPLICATION_CREDENTIALS)
            bucket_name = settings.BUCKET_NAME
            # storage_client = storage.Client.from_service_account_json("app/helpers/credentials-prod.json")
            # bucket_name = "musashino-storage-saved"
            # Set destination path based on logo type
            if logo_type == "CONFIG_FAVICON":
                destination_blob_name = f"uploads/favicon/{file.filename.strip()}"
            else:
                destination_blob_name = f"uploads/logo/{file.filename.strip()}"
            filename = file.filename
            file_extension = filename.split('.')[-1].lower()

            # Validate file extension based on logo type
            if logo_type == "CONFIG_FAVICON":
                if file_extension not in ["ico", "png", "jpg", "jpeg"]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=get_message("favicon_invalid_format", language))
            else:
                if file_extension not in ["jpg", "jpeg", "png", "svg", "webp"]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=get_message("image_invalid_format", language))

            # Set content type
            if file_extension in ["jpg", "jpeg"]:
                content_type = "image/jpeg"
            elif file_extension == "png":
                content_type = "image/png"
            elif file_extension == "ico":
                content_type = "image/x-icon"
            elif file_extension == "svg":
                content_type = "image/svg+xml"
            elif file_extension == "webp":
                content_type = "image/webp"
            else:
                content_type = "application/octet-stream"
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_file(file.file, content_type=content_type)
            blob.content_type = file.content_type
            blob.content_disposition = "inline"
            logo_url = blob.public_url
            config.config_value = logo_url
            db.commit()
            db.refresh(config)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=config)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_LOGO.endpoint_name,
    path=AccessEndpoint.GET_LOGO.endpoint_path,
    response_model=DataResponse[ConfigResp],
    include_in_schema=True,
    description="CONFIG_LOGO_CLIENT: for client logo <br>CONFIG_LOGO_ADMIN: for admin logo <br> CONFIG_LOGO_BOT: for bot logo <br> CONFIG_FAVICON: for favicon <br> CONFIG_ICON_USER: for user interface icon <br> CONFIG_ICON_CHAT: for chat interface icon"
)
async def get_logo(
        logo_type: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if not logo_type:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("logo_type_required", language))

        valid_logo_types = [
            "CONFIG_LOGO_ADMIN",
            "CONFIG_LOGO_CLIENT",
            "CONFIG_LOGO_BOT",
            "CONFIG_FAVICON",
            "CONFIG_ICON_USER",
            "CONFIG_ICON_CHAT"
        ]
        if logo_type not in valid_logo_types:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("logo_type_invalid", language))

        config = db.query(Configuration).filter_by(config_key=logo_type).first()
        if not config:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("logo_not_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=config)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.UPDATE_CONFIGURATION_USER_CHAT_TITLE.endpoint_name,
    path=AccessEndpoint.UPDATE_CONFIGURATION_USER_CHAT_TITLE.endpoint_path,
    response_model=DataResponse[ConfigUserChatResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_configuration_user_chat_title(
        request: Request,
        company: int,
        create: UserChatTitleUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = request.state.user.to_dict()
        user_id = auth_info.get('client_id')

        user_chat_title = db.query(ConfigurationUserChatTitle).filter(
            ConfigurationUserChatTitle.company_id == company
        ).first()

        if not user_chat_title:
            user_chat_title = ConfigurationUserChatTitle(
                userchat_title_jp=create.userchat_title_jp,
                userchat_title_en=create.userchat_title_en,
                company_id=company,
                created_by=user_id
            )
            db.add(user_chat_title)
            db.commit()
            db.refresh(user_chat_title)
        else:
            user_chat_title.userchat_title_jp = create.userchat_title_jp
            user_chat_title.userchat_title_en = create.userchat_title_en
            user_chat_title.updated_by = user_id
            user_chat_title.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(user_chat_title)

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data=user_chat_title.to_dict()
        )
    except GatewayException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_CONFIGURATION_USER_CHAT_TITLE.endpoint_name,
    path=AccessEndpoint.GET_CONFIGURATION_USER_CHAT_TITLE.endpoint_path,
    response_model=DataResponse[ConfigUserChatResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_configuration_user_chat_title(
        company: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config_user_chat = db.query(ConfigurationUserChatTitle).filter(
            ConfigurationUserChatTitle.company_id == company,
            ConfigurationUserChatTitle.is_active == True, ConfigurationUserChatTitle.is_deleted == False
        ).first()

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=config_user_chat)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.post(
    name=AccessEndpoint.UPDATE_CONFIGURATION_MODEL_KEY.endpoint_name,
    path=AccessEndpoint.UPDATE_CONFIGURATION_MODEL_KEY.endpoint_path,
    response_model=DataResponse[ModelKeyResponse],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_configuration_model_key(
        request: Request,
        company: int,
        update: ModelKeyUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = request.state.user.to_dict()
        user_id = auth_info.get('client_id')
        # TODO: Validate service_key format
        if not ("*" in update.service_key and not update.service_key.startswith("sk-")):
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {update.service_key}"}
                resp = await client.get(settings.OPENAI_MODEL_URL, headers=headers)
                if resp.status_code != 200:
                    logger.warning(json.dumps({
                        "event": "invalid_open_ai_key",
                        "status_code": resp.status_code,
                        "response": resp.text
                    }))
                    raise GatewayException(
                        status_code=http_status.HTTP_400_BAD_REQUEST,
                        detail=get_message("invalid_open_ai_key", language)
                    )
        model_key = db.query(ConfigurationModelKey).filter(
            ConfigurationModelKey.company_id == company
        ).first()
        logger.info(json.dumps({
            "event": "update_model_key_request",
            "user_id": user_id,
            "company_id": company,
            "request_body": update.dict()
        }))
        if not model_key:
            config_model_key = ConfigurationModelKey(
                service_name=update.service_name,
                service_key=update.service_key,
                company_id=company,
                created_by=user_id
            )
            db.add(config_model_key)
            db.commit()
            db.refresh(config_model_key)
            logger.info(json.dumps({
                "event": "update_model_key_response",
                "created_by": user_id,
                "company_id": company
            }))
        else:
            model_key.service_name = update.service_name
            if not ("*" in update.service_key and not update.service_key.startswith("sk-")):
                model_key.service_key = update.service_key
            model_key.updated_by = user_id
            model_key.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(model_key)
            logger.info(json.dumps({
                "event": "update_model_key_response",
                "updated_by": user_id,
                "updated_at": datetime.utcnow().isoformat(),
                "company_id": company
            }))
        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data=model_key.to_dict()
        )
    except GatewayException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_CONFIGURATION_MODEL_KEY.endpoint_name,
    path=AccessEndpoint.GET_CONFIGURATION_MODEL_KEY.endpoint_path,
    response_model=DataResponse[ModelKeyResponse],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_configuration_model_key(
        company: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        config_model_key = db.query(ConfigurationModelKey).filter(
            ConfigurationModelKey.company_id == company,
            ConfigurationModelKey.is_active == True, ConfigurationModelKey.is_deleted == False
        ).first()

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=config_model_key)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language))


@router.get(
    "/favicon.ico",
    include_in_schema=False,
    description="Serve favicon from configuration"
)
async def get_favicon(
        db: Session = Depends(get_db)
):
    """
    Serve favicon.ico from configuration.
    If no favicon is configured, returns a default response.
    """
    try:
        config = db.query(Configuration).filter_by(config_key="CONFIG_FAVICON").first()
        if config and config.config_value:
            return RedirectResponse(url=config.config_value)
        else:
            # Return default favicon or 404
            return RedirectResponse(url="/static/default-favicon.ico", status_code=302)
    except Exception:
        # Return default favicon on error
        return RedirectResponse(url="/static/default-favicon.ico", status_code=302)
