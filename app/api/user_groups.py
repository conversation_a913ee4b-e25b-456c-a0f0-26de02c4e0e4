import traceback
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from fastapi import Query
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, Auth<PERSON>er<PERSON><PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.user_groups import UserGroupReps, UserGroupCreate, UserGroupUpdate, UserGroupFilter
from app.models.users import UserGroup, Users, Group
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_USER_GROUP.endpoint_name,
    path=AccessEndpoint.CREATE_USER_GROUP.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserGroupReps],
    dependencies=[Depends(AuthPer<PERSON><PERSON><PERSON><PERSON>(validate_admin))]
)
async def create_user_group(
        u_group: UserGroupCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        # TODO: check if user exist
        is_user_exists = db.query(Users).filter(Users.id == u_group.user_id, Users.is_active == True).first()
        if is_user_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_not_found", language))
        # TODO: Check if group exist
        is_group_exists = db.query(Group).filter(Group.id == u_group.group_id).first()
        if is_group_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_not_found", language))
        # TODO: check if user_exist in group or not
        is_u_group_exists = db.query(UserGroup).filter(UserGroup.user_id == u_group.user_id,
                                                       UserGroup.group_id == u_group.group_id).first()
        if is_u_group_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_exist_group", language))

        db_u_group = UserGroup(**u_group.dict())
        db.add(db_u_group)
        db.commit()
        db.refresh(db_u_group)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_u_group)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_GROUP.endpoint_name,
    path=AccessEndpoint.GET_USER_GROUP.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserGroupReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_user_group(
        u_group_id: int,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_u_group = db.query(UserGroup).filter(UserGroup.id == u_group_id, UserGroup.is_deleted == is_deleted).first()
        if not db_u_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_u_group)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_GROUPS.endpoint_name,
    path=AccessEndpoint.GET_USER_GROUPS.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[Page[UserGroupReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_user_group(
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: UserGroupFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by id"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:

        statement = db.query(UserGroup).filter(
            UserGroup.is_active == filters.is_active,
            UserGroup.is_deleted == filters.is_deleted
        )

        if search:
            statement = statement.filter(
                (UserGroup.id.ilike(f"%{search}%"))
            )

        user_group = CRUDBase(UserGroup).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_group)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_USER_GROUP.endpoint_name,
    path=AccessEndpoint.UPDATE_USER_GROUP.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserGroupReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_user_group(
        u_group_id: int,
        u_group: UserGroupUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_u_group = db.query(UserGroup).filter(UserGroup.id == u_group_id).first()
        if not db_u_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        is_user_exists = db.query(Users).filter(Users.id == u_group.user_id, Users.is_active == True).first()
        if is_user_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_not_found", language))
        # TODO: Check if group exist
        is_group_exists = db.query(Group).filter(Group.id == u_group.group_id).first()
        if is_group_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_not_found", language))
        # TODO: check if user_exist in group or not
        is_u_group_exists = db.query(UserGroup).filter(UserGroup.user_id == u_group.user_id,
                                                       UserGroup.group_id == u_group.group_id).first()
        if is_u_group_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_exist_group", language))

        db_u_group.user_id = u_group.user_id
        db_u_group.group_id = u_group.group_id
        db_u_group.is_active = u_group.is_active

        db.commit()
        db.refresh(db_u_group)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_u_group)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_USER_GROUP.endpoint_name,
    path=AccessEndpoint.DELETE_USER_GROUP.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_user_group(
        u_group_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_u_group = db.query(UserGroup).filter(UserGroup.id == u_group_id).first()
        if not db_u_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_u_group.is_deleted = True
        db.commit()
        db.refresh(db_u_group)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
