import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermissionChecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.permissions import PermissionCreate, PermissionReps, PermissionUpdate, PermissionFilter
from app.models.users import Permission
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_PERMISSION.endpoint_name,
    path=AccessEndpoint.CREATE_PERMISSION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[PermissionReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_permission(permission: PermissionCreate, db: Session = Depends(get_db),
                            language: Optional[str] = "jp") -> DataResponse:
    try:
        is_permission_exists = db.query(exists().where(Permission.component == permission.component)).scalar()
        if is_permission_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("permission_exist", language))

        db_permission = Permission(**permission.dict())
        db.add(db_permission)
        db.commit()
        db.refresh(db_permission)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_permission)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_PERMISSION.endpoint_name,
    path=AccessEndpoint.GET_PERMISSION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[PermissionReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_permissions(
        permission_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_permission = db.query(Permission).filter(Permission.id == permission_id,
                                                    Permission.is_deleted == is_deleted).first()
        if not db_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_permission)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_PERMISSIONS.endpoint_name,
    path=AccessEndpoint.GET_PERMISSIONS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[Page[PermissionReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_permissions(
        company: int,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        search: Optional[str] = Query(None, description="Search by Permission Name, Component"),
        filters: PermissionFilter = Depends(),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        active_filter = filters.is_active
        if isinstance(active_filter, str):
            if active_filter.lower() == "true":
                active_filter = True
            elif active_filter.lower() == "false":
                active_filter = False
            else:
                active_filter = None

        statement = db.query(Permission).filter(
            Permission.is_deleted == filters.is_deleted
        )

        if active_filter is not None:
            statement = statement.filter(Permission.is_active == active_filter)

        if company != 0:
            statement = statement.filter(Permission.component.notin_(["PACKAGE", "COMPANY"]))

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (Permission.name.ilike(f"%{search}%")) |
                (Permission.component.ilike(f"%{search}%"))
            )

        statement = statement.order_by(Permission.position.asc())

        permissions = CRUDBase(Permission).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=permissions)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_PERMISSION.endpoint_name,
    path=AccessEndpoint.UPDATE_PERMISSION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[PermissionReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_permissions(
        permission_id: str,
        permission: PermissionUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_permission = db.query(Permission).filter(Permission.id == permission_id,
                                                    Permission.is_deleted == False).first()
        if not db_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_permission.description = permission.description
        db_permission.endpoint = permission.endpoint
        db_permission.grant_type = permission.grant_type
        db_permission.component = permission.component

        db.commit()
        db.refresh(db_permission)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_permission)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_PERMISSION.endpoint_name,
    path=AccessEndpoint.DELETE_PERMISSION.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_permissions(permission_id: str, db: Session = Depends(get_db),
                             language: Optional[str] = "jp") -> DataResponse:
    try:
        db_permission = db.query(Permission).filter(Permission.id == permission_id).first()
        if not db_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_permission.is_deleted = True
        db_permission.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_permission)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
