import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.exceptions import GatewayException
from app.helpers.paging import Page, PaginationParams
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.role_permissions import RolePermissionCreate, RolePermissionReps, RolePermissionUpdate
from app.models.role_permissions import RolePermission
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_ROLE_PERMISSION.endpoint_name,
    path=AccessEndpoint.CREATE_ROLE_PERMISSION.endpoint_path,
    response_model=DataResponse[RolePermissionReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermission<PERSON>hecker(validate_admin))]
)
async def create_role_permission(
        createRequest: RolePermissionCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_exist = db.query(exists().where(RolePermission.role_id == createRequest.role_id,
                                             RolePermission.permission_id == createRequest.permission_id)).scalar()
        if data_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        data_exist = RolePermission(**createRequest.dict())
        db.add(data_exist)
        db.commit()
        db.refresh(data_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ROLE_PERMISSION.endpoint_name,
    path=AccessEndpoint.GET_ROLE_PERMISSION.endpoint_path,
    response_model=DataResponse[RolePermissionReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_role_permission(
        role_per_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(RolePermission).filter(RolePermission.id == role_per_id,
                                                  RolePermission.is_deleted == is_deleted).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ROLE_PERMISSIONS.endpoint_name,
    path=AccessEndpoint.GET_ROLE_PERMISSIONS.endpoint_path,
    response_model=DataResponse[Page[RolePermissionReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_role_permission(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(RolePermission).filter(RolePermission.is_deleted == is_deleted)
        data = CRUDBase(RolePermission).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_ROLE_PERMISSION.endpoint_name,
    path=AccessEndpoint.UPDATE_ROLE_PERMISSION.endpoint_path,
    response_model=DataResponse[RolePermissionReps],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_role_permission(
        department_datastore_access_id: str,
        updateRequest: RolePermissionUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(RolePermission).filter(
            RolePermission.id == department_datastore_access_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        db_data.role_id = updateRequest.role_id
        db_data.permission_id = updateRequest.permission_id

        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_ROLE_PERMISSION.endpoint_name,
    path=AccessEndpoint.DELETE_ROLE_PERMISSION.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_role_permission(
        data_store_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(RolePermission).filter(RolePermission.id == data_store_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
