import traceback
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.group_use_case import CreateGroup<PERSON>seCase, UpdateGroupUseCase, GroupUseCaseReps
from app.models.ai_services import GroupUseCase, UseCases
from app.models.users import Group
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_GROUP_USECASE.endpoint_name,
    path=AccessEndpoint.CREATE_GROUP_USECASE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[GroupUseCaseReps],
    dependencies=[Depends(AuthPermission<PERSON>hecker(validate_admin))]
)
async def create_group_usecase(
        g_usecase: CreateGroupUseCase,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        is_group_exists = db.query(Group).filter(Group.id == g_usecase.group_id, Group.is_deleted == False).first()
        if is_group_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_not_found", language))
        is_use_case_exists = db.query(UseCases).filter(UseCases.id == g_usecase.use_case_id).first()
        if is_use_case_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("use_case_not_found", language))
        is_group_usecase_exists = db.query(GroupUseCase).filter(GroupUseCase.group_id == g_usecase.group_id,
                                                                GroupUseCase.use_case_id == g_usecase.use_case_id).first()

        if is_group_usecase_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("usecase_exist_group", language))
        db_g_usecase = GroupUseCase(**g_usecase.dict())
        db.add(db_g_usecase)
        db.commit()
        db.refresh(db_g_usecase)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_g_usecase)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUP_USECASE.endpoint_name,
    path=AccessEndpoint.GET_GROUP_USECASE.endpoint_path,
    response_model=DataResponse[GroupUseCaseReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_group_usecase(
        g_usecase_id: int,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_g_usecase = db.query(GroupUseCase).filter(GroupUseCase.id == g_usecase_id,
                                                     GroupUseCase.is_deleted == is_deleted).first()
        if not db_g_usecase:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_g_usecase)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUP_USECASE.endpoint_name,
    path=AccessEndpoint.GET_GROUP_USECASE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[Page[GroupUseCaseReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_group_usecase(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:

        statement = db.query(GroupUseCase).filter(GroupUseCase.is_deleted == is_deleted)
        group_usecase = CRUDBase(GroupUseCase).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=group_usecase)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_GROUP_USECASE.endpoint_name,
    path=AccessEndpoint.UPDATE_GROUP_USECASE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[GroupUseCaseReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_group_usecase(
        g_usecase_id: int,
        u_group: UpdateGroupUseCase,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_g_usecase = db.query(GroupUseCase).filter(GroupUseCase.id == g_usecase_id).first()
        if not db_g_usecase:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        is_group_exists = db.query(Group).filter(Group.id == u_group.group_id, Group.is_deleted == False).first()
        if is_group_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_not_found", language))
        is_use_case_exists = db.query(UseCases).filter(UseCases.id == u_group.use_case_id).first()
        if is_use_case_exists is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("use_case_not_found", language))

        db_g_usecase.is_active = u_group.is_active

        db.commit()
        db.refresh(db_g_usecase)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_g_usecase)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_GROUP_USECASE.endpoint_name,
    path=AccessEndpoint.DELETE_GROUP_USECASE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_group_usecase(
        g_usecase_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_g_usecase = db.query(GroupUseCase).filter(GroupUseCase.id == g_usecase_id).first()
        if not db_g_usecase:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_g_usecase.is_deleted = True
        db.commit()
        db.refresh(db_g_usecase)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
