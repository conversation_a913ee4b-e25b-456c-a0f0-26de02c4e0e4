import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Request, status as http_status
from fastapi import Query
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermissionChecker
from app.helpers.decorators import track_action
from app.helpers.endpoints import AccessEndpoint
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.service_packages import ServicePackagesCreate, ServicePackagesResp, ServicePackagesUpdate, \
    ServicePackageFilter
from app.models.service_packages import ServicePackage
from app.models.company_subscriptions import CompanySubscriptions
from app.models.companies import Companies
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.models.users import Users
import os
from app.helpers.config import settings
from app.models.datastores_information import DatastoresInformation
from app.models.agent_builders_information import AgentBuildersInformation
from app.models.notifications import Notifications
from sqlalchemy import update
from app.models.token import Token
from app.models.gcp_projects import GCPProject
from sqlalchemy import func

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_SERVICE_PACKAGE.endpoint_name,
    path=AccessEndpoint.CREATE_SERVICE_PACKAGE.endpoint_path,
    response_model=DataResponse[ServicePackagesResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@track_action(action_name=AccessEndpoint.CREATE_SERVICE_PACKAGE.name)
async def create_service_package(
        request: Request,
        servicePackage: ServicePackagesCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_service = ServicePackage(**servicePackage.dict())
        db.add(db_service)
        db.commit()
        db.refresh(db_service)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_service)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_SERVICE_PACKAGE.endpoint_name,
    path=AccessEndpoint.GET_SERVICE_PACKAGE.endpoint_path,
    response_model=DataResponse[ServicePackagesResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@track_action(action_name=AccessEndpoint.GET_SERVICE_PACKAGE.name)
async def get_service_package(
        request: Request,
        service_package_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_service = db.query(ServicePackage).filter(ServicePackage.id == service_package_id,
                                                     ServicePackage.is_deleted == is_deleted).first()
        if not db_service:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("package_not_found", language))

        db_company_subscription = db.query(CompanySubscriptions).filter(
            CompanySubscriptions.package_id == service_package_id).first()
        db_company = db.query(Companies).filter(Companies.id == db_company_subscription.company_id,
                                                Companies.is_active == True,
                                                Companies.is_deleted == False).first()
        if not db_company:
            company_active = False
        else:
            company_active = True

        db_service.company_active = company_active

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_service)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_SERVICE_PACKAGES.endpoint_name,
    path=AccessEndpoint.GET_SERVICE_PACKAGES.endpoint_path,
    response_model=DataResponse[Page[ServicePackagesResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@track_action(action_name=AccessEndpoint.GET_SERVICE_PACKAGES.name)
async def get_all_service(
        request: Request,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: ServicePackageFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by package name"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(ServicePackage).filter(
            ServicePackage.is_deleted == False,
            ServicePackage.package_name != "Musashino",
            ServicePackage.id != 1
        )
        if filters.is_active == "true":
            statement = statement.filter(
                ServicePackage.is_active == True
            )
        elif filters.is_active == "false":
            statement = statement.filter(
                ServicePackage.is_active == False
            )
        else:
            statement = statement
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                ServicePackage.package_name.ilike(f"%{search}%", escape='\\')
            )
        service = CRUDBase(ServicePackage).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=service)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.put(
    name=AccessEndpoint.UPDATE_SERVICE_PACKAGE.endpoint_name,
    path=AccessEndpoint.UPDATE_SERVICE_PACKAGE.endpoint_path,
    response_model=DataResponse[ServicePackagesResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@track_action(action_name=AccessEndpoint.UPDATE_SERVICE_PACKAGE.name)
async def update_service_packages(
        request: Request,
        service_package_id: str,
        services: ServicePackagesUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_service = db.query(ServicePackage).filter(ServicePackage.id == service_package_id).first()
        if not db_service:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("package_not_found", language))
        package_exist = db.query(ServicePackage).filter(ServicePackage.package_name == services.package_name,
                                                        ServicePackage.id != service_package_id,
                                                        ServicePackage.is_active == True).first()
        if package_exist:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("package_exist", language))
        old_max_users = db_service.max_users
        company_sub = db.query(CompanySubscriptions).filter(
            CompanySubscriptions.package_id == service_package_id).first()
        count_user = db.query(Users).join(Companies, Users.company_id == Companies.id).join(CompanySubscriptions,
                                                                                            Companies.id == CompanySubscriptions.company_id).join(
            ServicePackage, ServicePackage.id == CompanySubscriptions.package_id).filter(
            Users.company_id == company_sub.company_id,
            Users.is_active == True,
            Users.is_deleted == False,
            CompanySubscriptions.is_active == True,
            CompanySubscriptions.status == 1
        ).count()

        if count_user > (services.max_users + 1):
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_user_exceed", language))


        # TODO : GET max_data_store and max_agent_builder from gcp project
        max_data_store = db.query(func.sum(GCPProject.datastore_quota)).filter(GCPProject.is_active == True,
                                                                               GCPProject.is_deleted == False).scalar()

        max_agent_builder = db.query(func.sum(GCPProject.agent_builder_quota)).filter(GCPProject.is_active == True,
                                                                                      GCPProject.is_deleted == False).scalar()

        # max_data_store = int(os.getenv('MAX_DATASTORES', 1000))
        # max_agent_builder = int(os.getenv('MAX_AGENT_BUILDERS', 1000))
        total_data_store = db.query(DatastoresInformation).filter(DatastoresInformation.is_active == True,
                                                                  DatastoresInformation.is_deleted == False).count()
        total_agent_builder = db.query(AgentBuildersInformation).filter(AgentBuildersInformation.is_active == True,
                                                                        AgentBuildersInformation.is_deleted == False).count()
        if language == "en":
            title = "Notification limit reached."
            ds_limit = (f"Data Store is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
            ab_limit = (f"Agent Builder is approaching its upper limit for the number of data stores it can create.\n"
                        f"Current number of creations：{total_data_store}\n"
                        f"Upper limit：{max_data_store}\n")
        else:
            title = "通知制限に達しました。"
            ds_limit = (f"Data Store のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")
            ab_limit = (f"Agent Builder のデータストア作成数の上限値に近くなっています。\n"
                        f"現在の作成数：{total_data_store}件\n"
                        f"作成数の上限値：{max_data_store}件\n")
        # Validate datastore and agent builder limit
        if ((total_data_store + services.max_datastores) / max_data_store) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ds_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        if ((total_agent_builder + services.max_agent_builders) / max_agent_builder) * 100 > 90:
            notification = Notifications(
                title=title,
                content=ab_limit,
                start_date=datetime.utcnow(),
                end_date=None,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                level=0,
                company_id=1,
                type=3
            )
            db.add(notification)
            db.commit()
        # if ((total_data_store + services.max_datastores) / max_data_store) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_datastore_exceed", language))
        # if ((total_agent_builder + services.max_agent_builders) / max_agent_builder) * 100 >= 100:
        #     raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        #                            detail=get_message("max_agent_builder_exceed", language))

        data_store_per_company = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company_sub.company_id,
            DatastoresInformation.is_active == True,
            DatastoresInformation.is_deleted == False).count()
        agent_builder_per_company = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company_sub.company_id,
            AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False).count()
        if services.max_datastores < data_store_per_company:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_datastore_less", language))
        if services.max_agent_builders < agent_builder_per_company:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("max_agent_builder_less", language))
        list_user_ids = [uid for (uid,) in db.query(Users.id)
        .filter(Users.company_id == company_sub.company_id)
        .all()]
        if not services.is_active:
            db.query(Users).filter(Users.company_id == company_sub.company_id).update(
                {"is_active": False},
                synchronize_session=False
            )
            for user in list_user_ids:
                stmt = update(Token).where(Token.user_id == user).values(
                    is_active=False, is_deleted=True, updated_at=datetime.now())
                db.execute(stmt)

        else:
            db.query(Users).filter(Users.company_id == company_sub.company_id).update(
                {"is_active": True},
                synchronize_session=False
            )
            for user in list_user_ids:
                stmt = update(Token).where(Token.user_id == user).values(
                    is_active=True, is_deleted=False, updated_at=datetime.now())
                db.execute(stmt)
        db_service.package_name = services.package_name
        db_service.description = services.description
        db_service.max_datastores = services.max_datastores
        db_service.max_agent_builders = services.max_agent_builders
        db_service.max_queries_per_day = services.max_queries_per_day
        db_service.max_users = services.max_users
        db_service.max_admins = services.max_admins
        db_service.max_managers = services.max_managers
        db_service.storage_limit = services.storage_limit
        db_service.credits_package_limit = services.credits_package_limit
        db_service.llm_model_type = services.llm_model_type
        db_service.max_tokens_per_month = services.max_tokens_per_month
        db_service.conversation_retention_days = services.conversation_retention_days
        db_service.encryption_level = services.encryption_level
        db_service.old_max_users = old_max_users
        db_service.note = services.note
        db_service.is_active = services.is_active
        db.commit()
        db.refresh(db_service)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_service)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_SERVICE_PACKAGE.endpoint_name,
    path=AccessEndpoint.DELETE_SERVICE_PACKAGE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@track_action(action_name=AccessEndpoint.DELETE_SERVICE_PACKAGE.name)
async def delete_service_packages(
        request: Request,
        service_package_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        service_package = db.query(ServicePackage).filter(
            ServicePackage.id == service_package_id,
            ServicePackage.is_deleted == False
        ).first()

        if not service_package:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("package_not_found", language)
            )

        package_subscription = db.query(CompanySubscriptions).filter(
            CompanySubscriptions.package_id == service_package_id,
            CompanySubscriptions.is_active == True
        ).first()

        if package_subscription:
            company = db.query(Companies).filter(
                Companies.id == package_subscription.company_id,
                Companies.is_deleted == False
            ).first()
            if company:
                raise GatewayException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail=get_message("has_company_registered", language)
                )

        list_user_ids = [uid for (uid,) in db.query(Users.id)
        .filter(Users.company_id == package_subscription.company_id)
        .all()]
        for user in list_user_ids:
            stmt = update(Token).where(Token.user_id == user).values(
                is_active=False, is_deleted=True, updated_at=datetime.now())
            db.execute(stmt)
        service_package.is_deleted = True
        service_package.deleted_at = datetime.now()
        db.commit()
        db.refresh(service_package)

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=None
        )

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )
