import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPer<PERSON><PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.groups import GroupReps, GroupCreate, GroupUpdate, GroupRepsV2
from app.models.users import Group, UserGroup, Users
from app.models.ai_services import GroupUseCase, UseCases
from app.models.companies import Companies
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from sqlalchemy import func

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_GROUP.endpoint_name,
    path=AccessEndpoint.CREATE_GROUP.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[GroupReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_group(
        groups: GroupCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        is_group_exists = db.query(
            exists().where(func.trim(Group.name) == func.trim(groups.name),
                           Group.company_id == groups.company_id, Group.is_deleted == False)).scalar()
        if is_group_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_exist", language))
        db_company = db.query(Companies).filter(Companies.id == groups.company_id).first()
        if not db_company:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        if groups.list_user_id:
            for list_user in groups.list_user_id:
                is_user_exists = db.query(Users).filter(Users.id == list_user,
                                                        Users.company_id == groups.company_id).first()
                if not is_user_exists:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("user_not_found", language))
        if groups.list_usecase_id:
            for list_usecase in groups.list_usecase_id:
                is_use_case_exists = db.query(UseCases).filter(UseCases.id == list_usecase).first()
                if not is_use_case_exists:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("use_case_not_found", language))

        # TODO: Create group
        db_group = Group(**groups.dict(exclude={"list_user_id", "list_usecase_id"}))
        db.add(db_group)
        db.commit()
        db.refresh(db_group)

        # TODO: Add list_user_id and list_usecase_id
        if groups.list_user_id:
            for list_user in groups.list_user_id:
                db_user_group = UserGroup(user_id=list_user, group_id=db_group.id)
                db.add(db_user_group)
                db.commit()
                db.refresh(db_user_group)
        if groups.list_usecase_id:
            for list_usecase in groups.list_usecase_id:
                db_group_usecase = GroupUseCase(use_case_id=list_usecase, group_id=db_group.id)
                db.add(db_group_usecase)
                db.commit()
                db.refresh(db_group_usecase)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_group)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUP.endpoint_name,
    path=AccessEndpoint.GET_GROUP.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[GroupRepsV2],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_group(
        company: int,
        group_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_group = db.query(Group).filter(Group.id == group_id, Group.is_deleted == is_deleted,
                                          Group.company_id == company).first()
        if not db_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        user_groups = db.query(UserGroup).filter(
            UserGroup.group_id == db_group.id,
            UserGroup.is_deleted == False,
            UserGroup.is_active == True
        ).all()
        user = [u_g.user_id for u_g in user_groups]
        list_user_information = []
        for u in user:
            users = db.query(Users).filter(Users.id == u).first()
            list_user_information.append({
                "id": users.id,
                "full_name": users.full_name,
                "avatar_url": users.avatar_url
            })
        db_group.list_user_id = list_user_information

        group_use_cases = db.query(GroupUseCase).filter(
            GroupUseCase.group_id == db_group.id,
            GroupUseCase.is_deleted == False,
            GroupUseCase.is_active == True
        ).all()
        usecase = [uc_g.use_case_id for uc_g in group_use_cases]

        list_usecase_information = []
        for u in usecase:
            db_usecase = db.query(UseCases).filter(UseCases.id == u).first()
            list_usecase_information.append({
                "id": db_usecase.id,
                "use_case_title": db_usecase.use_case_title
            })
        db_group.list_usecase_id = list_usecase_information

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_group)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUPS.endpoint_name,
    path=AccessEndpoint.GET_GROUPS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[Page[GroupRepsV2]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_groups(
        company: int,
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        search: Optional[str] = Query(None, description="Search by Group Name"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Group).filter(Group.is_deleted == is_deleted, Group.company_id == company)
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (Group.name.ilike(f"%{search}%"))
            )
        groups = CRUDBase(Group).list(db=db, query=statement, params=page)
        for group in groups.items:
            # TODO: Get list user_id
            users = db.query(Users.id, Users.full_name, Users.avatar_url).join(UserGroup,
                                                                               Users.id == UserGroup.user_id).filter(
                UserGroup.group_id == group.id,
                UserGroup.is_deleted == False,
                UserGroup.is_active == True,
                Users.is_musashino == False
            ).limit(5).all()
            group.list_user_id = [{"id": user.id, "full_name": user.full_name, "avatar_url": user.avatar_url} for user
                                  in users]

            # TODO: Get list usecase_id
            use_cases = db.query(UseCases.id, UseCases.use_case_title).join(
                GroupUseCase, GroupUseCase.use_case_id == UseCases.id
            ).filter(
                GroupUseCase.group_id == group.id
            ).limit(5).all()
            group.list_usecase_id = [{"id": use_case.id, "use_case_title": use_case.use_case_title} for use_case
                                     in use_cases]

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=groups)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_GROUP.endpoint_name,
    path=AccessEndpoint.UPDATE_GROUP.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse[GroupReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_groups(
        group_id: str,
        group: GroupUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_group = db.query(Group).filter(Group.id == group_id, Group.company_id == group.company_id,group.is_deleted == False).first()
        if not db_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        if group.list_user_id:
            for list_user in group.list_user_id:
                is_user_exists = db.query(Users).filter(Users.id == list_user,
                                                        Users.company_id == group.company_id).first()
                if not is_user_exists:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("user_not_found", language))
        if group.list_usecase_id:
            for list_usecase in group.list_usecase_id:
                is_use_case_exists = db.query(UseCases).filter(UseCases.id == list_usecase).first()
                if not is_use_case_exists:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("use_case_not_found", language))
        groups_exist = db.query(Group).filter(func.trim(Group.name) == func.trim(group.name),
                                              Group.company_id == group.company_id, Group.is_deleted == False,
                                              Group.id != group_id).first()
        if groups_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("group_exist", language))
        db_group.name = group.name
        db_group.description = group.description
        db_group.group_config = group.group_config

        db.commit()
        db.refresh(db_group)

        if group.list_user_id is None:
            existing_users = db.query(UserGroup).filter(UserGroup.group_id == db_group.id).all()

            # Delete all use cases for the group
            for existing_user in existing_users:
                db.delete(existing_user)
                db.commit()
        else:
            # TODO: Fetch existing user for the group
            existing_users = db.query(UserGroup).filter(UserGroup.group_id == db_group.id).all()

            # TODO: Delete user that are not in the updated list
            for existing_user in existing_users:
                if existing_user.user_id not in group.list_user_id:
                    db.delete(existing_user)
                    db.commit()

            # TODO: Add new user from the updated list
            if group.list_user_id:
                for list_user in group.list_user_id:
                    is_user_exists = db.query(UserGroup).filter(
                        UserGroup.user_id == list_user,
                        UserGroup.group_id == db_group.id
                    ).first()
                    if not is_user_exists:
                        db_user_group = UserGroup(user_id=list_user, group_id=db_group.id)
                        db.add(db_user_group)
                        db.commit()
                        db.refresh(db_user_group)

        if group.list_usecase_id is None:
            # Fetch existing use cases for the group
            existing_use_cases = db.query(GroupUseCase).filter(GroupUseCase.group_id == db_group.id).all()

            # Delete all use cases for the group
            for existing_use_case in existing_use_cases:
                db.delete(existing_use_case)
                db.commit()
        else:
            # TODO: Fetch existing use cases for the group
            existing_use_cases = db.query(GroupUseCase).filter(GroupUseCase.group_id == db_group.id).all()

            # TODO: Delete use cases that are not in the updated list
            for existing_use_case in existing_use_cases:
                if existing_use_case.use_case_id not in group.list_usecase_id:
                    db.delete(existing_use_case)
                    db.commit()

            # TODO: Add new use cases from the updated list
            if group.list_usecase_id:
                for list_usecase in group.list_usecase_id:
                    is_use_case_exists = db.query(GroupUseCase).filter(
                        GroupUseCase.use_case_id == list_usecase,
                        GroupUseCase.group_id == db_group.id
                    ).first()
                    if not is_use_case_exists:
                        db_group_usecase = GroupUseCase(use_case_id=list_usecase, group_id=db_group.id)
                        db.add(db_group_usecase)
                        db.commit()
                        db.refresh(db_group_usecase)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_group)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_GROUP.endpoint_name,
    path=AccessEndpoint.DELETE_GROUP.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_groups(
        company: int,
        group_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_group = db.query(Group).filter(Group.id == group_id, Group.company_id == company).first()
        if not db_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_group.is_deleted = True
        db_group.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_group)

        # Delete all users for the group
        user_groups = db.query(UserGroup).filter(UserGroup.group_id == db_group.id).all()
        for user_group in user_groups:
            db.delete(user_group)
            db.commit()

        # Delete all use cases for the group
        group_usecases = db.query(GroupUseCase).filter(GroupUseCase.group_id == db_group.id).all()
        for group_usecase in group_usecases:
            db.delete(group_usecase)
            db.commit()

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
