import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.exceptions import GatewayException
from app.helpers.paging import Page, PaginationParams
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.department_datastore_access import (DepartmentDataStoreAccessCreate,
                                                     DepartmentDataStoreAccessReps, DepartmentDataStoreAccessUpdate)
from app.models.department_datastore_access import DepartmentDataStoreAccess
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_DEPARTMENT_DATASTORE_ACCESS.endpoint_name,
    path=AccessEndpoint.CREATE_DEPARTMENT_DATASTORE_ACCESS.endpoint_path,
    response_model=DataResponse[DepartmentDataStoreAccessReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_department_datastore_access(
        access: DepartmentDataStoreAccessCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_exist = db.query(exists().where(DepartmentDataStoreAccess.department_id == access.department_id,
                                             DepartmentDataStoreAccess.datastore_id == access.datastore_id)).scalar()
        if data_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("record_exist", language))

        data_exist = DepartmentDataStoreAccess(**access.dict())
        db.add(data_exist)
        db.commit()
        db.refresh(data_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DEPARTMENT_DATASTORE_ACCESS.endpoint_name,
    path=AccessEndpoint.GET_DEPARTMENT_DATASTORE_ACCESS.endpoint_path,
    response_model=DataResponse[DepartmentDataStoreAccessReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_department_datastore_access(
        department_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(DepartmentDataStoreAccess).filter(DepartmentDataStoreAccess.id == department_id,
                                                             DepartmentDataStoreAccess.is_deleted == is_deleted).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DEPARTMENT_DATASTORE_ACCESS_LIST.endpoint_name,
    path=AccessEndpoint.GET_DEPARTMENT_DATASTORE_ACCESS_LIST.endpoint_path,
    response_model=DataResponse[Page[DepartmentDataStoreAccessReps]],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_department_datastore_access(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(DepartmentDataStoreAccess).filter(DepartmentDataStoreAccess.is_deleted == is_deleted)
        data = CRUDBase(DepartmentDataStoreAccess).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_DEPARTMENT_DATASTORE_ACCESS.endpoint_name,
    path=AccessEndpoint.UPDATE_DEPARTMENT_DATASTORE_ACCESS.endpoint_path,
    response_model=DataResponse[DepartmentDataStoreAccessReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_department_datastore_access(department_datastore_access_id: str,
                                             access: DepartmentDataStoreAccessUpdate,
                                             db: Session = Depends(get_db),
                                             language: Optional[str] = "jp") -> DataResponse:
    try:
        db_data = db.query(DepartmentDataStoreAccess).filter(
            DepartmentDataStoreAccess.id == department_datastore_access_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        db_data.access_level = access.access_level
        db_data.is_active = access.is_active

        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_DEPARTMENT_DATASTORE_ACCESS.endpoint_name,
    path=AccessEndpoint.DELETE_DEPARTMENT_DATASTORE_ACCESS.endpoint_path,
    response_model=DataResponse,
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_department_datastore_access(data_store_id: str, db: Session = Depends(get_db),
                                             language: Optional[str] = "jp") -> DataResponse:
    try:
        db_data = db.query(DepartmentDataStoreAccess).filter(DepartmentDataStoreAccess.id == data_store_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
