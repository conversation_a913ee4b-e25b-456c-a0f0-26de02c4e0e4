import traceback
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPer<PERSON><PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.group_permissions import GroupPermissionCreate, GroupPermissionReps, GroupPermissionUpdate
from app.models.users import GroupPermission
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_GROUP_PERMISSION.endpoint_name,
    path=AccessEndpoint.CREATE_GROUP_PERMISSION.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[GroupPermissionReps],
    dependencies=[Depends(AuthPermission<PERSON>hecker(validate_admin))]
)
async def create_permission(g_permission: GroupPermissionCreate, db: Session = Depends(get_db),
                            language: Optional[str] = "jp") -> DataResponse:
    try:
        is_g_permission_exists = db.query(exists().where(
            GroupPermission.group_id == g_permission.group_id,
            GroupPermission.permission_id == g_permission.permission_id
        )).scalar()
        if is_g_permission_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("permission_exist_group", language))

        db_g_permission = GroupPermission(**g_permission.dict())
        db.add(db_g_permission)
        db.commit()
        db.refresh(db_g_permission)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_g_permission)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUP_PERMISSION.endpoint_name,
    path=AccessEndpoint.GET_GROUP_PERMISSION.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[GroupPermissionReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_permissions(g_permission_id: str, is_deleted: bool = False,
                          db: Session = Depends(get_db),
                          language: Optional[str] = "jp") -> DataResponse:
    try:
        db_permission = db.query(GroupPermission).filter(GroupPermission.id == g_permission_id,
                                                         GroupPermission.is_deleted == is_deleted).first()
        if not db_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_permission)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GROUP_PERMISSIONS.endpoint_name,
    path=AccessEndpoint.GET_GROUP_PERMISSIONS.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[Page[GroupPermissionReps]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_permissions(is_deleted: bool = False, page: PaginationParams = Depends(),
                              db: Session = Depends(get_db), language: Optional[str] = "jp") -> DataResponse:
    try:
        statement = db.query(GroupPermission).filter(GroupPermission.is_deleted == is_deleted)
        group_permissions = CRUDBase(GroupPermission).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=group_permissions)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_GROUP_PERMISSION.endpoint_name,
    path=AccessEndpoint.UPDATE_GROUP_PERMISSION.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[GroupPermissionReps],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_permissions(g_permission_id: str, g_permission: GroupPermissionUpdate,
                             db: Session = Depends(get_db), language: Optional[str] = "jp") -> DataResponse:
    try:
        db_g_permission = db.query(GroupPermission).filter(GroupPermission.id == g_permission_id).first()
        if not db_g_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_g_permission.grant_type = g_permission.grant_type
        db_g_permission.valid_from = g_permission.valid_from
        db_g_permission.valid_to = g_permission.valid_to
        db_g_permission.is_active = g_permission.is_active

        db.commit()
        db.refresh(db_g_permission)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_g_permission)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_GROUP_PERMISSION.endpoint_name,
    path=AccessEndpoint.DELETE_GROUP_PERMISSION.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_permissions(g_permission_id: str, db: Session = Depends(get_db),
                             language: Optional[str] = "jp") -> DataResponse:
    try:
        db_g_permission = db.query(GroupPermission).filter(GroupPermission.id == g_permission_id).first()
        if not db_g_permission:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_g_permission.is_deleted = True
        db.commit()
        db.refresh(db_g_permission)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
