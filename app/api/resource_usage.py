import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.helpers.exceptions import GatewayException
from app.helpers.paging import Page, PaginationParams
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.resource_usage import ResourceUsageReps, ResourceUsageCreate, ResourceUsageUpdate
from app.models.resource_usage import ResourceUsage
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_RESOURCE_USAGE.endpoint_name,
    path=AccessEndpoint.CREATE_RESOURCE_USAGE.endpoint_path,
    response_model=DataResponse[ResourceUsageReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_resource_usage(
        createRequest: ResourceUsageCreate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_exist = ResourceUsage(**createRequest.dict())
        db.add(data_exist)
        db.commit()
        db.refresh(data_exist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data_exist)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_RESOURCE_USAGE.endpoint_name,
    path=AccessEndpoint.GET_RESOURCE_USAGE.endpoint_path,
    response_model=DataResponse[ResourceUsageReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_resource_usage(
        resource_usage_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(ResourceUsage).filter(ResourceUsage.id == resource_usage_id,
                                                 ResourceUsage.is_deleted == is_deleted).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_RESOURCE_USAGES.endpoint_name,
    path=AccessEndpoint.GET_RESOURCE_USAGES.endpoint_path,
    response_model=DataResponse[Page[ResourceUsageReps]],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_resource_usage(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(ResourceUsage).filter(ResourceUsage.is_deleted == is_deleted)
        data = CRUDBase(ResourceUsage).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_RESOURCE_USAGE.endpoint_name,
    path=AccessEndpoint.UPDATE_RESOURCE_USAGE.endpoint_path,
    response_model=DataResponse[ResourceUsageReps],
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_resource_usage(
        resource_usage_id: str,
        updateRequest: ResourceUsageUpdate,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(ResourceUsage).filter(
            ResourceUsage.id == resource_usage_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        db_data.resource_id = updateRequest.resource_id
        db_data.company_id = updateRequest.company_id
        db_data.resource_type = updateRequest.resource_type
        db_data.usage_amount = updateRequest.usage_amount
        db_data.usage_date = updateRequest.usage_date
        db_data.is_active = updateRequest.is_active

        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_RESOURCE_USAGE.endpoint_name,
    path=AccessEndpoint.DELETE_RESOURCE_USAGE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=False,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_resource_usage(
        resource_usage_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_data = db.query(ResourceUsage).filter(ResourceUsage.id == resource_usage_id).first()
        if not db_data:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_data.is_deleted = True
        db_data.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_data)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
