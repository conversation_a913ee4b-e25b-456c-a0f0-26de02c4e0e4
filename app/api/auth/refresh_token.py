import traceback
from typing import Optional
from fastapi import APIRouter, Depends, status, Request, Response
from sqlalchemy.orm import Session
from app.helpers.security import Authorizer, generate_legacy_token, validate_legacy_refresh_token, check_client_ip
from app.helpers.exceptions import GatewayException
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.utils.common import get_message

router = APIRouter()


@router.post(
    '/refresh',
    response_model_exclude_none=True,
    dependencies=[Depends(validate_legacy_refresh_token)]
)
async def refresh_token(request: Request, response: Response, db: Session = Depends(get_db),
                        language: Optional[str] = "jp") -> DataResponse:
    try:
        client_host = check_client_ip(request)
        auth = request.state.user.to_dict()
        uid = auth.get('uid')
        client_id = auth.get('client_id')
        email = auth.get('email')
        domain = request.headers.get('host', '')

        auth_info = Authorizer(uid=uid, client_id=client_id, email=email, client_host=str(client_host))
        access_token = generate_legacy_token(auth_info)
        if isinstance(access_token, bytes):
            access_token = access_token.decode('utf-8')

        response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True, samesite="none")

        return DataResponse.success(statusCode=status.HTTP_200_OK, data={"uid": uid})

    except GatewayException:
        raise

    except Exception:
        print(f"[POST][GATEWAY ERROR] REFRESH TOKEN: {traceback.format_exc()}")
        raise GatewayException(status_code=status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
