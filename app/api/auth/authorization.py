import traceback
from datetime import datetime, timedelta
from typing import Optional
import httpx
import uuid
from fastapi import APIRouter, BackgroundTasks, Depends, Request, Response, status as http_status
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from sqlalchemy import update, func, or_
from app.core.sendmail.send_async_mail import EmailAsyncClient, EmailAsyncTemplate
from app.helpers.config import settings
from app.helpers.security import (Authorizer, check_password_strength, check_client_ip, generate_legacy_token,
                                  validate_legacy_token, generate_legacy_refresh_token, hash_password, validate_user,
                                  AuthPermissionChecker)
from app.helpers.exceptions import GatewayException
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.login import OauthLogin, SSOLogin, ChangePassword, SetPassword
from app.schemas.users import PasswordResetReq, PasswordForgotReq
from app.models.users import Users, Role, UserRole
from app.models.token import PasswordResetToken
from app.services.send_mail_service import PasswordResetAsyncService
from app.utils.common import generate_id_code, get_message
from app.models.token import Token

router = APIRouter()


@router.post(
    "/admin/sso/login",
    response_model=DataResponse
)
async def admin_login_with_sso(
        request: Request,
        response: Response,
        login: SSOLogin,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        client_host = check_client_ip(request)
        domain = request.headers.get('host', '')
        url = "https://www.googleapis.com/oauth2/v3/userinfo"
        headers = {
            "Authorization": f"Bearer {login.access_token}"
        }
        auth = httpx.get(url, headers=headers)
        if auth.status_code == 200:
            user = auth.json()
            google_id = user['sub']
            avatar_url = user['picture']
            name = user['name']
            email = user['email']

            user = db.query(Users).filter(Users.email == email, Users.is_active == True,
                                          Users.is_deleted == False).first()
            if user:
                user_roles = (
                    db.query(UserRole)
                    .join(Role, UserRole.role_id == Role.id)
                    .filter(UserRole.user_id == user.id, or_(
                        func.lower(Role.name) == "admin",
                        func.lower(Role.name) == "superadmin"
                    ), UserRole.is_active == True,
                            UserRole.is_deleted == False)
                    .first()
                )
                if not user_roles:
                    raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                           detail=get_message("admin_privileges", language))

                user.last_login = datetime.now()
                user.google_id = google_id
                user.avatar_url = avatar_url
                user.full_name = name
                db.commit()
                db.refresh(user)

                auth_info = Authorizer(uid=user.uid, client_id=user.id, email=email, client_host=str(client_host))

                access_token = generate_legacy_token(auth_info)
                refresh_token = generate_legacy_refresh_token(auth_info)
                if isinstance(access_token, bytes):
                    access_token = access_token.decode('utf-8')
                if isinstance(refresh_token, bytes):
                    refresh_token = refresh_token.decode('utf-8')

                response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True,
                                    samesite="none")
                response.set_cookie(key="r", value=refresh_token, domain=domain, httponly=True, secure=True,
                                    samesite="none")

                return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                            data={"uid": user.uid, "company_id": user.company_id})

            else:
                raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED,
                                       detail=get_message("account_incorrect", language))
        else:
            raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED,
                                   detail=get_message("account_incorrect", language))

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/admin/oauth/login",
    response_model=DataResponse
)
async def admin_legacy_login(request: Request,
                             response: Response,
                             login: OauthLogin,
                             language: Optional[str] = "jp",
                             db: Session = Depends(get_db)) -> DataResponse:
    try:
        client_host = check_client_ip(request)
        domain = request.headers.get('host', '')
        user: Users = db.query(Users).filter(Users.email == login.email, Users.is_active == True,
                                             Users.is_deleted == False).first()
        if not user:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("email_password_incorrect", language))

        # user_roles = (
        #     db.query(UserRole)
        #     .join(Role, UserRole.role_id == Role.id)
        #     .filter(UserRole.user_id == user.id, or_(
        #         func.lower(Role.name) == "admin",
        #         func.lower(Role.name) == "superadmin"
        #     ), UserRole.is_deleted == False)
        #     .first()
        # )
        user_role_user = (
            db.query(UserRole)
            .join(Role, UserRole.role_id == Role.id)
            .filter(UserRole.user_id == user.id, Role.important == True, or_(
                func.lower(Role.name) == "user"
            ), UserRole.is_deleted == False)
            .first()
        )
        list_role = (
            db.query(UserRole)
            .join(Role, UserRole.role_id == Role.id)
            .filter(UserRole.user_id == user.id, Role.is_active == True, Role.is_deleted == False,
                    UserRole.is_deleted == False)
            .count()
        )

        if user_role_user and list_role == 1:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("admin_privileges", language))

        is_correct = user.verify_password(login.password)
        if not is_correct:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("email_password_incorrect", language))

        user.last_login = datetime.now()
        db.commit()
        db.refresh(user)

        auth_info = Authorizer(uid=user.uid, client_id=user.id, email=login.email, client_host=str(client_host))
        access_token = generate_legacy_token(auth_info)
        refresh_token = generate_legacy_refresh_token(auth_info)
        if isinstance(access_token, bytes):
            access_token = access_token.decode('utf-8')
        if isinstance(refresh_token, bytes):
            refresh_token = refresh_token.decode('utf-8')
        exist_token = db.query(Token).filter(Token.user_id == user.id, Token.user_uid == user.uid).first()
        if exist_token:
            stmt = update(Token).where(Token.user_id == user.id, Token.user_uid == user.uid).values(
                access_token=access_token, product_type="admin",
                refresh_token=refresh_token, updated_at=datetime.now())
            db.execute(stmt)
            db.commit()
        else:
            new_token = Token(user_id=user.id, user_uid=user.uid, access_token=access_token,
                              refresh_token=refresh_token,
                              domain=domain, product_type="admin", ip_address=client_host,
                              user_agent=request.headers.get('user-agent', ''), is_active=True, is_deleted=False)
            db.add(new_token)
            db.commit()
            db.refresh(new_token)
        response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True, samesite="none")
        response.set_cookie(key="r", value=refresh_token, domain=domain, httponly=True, secure=True, samesite="none")

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data={"uid": user.uid, "company_id": user.company_id})

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/sso/login",
    response_model=DataResponse
)
async def login_with_sso(
        request: Request,
        response: Response,
        login: SSOLogin,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)) -> DataResponse:
    try:
        client_host = check_client_ip(request)
        domain = request.headers.get('host', '')
        url = "https://www.googleapis.com/oauth2/v3/userinfo"
        headers = {"Authorization": f"Bearer {login.access_token}"}

        async with httpx.AsyncClient() as client:
            auth = await client.get(url, headers=headers)

        if auth.status_code == 200:
            user = auth.json()
            google_id = user['sub']
            avatar_url = user['picture']
            name = user['name']
            email = user['email']

            user = db.query(Users).filter(Users.email == email).first()
            if user:
                if user.is_active == False or user.is_deleted == True:
                    raise GatewayException(
                        status_code=http_status.HTTP_403_FORBIDDEN,
                        detail=get_message("account_deleted", language)
                    )

                user.last_login = datetime.now()
                user.google_id = google_id
                user.avatar_url = avatar_url
                user.full_name = name
                db.commit()
                db.refresh(user)
            else:
                new_user = Users(
                    full_name=name,
                    email=email,
                    google_id=google_id,
                    avatar_url=avatar_url,
                    provider="google",
                    uid=f"{uuid.uuid4().hex}".upper(),
                    auth_mode="OAUTH",
                    last_login=datetime.now(),
                    is_first_login=True,
                    is_deleted=False
                )
                db.add(new_user)
                db.commit()
                db.refresh(new_user)
                user = new_user

            user_role = (
                db.query(Role)
                .join(UserRole, UserRole.role_id == Role.id)
                .filter(
                    UserRole.user_id == user.id,
                    func.lower(Role.name) == "user",
                    Role.is_deleted == False,
                    UserRole.is_deleted == False
                )
                .first()
            )

            if not user_role:
                user_role = db.query(Role).filter(
                    func.lower(Role.name) == "user",
                    Role.is_deleted == False
                ).first()

                if not user_role:
                    user_role = Role(name="User", description="Default user role")
                    db.add(user_role)
                    db.commit()
                    db.refresh(user_role)

                new_user_role = UserRole(user_id=user.id, role_id=user_role.id)
                db.add(new_user_role)
                db.commit()
                db.refresh(new_user_role)

            auth_info = Authorizer(uid=user.uid, client_id=user.id, email=email, client_host=str(client_host))

            access_token = generate_legacy_token(auth_info)
            refresh_token = generate_legacy_refresh_token(auth_info)
            if isinstance(access_token, bytes):
                access_token = access_token.decode('utf-8')
            if isinstance(refresh_token, bytes):
                refresh_token = refresh_token.decode('utf-8')

            if user.is_first_login:
                data = {"uid": user.uid, "company_id": user.company_id, "is_first_login": user.is_first_login,
                        "token": access_token}
            else:
                response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True,
                                    samesite="none")
                response.set_cookie(key="r", value=refresh_token, domain=domain, httponly=True, secure=True,
                                    samesite="none")
                data = {"uid": user.uid, "company_id": user.company_id, "is_first_login": user.is_first_login}

            return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data)

        else:
            raise GatewayException(status_code=http_status.HTTP_401_UNAUTHORIZED,
                                   detail=get_message("account_incorrect", language))

    except GatewayException:
        raise
    except httpx.RequestError:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_502_BAD_GATEWAY,
                               detail=get_message("error_auth_server", language))
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/oauth/login",
    response_model=DataResponse
)
async def legacy_login(
        request: Request,
        response: Response,
        login: OauthLogin,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        client_host = check_client_ip(request)
        domain = request.headers.get('host', '')
        user: Users = db.query(Users).filter(Users.email == login.email, Users.is_active == True,
                                             Users.is_deleted == False).first()
        if not user:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("email_password_incorrect", language))

        is_correct = user.verify_password(login.password)

        if not is_correct:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("email_password_incorrect", language))

        user_role_user = (
            db.query(UserRole)
            .join(Role, UserRole.role_id == Role.id)
            .filter(UserRole.user_id == user.id, Role.important == True, or_(
                func.lower(Role.name) == "user"
            ), UserRole.is_deleted == False)
            .first()
        )
        if not user_role_user:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("user_privileges", language))

        user.last_login = datetime.now()
        db.commit()
        db.refresh(user)

        auth_info = Authorizer(uid=user.uid, client_id=user.id, email=login.email, client_host=str(client_host))
        access_token = generate_legacy_token(auth_info)
        refresh_token = generate_legacy_refresh_token(auth_info)
        if isinstance(access_token, bytes):
            access_token = access_token.decode('utf-8')
        if isinstance(refresh_token, bytes):
            refresh_token = refresh_token.decode('utf-8')

        exist_token = db.query(Token).filter(Token.user_id == user.id, Token.user_uid == user.uid).first()
        if exist_token:
            stmt = update(Token).where(Token.user_id == user.id, Token.user_uid == user.uid).values(
                access_token=access_token, product_type="user",
                refresh_token=refresh_token, updated_at=datetime.now())
            db.execute(stmt)
            db.commit()
        else:
            new_token = Token(user_id=user.id, user_uid=user.uid, access_token=access_token,
                              refresh_token=refresh_token,
                              domain=domain, product_type="user", ip_address=client_host,
                              user_agent=request.headers.get('user-agent', ''), is_active=True, is_deleted=False)
            db.add(new_token)
            db.commit()
            db.refresh(new_token)
        if user.is_first_login:
            data = {"uid": user.uid, "company_id": user.company_id, "is_first_login": user.is_first_login,
                    "token": access_token}
        else:
            response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True, samesite="none")
            response.set_cookie(key="r", value=refresh_token, domain=domain, httponly=True, secure=True,
                                samesite="none")
            data = {"uid": user.uid, "company_id": user.company_id, "is_first_login": user.is_first_login}

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/logout",
    response_model=DataResponse
)
async def api_logout(request: Request, response: Response,
                     language: Optional[str] = "jp") -> DataResponse:
    try:
        domain = request.headers.get('host', '')
        response.delete_cookie(key="t", domain=domain, httponly=True, secure=True, samesite="none")
        response.delete_cookie(key="r", domain=domain, httponly=True, secure=True, samesite="none")
        data = {"message": "Logged out successfully."}

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/change-password",
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def auth_change_password(
        request: Request,
        changePassword: ChangePassword,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        if not user:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("account_incorrect", language)])

        has_password = user.hashed_password or None
        if not has_password:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("password_not_created", language)])

        is_valid = changePassword.old_password and changePassword.password and changePassword.confirm_pw and changePassword.password == changePassword.confirm_pw
        if not is_valid:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("confirm_incorrect", language)])

        is_dup = changePassword.old_password and changePassword.password and changePassword.old_password == changePassword.password
        if is_dup:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("new_pass_not_old_pass", language)])

        is_correct = user.verify_password(changePassword.old_password)
        if not is_correct:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("old_password_incorrect", language)])

        is_strong, score, message = check_password_strength(changePassword.password, language)
        if not is_strong:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[message])

        stmt = (update(Users).where(Users.id == user.id).values(hashed_password=hash_password(changePassword.password)))
        db.execute(stmt)
        db.commit()

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("password_update_success", language))

    except GatewayException:
        raise

    except Exception:
        print(f"[POST][GATEWAY ERROR] AUTH CHANGE PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/forgot-password",
    response_model=DataResponse
)
async def forgot_password_user(
        forgot: PasswordForgotReq,
        background_tasks: BackgroundTasks,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)) -> DataResponse:
    try:
        user = db.query(Users).filter_by(email=forgot.email).scalar()
        if not user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("email_not_found", language))

        if user.is_active == False or user.is_deleted == True:
            raise GatewayException(status_code=http_status.HTTP_403_FORBIDDEN,
                                   detail=get_message("account_deleted", language))

        auth_token = generate_id_code(length=30)
        email_config = {
            'sender_email': settings.SENDER_EMAIL,
            'password': settings.SENDER_EMAIL_PASSWORD,
            'smtp_server': settings.SMTP_SERVER,
            'smtp_port': settings.SMTP_PORT
        }

        email_client = EmailAsyncClient(config=email_config)
        email_template = EmailAsyncTemplate(template_name='sendmail/reset_password_template.html')
        password_reset_service = PasswordResetAsyncService(email_client, email_template)

        expires_at = datetime.now() + timedelta(minutes=15)
        expires_at = expires_at.strftime('%Y-%m-%d %H:%M')
        reset_token = PasswordResetToken(
            user_id=user.id,
            token=auth_token,
            expires_at=expires_at
        )
        db.add(reset_token)
        db.commit()
        db.refresh(reset_token)

        reset_link = f'{settings.HOST_CLIENT_APP}/reset-password?reset_password_token={auth_token}&email={forgot.email}'
        context = {"name": user.full_name, "purpose": "reset_password", "reset_link": reset_link,
                   "expires_at": expires_at, "more_info": "", "show_social_links": False, "show_footer": False}
        receivers = [{'email': user.email, "context": context, "reset_token": reset_token}]

        background_tasks.add_task(password_reset_service.send_password_reset, receivers=receivers, db=db)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("email_send", language))

    except GatewayException:
        raise
    except Exception:
        print(f"[POST][GATEWAY ERROR] FORGOT PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/reset-password",
    response_model=DataResponse
)
async def reset_password_user(
        reset_pw: PasswordResetReq,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_token = db.query(PasswordResetToken).filter(
            PasswordResetToken.token == reset_pw.token,
            PasswordResetToken.is_deleted == False
        ).first()

        if not auth_token or auth_token.used:
            return DataResponse().errors(statusCode=http_status.HTTP_410_GONE,
                                         error=[get_message("invalid_token", language)])
        if auth_token.expires_at < datetime.now():
            return DataResponse().errors(statusCode=http_status.HTTP_411_LENGTH_REQUIRED,
                                         error=[get_message("expired_token", language)])

        user = db.query(Users).filter(
            Users.id == auth_token.user_id,
            Users.is_active == True,
            Users.is_deleted == False
        ).first()

        if not user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))

        if reset_pw.new_password != reset_pw.confirm_new_password:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("password_not_confirm_password", language))

        is_strong, _, message = check_password_strength(reset_pw.new_password, language)
        if not is_strong:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("password_not_strong", language))

        hashed_password = hash_password(reset_pw.new_password)
        stmt = update(Users).where(Users.id == user.id).values(hashed_password=hashed_password)
        db.execute(stmt)

        auth_token.used = True
        db.commit()
        user_email = user.email
        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=f"パスワードが正常に更新されました: {user_email}" if language == "jp" else f"Password updated successfully: {user_email}")

    except GatewayException:
        raise
    except Exception:
        print(f"[POST][GATEWAY ERROR] FORGOT PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    "/set-password",
    response_model=DataResponse
)
async def set_password_user_google(
        request: Request,
        response: Response,
        setPassword: SetPassword,
        language: Optional[str] = "jp",
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        client_host = check_client_ip(request)
        domain = request.headers.get('host', '')
        state_user = validate_legacy_token(request, setPassword.token, db)
        auth_info = jsonable_encoder(state_user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_first_login == True).first()
        if not user:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("account_incorrect", language)])

        is_valid = setPassword.password and setPassword.confirm_pw and setPassword.password == setPassword.confirm_pw
        if not is_valid:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("confirm_incorrect", language)])

        is_strong, score, message = check_password_strength(setPassword.password, language)
        if not is_strong:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[message])

        is_correct = user.verify_password(setPassword.password)
        if is_correct and setPassword.password == setPassword.confirm_pw:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("old_password_need_doesnt_match_new_password", language)])

        stmt = db.query(Users).filter(Users.id == user.id).first()
        stmt.hashed_password = hash_password(setPassword.password)
        stmt.is_first_login = False
        db.commit()
        db.refresh(stmt)

        new_auth = Authorizer(uid=user.uid, client_id=user.id, email=user.email, client_host=str(client_host))
        access_token = generate_legacy_token(new_auth)
        refresh_token = generate_legacy_refresh_token(new_auth)
        if isinstance(access_token, bytes):
            access_token = access_token.decode('utf-8')
        if isinstance(refresh_token, bytes):
            refresh_token = refresh_token.decode('utf-8')

        response.set_cookie(key="t", value=access_token, domain=domain, httponly=True, secure=True, samesite="none")
        response.set_cookie(key="r", value=refresh_token, domain=domain, httponly=True, secure=True,
                            samesite="none")

        return DataResponse.success(statusCode=http_status.HTTP_200_OK,
                                    data=get_message("password_update_success", language))

    except GatewayException:
        raise

    except Exception:
        print(f"[POST][GATEWAY ERROR] AUTH CHANGE PASSWORD: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
