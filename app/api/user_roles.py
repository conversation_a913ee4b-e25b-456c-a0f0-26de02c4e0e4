import traceback
from typing import List, Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, AuthPermission<PERSON>hecker
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.user_roles import UserRole<PERSON><PERSON>, UserRoleResp, UserRoleUpdate
from app.models.users import UserRole
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_USER_ROLE.endpoint_name,
    path=AccessEndpoint.CREATE_USER_ROLE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserRoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_permission(u_role: UserRoleCreate, db: Session = Depends(get_db),
                            language: Optional[str] = "jp") -> DataResponse:
    try:
        is_u_role_exists = db.query(exists().where(
            UserRole.user_id == u_role.user_id,
            UserRole.role_id == u_role.role_id
        )).scalar()
        if is_u_role_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("user_exist_role", language))

        db_u_role = UserRole(**u_role.dict())
        db.add(db_u_role)
        db.commit()
        db.refresh(db_u_role)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_u_role)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_ROLE.endpoint_name,
    path=AccessEndpoint.GET_USER_ROLE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserRoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_permissions(u_role_id: str, is_deleted: bool = False, db: Session = Depends(get_db),
                          language: Optional[str] = "jp") -> DataResponse:
    try:
        db_u_group = db.query(UserRole).filter(UserRole.id == u_role_id, UserRole.is_deleted == is_deleted).first()
        if not db_u_group:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_u_group)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USER_ROLES.endpoint_name,
    path=AccessEndpoint.GET_USER_ROLES.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[Page[UserRoleResp]],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_permissions(is_deleted: bool = False, page: PaginationParams = Depends(),
                              db: Session = Depends(get_db), language: Optional[str] = "jp") -> DataResponse:
    try:

        statement = db.query(UserRole).filter(UserRole.is_deleted == is_deleted)
        user_role = CRUDBase(UserRole).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=user_role)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_USER_ROLE.endpoint_name,
    path=AccessEndpoint.UPDATE_USER_ROLE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse[UserRoleResp],
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_permissions(u_role_id: str, u_role: UserRoleUpdate, db: Session = Depends(get_db),
                             language: Optional[str] = "jp") -> DataResponse:
    try:
        db_u_role = db.query(UserRole).filter(UserRole.id == u_role_id).first()
        if not db_u_role:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_u_role.is_active = u_role.is_active

        db.commit()
        db.refresh(db_u_role)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_u_role)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_USER_ROLE.endpoint_name,
    path=AccessEndpoint.DELETE_USER_ROLE.endpoint_path,
    include_in_schema=False,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_user_role(u_role_id: str, db: Session = Depends(get_db),
                           language: Optional[str] = "jp") -> DataResponse:
    try:
        db_u_role = db.query(UserRole).filter(UserRole.id == u_role_id).first()
        if not db_u_role:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_u_role.is_deleted = True
        db.commit()
        db.refresh(db_u_role)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
