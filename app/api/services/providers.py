import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, status as http_status
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.services import CreateProvider, ProviderResp, UpdateProvider, ProviderFilter, UpdateProviderUsecase
from app.models.ai_services import Providers, UseCases, ChatRoom
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
from app.core.cloud_engine.google import GoogleCloudPlatform
from fastapi import Query
from app.models.config import Configuration

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_PROVIDER.endpoint_name,
    path=AccessEndpoint.CREATE_PROVIDER.endpoint_path,
    response_model=DataResponse[ProviderResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_provider(
        provider: CreateProvider,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_provider = Providers(**provider.dict())
        db.add(db_provider)
        db.commit()
        db.refresh(db_provider)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_provider)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_PROVIDER.endpoint_name,
    path=AccessEndpoint.GET_PROVIDER.endpoint_path,
    response_model=DataResponse[ProviderResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_provider(
        provider_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_provider = db.query(Providers).filter(Providers.id == provider_id,
                                                 Providers.is_deleted == is_deleted).first()
        if not db_provider:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_provider)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_PROVIDERS.endpoint_name,
    path=AccessEndpoint.GET_PROVIDERS.endpoint_path,
    response_model=DataResponse[Page[ProviderResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_providers(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: ProviderFilter = Depends(),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(Providers).filter(Providers.is_deleted == is_deleted)
        if filters.is_active:
            if filters.is_active == "true" or filters.is_active == "True":
                statement = statement.filter(Providers.is_active == True)
            elif filters.is_active == "false" or filters.is_active == "False":
                statement = statement.filter(Providers.is_active == False)
            else:
                statement = statement
        providers = CRUDBase(Providers).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=providers)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_PROVIDER.endpoint_name,
    path=AccessEndpoint.UPDATE_PROVIDER.endpoint_path,
    response_model=DataResponse[ProviderResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_provider(
        provider_id: str,
        provider: UpdateProvider,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_provider = db.query(Providers).filter(Providers.id == provider_id, Providers.is_deleted == False).first()
        if not db_provider:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        # Check if the provider is already deleted
        db_provider.ai_service = provider.ai_service
        db_provider.service_name = provider.service_name
        db_provider.model_name = provider.model_name
        db_provider.llm_model_id = provider.llm_model_id
        db_provider.description = provider.description
        db_provider.is_public = provider.is_public
        db_provider.is_active = provider.is_active
        db.commit()
        db.refresh(db_provider)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_provider)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_PROVIDER.endpoint_name,
    path=AccessEndpoint.DELETE_PROVIDER.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_provider(
        provider_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_provider = db.query(Providers).filter(Providers.id == provider_id).first()
        if not db_provider:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        db_chatroom = db.query(ChatRoom).filter(ChatRoom.provider_id == provider_id, ChatRoom.is_deleted == False,
                                                ChatRoom.is_active == True).first()
        if db_chatroom:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("provider_is_used", language))

        db_provider.is_deleted = True
        db_provider.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_provider)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_PROVIDER_USECASE.endpoint_name,
    path=AccessEndpoint.UPDATE_PROVIDER_USECASE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_provider_usecase(
        update: UpdateProviderUsecase,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if not update.list_use_provider:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("provider_id_required", language))
        for provider in update.list_use_provider:
            db_provider = db.query(Providers).filter(Providers.id == provider, Providers.is_active == True,
                                                     Providers.is_deleted == False).first()
            if not db_provider:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("provider_not_valid", language))
        db_usecase = db.query(UseCases).filter(
            UseCases.is_deleted == False
        ).update({UseCases.list_provider_ids: update.list_use_provider}, synchronize_session=False)
        db.commit()

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_usecase)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_LIST_MODEL_AI.endpoint_name,
    path=AccessEndpoint.GET_LIST_MODEL_AI.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    # dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_list_model_ai(
        search: Optional[str] = Query(None, description="Search term for AI models"),
        ai_service: str = "google",
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        GCP_API_KEY = db.query(Configuration).filter(Configuration.config_key == 'GCP_API_KEY').first()
        OPENAI_KEY = db.query(Configuration).filter(Configuration.config_key == 'OPENAI_KEY').first()
        google = GoogleCloudPlatform()
        models = google.list_model_ai(search, GCP_API_KEY.config_value, OPENAI_KEY.config_value, ai_service)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=models)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_PROVIDERS_PUBLIC.endpoint_name,
    path=AccessEndpoint.GET_PROVIDERS_PUBLIC.endpoint_path,
    response_model=DataResponse[Page[ProviderResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_providers_public(
        company: int,
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: ProviderFilter = Depends(),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if company == 1:
            statement = db.query(Providers).filter(Providers.is_deleted == is_deleted, Providers.is_active == True)
        else:
            statement = db.query(Providers).filter(Providers.is_deleted == is_deleted, Providers.is_active == True,
                                                   Providers.is_public == True)
        providers = CRUDBase(Providers).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=providers)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.SET_PROVIDER_PUBLIC_OR_NOT.endpoint_name,
    path=AccessEndpoint.SET_PROVIDER_PUBLIC_OR_NOT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def set_provider_public_or_not(
        provider_id: str,
        is_public: bool,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_provider = db.query(Providers).filter(Providers.id == provider_id, Providers.is_deleted == False).first()
        if not db_provider:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        if is_public:
            db_provider.is_public = True
        else:
            db_provider.is_public = False

        db_provider.updated_at = datetime.now()
        db.commit()
        db.refresh(db_provider)

        # updated_chatroom = db.query(ChatRoom).filter(ChatRoom.provider_id == provider_id).all()

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
