import traceback
from typing import Optional, List
from fastapi import APIRouter, Depends, Request, File, UploadFile, Form, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from app.core.rag.processors.document_loader import DocumentLoader
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_user, AuthPer<PERSON><PERSON>hecker
from app.helpers.decorators import track_action
from app.helpers.endpoints import AccessEndpoint
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.chat import ChatReq
from app.models.users import Users, Role, UserRole
from app.services.chat_service import ConversationalHandler
from app.utils.common import get_message

router = APIRouter()


@router.post(
    name=AccessEndpoint.CHAT_AGENT.endpoint_name,
    path=AccessEndpoint.CHAT_AGENT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user, component=True))]
)
@track_action(action_name=AccessEndpoint.CHAT_AGENT.name)
async def chat_agent_completions(
        request: Request,
        message: str = Form(...),
        use_case_id: int = Form(...),
        provider_id: int | None = Form(None),
        language: str = Form("jp"),
        chatroom_code: str | None = Form(None),
        session_id: str | None = Form(None),
        enable_reasoning: bool | None = Form(None),
        files: Optional[List[UploadFile]] = File(None),
        db: Session = Depends(get_db)
) -> DataResponse:
    try:
        auth_info = request.state.user.to_dict()
        user_id = auth_info["client_id"]
        user = db.query(Users).filter(
            Users.id == user_id,
            Users.is_active == True,
            Users.is_deleted == False).first()

        user_roles = (
            db.query(Role.name)
            .join(UserRole, UserRole.role_id == Role.id)
            .filter(UserRole.user_id == user.id)
            .all()
        )

        user_info = {"full_name": user.full_name, "roles": [role.name for role in user_roles]}
        all_documents = []
        if files:
            loader = DocumentLoader()
            for file in files:
                source, document = await loader.load_document(file, language=language)
                all_documents.append({"source": source, "document": document})

        params = ChatReq(
            chatroom_code=chatroom_code,
            session_id=session_id,
            message=message,
            documents=all_documents,
            use_case_id=use_case_id,
            provider_id=provider_id,
            language=language,
            enable_reasoning=enable_reasoning
        )

        conversation_handler = ConversationalHandler(db=db, user_id=user_id, params=params)
        data = await conversation_handler.handle_request(user_info=user_info)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data)

    except SQLAlchemyError:
        db.rollback()
        raise GatewayException(status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                               detail=get_message("bad_request", language))
    except GatewayException:
        print(f"[ERROR][CHAT LLM]: {traceback.format_exc()}")
        raise
    except Exception:
        print(f"[POST][CHAT EXCEPTION]: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
