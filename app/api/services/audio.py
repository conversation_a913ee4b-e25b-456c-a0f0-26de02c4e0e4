import io
import traceback
from typing import Optional
import httpx
from fastapi import APIRouter, Depends, Request, UploadFile, File, Form, status as http_status
from fastapi.responses import StreamingResponse
from app.core.tts.text2speech import TTSProcessorFactory
from app.helpers.config import settings
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_user, AuthPermissionChecker
from app.schemas.base import DataResponse
from app.schemas.audio import TTSReq
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint
import base64
import tempfile

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_AUDIO_TTS.endpoint_name,
    path=AccessEndpoint.CREATE_AUDIO_TTS.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def create_audio_tts(
        request: Request,
        tts: TTSReq,
        language: Optional[str] = "jp"
) -> StreamingResponse:
    try:
        if tts.text:
            processor = TTSProcessorFactory.create_tts_processor()
            audio_data = await processor.audio_generate(tts.text)
            buffer = io.BytesIO(audio_data if isinstance(audio_data, bytes) else bytes(audio_data))
            buffer.seek(0)

            return StreamingResponse(buffer, media_type="audio/wav")

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(f"[POST][AUDIO EXCEPTION]: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.TRANSCRIBE_AUDIO.endpoint_name,
    path=AccessEndpoint.TRANSCRIBE_AUDIO.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def transcribe_audio(
        file: UploadFile = File(...),
        language: str = Form("ja"),
        source: str = Form("ja")
) -> DataResponse:
    try:
        if not file.filename.lower().endswith(('.mp3', '.wav', '.webm', '.mp4', '.ogg', '.flac')):
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="Unsupported file format.")

        file_data = await file.read()

        async with httpx.AsyncClient() as client:
            resp = await client.post(
                settings.STT_URL,
                files={"file": (file.filename, file_data)},
                data={"language": language, "source": source},
                timeout=600
            )

            if resp.status_code == 200:
                resp = resp.json()
                data = {
                    "text": resp.get('text', None)
                }

                return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=data)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(f"[POST][AUDIO EXCEPTION]: {traceback.format_exc()}")
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="Bad Request.")


@router.post(
    name=AccessEndpoint.TRANSCRIBE_AUDIO_V2.endpoint_name,
    path=AccessEndpoint.TRANSCRIBE_AUDIO_V2.endpoint_path,
    include_in_schema=True,
    response_model=DataResponse,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def transcribe_audio_v2(
        file: UploadFile = File(...),
        language: str = Form("ja")
) -> DataResponse:
    try:
        file_data = await file.read()
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp:
            tmp.write(file_data)
            tmp_path = tmp.name

        # Đọc file và encode base64
        with open(tmp_path, "rb") as audio_file:
            audio_content = base64.b64encode(audio_file.read()).decode("utf-8")

        payload = {
            "config": {
                "encoding": "LINEAR16",
                "languageCode": f"{language}-JP",
                # "sampleRateHertz": 48000
            },
            "audio": {
                "content": audio_content
            }
        }

        async with httpx.AsyncClient() as client:
            url = f"https://speech.googleapis.com/v1/speech:recognize?key={settings.GOOGLE_API_KEY}"
            response = await client.post(url, json=payload)

        if response.status_code != 200:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Google API Error: {response.text}"
            )

        data = response.json()

        text = ""
        for result in data.get("results", []):
            text += result["alternatives"][0]["transcript"]

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data={"text": text}
        )

    except GatewayException:
        raise
    except Exception:
        import traceback
        print(f"[POST][REST AUDIO ERROR]: {traceback.format_exc()}")
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="Failed to transcribe audio via REST API."
        )
