import os
import re
import time
import traceback
from fastapi import APIRouter, status as http_status
from fastapi import BackgroundTasks, Query
from fastapi import Depends
from typing import Optional, Any
from datetime import datetime

from starlette.responses import JSONResponse

from app.core.cloud_engine.google import GoogleCloudPlatform
from app.helpers.config import settings
from google.cloud import storage
from app.helpers.endpoints import AccessEndpoint
from app.helpers.exceptions import GatewayException
from app.utils.common import get_message
from app.schemas.base import DataResponse
from google.api_core.client_options import ClientOptions
from google.cloud import discoveryengine
from app.schemas.agent_builder import Agent<PERSON>uilder<PERSON><PERSON>, AgentBuilderCreateApp
from app.services.cloud_engine_service import DiscoveryEngineService
from app.helpers.security import validate_admin, AuthPermissionChecker
from app.db.base import get_db
from sqlalchemy.orm import Session
from app.models.company_subscriptions import CompanySubscriptions
from app.models.service_packages import ServicePackage
from app.models.agent_builders_information import AgentBuildersInformation
from app.models.datastores_information import DatastoresInformation
from app.models.config import Configuration
from app.helpers.constants import Project
from app.models.gcp_projects import GCPProject
from app.schemas.datastore_information import DSReps
from app.schemas.agent_builders_information import ABReps
from app.services.base import CRUDBase
from app.helpers.paging import Page, PaginationParams
from sqlalchemy import cast, String
from app.models.datastores_information_indexed import DatastoresInformationIndexed
from concurrent.futures import ThreadPoolExecutor

router = APIRouter()

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.GOOGLE_APPLICATION_CREDENTIALS
client = storage.Client()
SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
SOURCE_BUCKET_PREFIX = "gs://musashino-rag/"


@router.post(
    name=AccessEndpoint.CREATE_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.CREATE_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_create_data_store(
        company: int,
        slug: str,
        agent: AgentBuilderCreate,
        # agent_app: AgentBuilderCreateApp,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_db),
        location: Optional[str] = "global",
        language: Optional[str] = "jp",
) -> DataResponse:  # Replace with your actual DataResponse class
    try:
        google = GoogleCloudPlatform()

        # TODO: Check current project
        db_config = db.query(Configuration).filter(Configuration.config_key == Project.global_project).first()
        global_project_id = db_config.config_value if db_config else Project.global_project
        print(f"Global Project ID: {global_project_id}")
        # END TODO

        # TODO: Check package
        package = db.query(ServicePackage).join(CompanySubscriptions,
                                                ServicePackage.id == CompanySubscriptions.package_id).filter(
            CompanySubscriptions.company_id == company, CompanySubscriptions.status == 1,
            CompanySubscriptions.is_active == True).first()
        max_data_store = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company, DatastoresInformation.is_active == True,
            DatastoresInformation.is_deleted == False).count()
        if package.max_datastores <= max_data_store:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("max_datastore_exceed", language)
            )
        # END TODO

        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )
        project_id = get_current_project_id(db)
        db_gcp_project = db.query(GCPProject).filter(GCPProject.project_id == project_id).first()
        db_get_prefix = db.query(Configuration).filter(Configuration.config_key == settings.BUCKET_PREFIX).first()
        if global_project_id == project_id:
            project_id = global_project_id
            synced_path = db_gcp_project.bucket + '/' + agent.path
        else:
            # TODO: Copy the folder to internal bucket
            full_path = agent.path
            prefixes = [db_get_prefix.config_value]
            for prefix in prefixes:
                relative_path = full_path.partition(prefix)[1] + full_path.partition(prefix)[2]
                if relative_path != '':
                    break
            if relative_path == '':
                relative_path = full_path
            db_project = db.query(GCPProject).filter(GCPProject.project_id == global_project_id).first()
            synced_path = await google.copy_gcs_folder_to_internal(
                bucket=db_project.bucket,
                dest_bucket=db_gcp_project.bucket,
                relative_path=relative_path,
                dest_project_id=project_id,
                dest_path_override=relative_path
            )

        client = discoveryengine.DataStoreServiceClient(client_options=client_options)

        parent = client.collection_path(
            project=project_id,
            location=location,
            collection="default_collection",
        )

        bucket_name, prefix = google.parse_bucket_and_prefix(synced_path)

        ndjson_filename = f"{prefix}docs.ndjson"
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(ndjson_filename)

        if blob.exists():
            print(f"Found NDJSON: gs://{bucket_name}/{ndjson_filename}, deleting...")
            blob.delete()
        else:
            print("No NDJSON file found or doesn't exist, continuing...")
        print(f"Data copied to: gs://{synced_path}")

        # TODO: Create a unique data store ID
        data_store_id = (("DS_" + str(slug) + "_" + agent.display_name).lower().replace(" ",
                                                                                        "_")) + "_" + str(
            int(time.time()))
        # TODO: Create DataStore configuration
        data_store = discoveryengine.DataStore(
            display_name="DS_" + str(slug) + "_" + agent.display_name,
            industry_vertical=discoveryengine.IndustryVertical.GENERIC,
            solution_types=[discoveryengine.SolutionType.SOLUTION_TYPE_SEARCH],
            content_config=discoveryengine.DataStore.ContentConfig.CONTENT_REQUIRED,
            document_processing_config=discoveryengine.DocumentProcessingConfig(
                default_parsing_config=discoveryengine.DocumentProcessingConfig.ParsingConfig(
                    ocr_parsing_config=discoveryengine.DocumentProcessingConfig.ParsingConfig.OcrParsingConfig(
                        use_native_text=True
                    ),
                )
            )
        )

        # Create DataStore request
        request = discoveryengine.CreateDataStoreRequest(
            parent=parent,
            data_store_id=data_store_id,
            data_store=data_store,
        )

        # Create the data store
        operation = client.create_data_store(request=request)
        print(f"Waiting for operation to complete: {operation.operation.name}")
        operation.result()

        discoveryengine.CreateDataStoreMetadata(operation.metadata)

        client_docs = discoveryengine.DocumentServiceClient(client_options=client_options)

        # TODO : Create DataStore Agent Builder
        background_tasks.add_task(
            google.create_data_store_AB, synced_path, client_docs, project_id, location, data_store_id,
            slug)

        # Save Datastore Information to Database
        datastore_info = DatastoresInformation(
            datastore_name=agent.display_name,
            datastore_id=data_store_id,
            company_slug=slug,
            company_id=company,
            project_id=project_id,
            path=synced_path,
            location=location,
            description=agent.description
        )
        db.add(datastore_info)
        db.commit()
        db.refresh(datastore_info)

        # TODO 22/04/2025
        # Merge Create Agent Builder into Create Data Store

        project_id = get_current_project_id(db)
        package = db.query(ServicePackage).join(CompanySubscriptions,
                                                ServicePackage.id == CompanySubscriptions.package_id).filter(
            CompanySubscriptions.company_id == company, CompanySubscriptions.status == 1,
            CompanySubscriptions.is_active == True).first()
        max_agent_builders = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company, AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False).count()
        if package.max_agent_builders <= max_agent_builders:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("max_agent_builder_exceed", language)
            )
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )

        # Create a client
        client = discoveryengine.EngineServiceClient(client_options=client_options)

        parent = client.collection_path(
            project=project_id,
            location=location,
            collection="default_collection",
        )

        engine = discoveryengine.Engine(
            display_name="AB_" + str(slug) + "_" + agent.app_engine_name,
            # Options: GENERIC, MEDIA, HEALTHCARE_FHIR
            industry_vertical=discoveryengine.IndustryVertical.GENERIC,
            # Options: SOLUTION_TYPE_RECOMMENDATION, SOLUTION_TYPE_SEARCH, SOLUTION_TYPE_CHAT, SOLUTION_TYPE_GENERATIVE_CHAT
            solution_type=discoveryengine.SolutionType.SOLUTION_TYPE_SEARCH,
            # For search apps only
            search_engine_config=discoveryengine.Engine.SearchEngineConfig(
                # Options: SEARCH_TIER_STANDARD, SEARCH_TIER_ENTERPRISE
                search_tier=discoveryengine.SearchTier.SEARCH_TIER_ENTERPRISE,
                # Options: SEARCH_ADD_ON_LLM, SEARCH_ADD_ON_UNSPECIFIED
                search_add_ons=[discoveryengine.SearchAddOn.SEARCH_ADD_ON_LLM],

            ),
            # For generic recommendation apps only
            # similar_documents_config=discoveryengine.Engine.SimilarDocumentsEngineConfig,
            data_store_ids=[data_store_id],
        )
        engine_id = "ab_" + str(slug) + "_" + agent.app_engine_id + "_" + str(int(time.time()))
        request = discoveryengine.CreateEngineRequest(
            parent=parent,
            engine=engine,
            engine_id=engine_id,
        )

        # Make the request
        operation = client.create_engine(request=request)

        print(f"Waiting for operation to complete: {operation.operation.name}")
        response = operation.result()

        # After the operation is complete,
        # get information from operation metadata
        metadata = discoveryengine.CreateEngineMetadata(operation.metadata)

        agent_builder_info = AgentBuildersInformation(
            agent_builder_name=agent.app_engine_name,
            agent_builder_id=engine_id,
            data_store_ids=[data_store_id],
            company_slug=company,
            project_id=project_id,
            location="",
            company_id=company,
            description=agent.description_app
        )
        db.add(agent_builder_info)
        db.commit()
        db.refresh(agent_builder_info)
        print(response)
        print(metadata)

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=f"Datastore {agent.display_name} created successfully." if language == "jp" else f"データストア {agent.display_name} が正常に作成されました。"
        )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.DELETE_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_delete_data_store(
        company: int,
        slug: str,
        datastore_id: str,
        location: Optional[str] = "global",
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )

        db_data_store = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company,
            # Companies.is_active == True,
            DatastoresInformation.datastore_id == datastore_id,
            DatastoresInformation.is_deleted == False,
            DatastoresInformation.is_active == True
        ).first()

        db_agent_builder = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company,
            cast(AgentBuildersInformation.data_store_ids, String).like(f'%"{datastore_id}"%'),
            AgentBuildersInformation.is_deleted == False,
            AgentBuildersInformation.is_active == True
        ).first()

        # Delete DataStore GCP
        client = discoveryengine.DataStoreServiceClient(client_options=client_options)
        clientApp = discoveryengine.EngineServiceClient(client_options=client_options)

        project_id = db_data_store.project_id if db_data_store else get_current_project_id(db)
        if db_agent_builder:
            # TODO: Delete the engine
            # Format: "projects/{project_id}/locations/{location}/collections/default_collection/engines/{engine_id}"
            parent = clientApp.collection_path(
                project=project_id,
                location=location,
                collection="default_collection",
            )
            engine_name = f"{parent}/engines/{db_agent_builder.agent_builder_id}"

            request = discoveryengine.DeleteEngineRequest(name=engine_name)

            operation = clientApp.delete_engine(request=request)
            print(f"Waiting for delete operation to complete: {operation.operation.name}")

        parent = client.collection_path(
            project=project_id,
            location=location,
            collection="default_collection",
        )

        datastore_name = f"{parent}/dataStores/{datastore_id}"

        request = discoveryengine.DeleteDataStoreRequest(name=datastore_name)

        operation = client.delete_data_store(request=request)
        print(f"Waiting for deletion operation to complete: {operation.operation.name}")
        # operation.result()

        # Delete From Database
        if db_data_store:
            db_data_store.is_deleted = True
            db_data_store.is_active = False
            db_data_store.deleted_at = datetime.now()
            db.commit()
            db.refresh(db_data_store)

        if db_agent_builder:
            db_agent_builder.is_deleted = True
            db_agent_builder.is_active = False
            db.commit()
            db.refresh(db_agent_builder)

        if db_agent_builder:
            message = (
                f"Datastore {datastore_id}, Agent Builder {db_agent_builder.agent_builder_id} deleted successfully."
                if language == "jp"
                else f"Datastore {datastore_id}, Agent Builder {db_agent_builder.agent_builder_id} が正常に削除されました。"
            )
        else:
            message = (
                f"Datastore {datastore_id} deleted successfully."
                if language == "jp"
                else f"Datastore {datastore_id} が正常に削除されました。"
            )
        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=message
        )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        error_message = str(e)
        if language == "jp" and "currently exists in list" in error_message:
            match = re.search(r"engines/([\w\-]+)", error_message)
            if match:
                agent_builder_name = match.group(1)
            else:
                agent_builder_name = "不明"
            custom_error = (
                f"このデータストアは、他のAgentBuilderで使用しているため削除できません\n\n"
                f"AgentBuilderID：{agent_builder_name}"
            )
        elif language == "en" and "currently exists in list" in error_message:
            match = re.search(r"engines/([\w\-]+)", error_message)
            if match:
                agent_builder_name = match.group(1)
            else:
                agent_builder_name = "Unknown"
            custom_error = (
                f"This datastore cannot be deleted because it is used by another AgentBuilder\n\n"
                f"AgentBuilder Name: {agent_builder_name}"
            )
        else:
            custom_error = get_message("bad_request", language)
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=custom_error
        )


@router.post(
    name=AccessEndpoint.VECTOR_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.VECTOR_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_vector_data_store(
        company: int,
        slug: str,
        data_store_id: str,
        agent: AgentBuilderCreate,
        background_tasks: BackgroundTasks,
        project_id: Optional[str] = "musashino-rag",
        location: Optional[str] = "global",
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )
        # Now use DocumentServiceClient for document indexing
        client_docs = discoveryengine.DocumentServiceClient(client_options=client_options)

        google = DiscoveryEngineService()
        background_tasks.add_task(
            google.create_data_store_agent_builder(agent.path, client_docs, project_id, location, data_store_id,
                                                   slug))

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=f"Datastore sync successful." if language == "jp" else f"データストアの同期が成功しました。"
        )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.CREATE_APP_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.CREATE_APP_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_create_app(
        company: int,
        slug: str,
        background_tasks: BackgroundTasks,
        agent: AgentBuilderCreateApp,
        db: Session = Depends(get_db),
        location: Optional[str] = "global",
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        project_id = get_current_project_id(db)
        package = db.query(ServicePackage).join(CompanySubscriptions,
                                                ServicePackage.id == CompanySubscriptions.package_id).filter(
            CompanySubscriptions.company_id == company, CompanySubscriptions.status == 1,
            CompanySubscriptions.is_active == True).first()
        max_agent_builders = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company, AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False).count()
        if package.max_agent_builders <= max_agent_builders:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("max_agent_builder_exceed", language)
            )
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )

        # Create a client
        client = discoveryengine.EngineServiceClient(client_options=client_options)

        # The full resource name of the collection
        # e.g. projects/{project}/locations/{location}/collections/default_collection
        parent = client.collection_path(
            project=project_id,
            location=location,
            collection="default_collection",
        )

        engine = discoveryengine.Engine(
            display_name="AB_" + str(slug) + "_" + agent.app_engine_name,
            # Options: GENERIC, MEDIA, HEALTHCARE_FHIR
            industry_vertical=discoveryengine.IndustryVertical.GENERIC,
            # Options: SOLUTION_TYPE_RECOMMENDATION, SOLUTION_TYPE_SEARCH, SOLUTION_TYPE_CHAT, SOLUTION_TYPE_GENERATIVE_CHAT
            solution_type=discoveryengine.SolutionType.SOLUTION_TYPE_SEARCH,
            # For search apps only
            search_engine_config=discoveryengine.Engine.SearchEngineConfig(
                # Options: SEARCH_TIER_STANDARD, SEARCH_TIER_ENTERPRISE
                search_tier=discoveryengine.SearchTier.SEARCH_TIER_ENTERPRISE,
                # Options: SEARCH_ADD_ON_LLM, SEARCH_ADD_ON_UNSPECIFIED
                search_add_ons=[discoveryengine.SearchAddOn.SEARCH_ADD_ON_LLM],

            ),
            # For generic recommendation apps only
            # similar_documents_config=discoveryengine.Engine.SimilarDocumentsEngineConfig,
            data_store_ids=agent.data_store_ids,
        )
        engine_id = "ab_" + str(slug) + "_" + agent.app_engine_id + "_" + str(int(time.time()))
        request = discoveryengine.CreateEngineRequest(
            parent=parent,
            engine=engine,
            engine_id=engine_id,
        )

        # Make the request
        operation = client.create_engine(request=request)

        print(f"Waiting for operation to complete: {operation.operation.name}")
        response = operation.result()

        # After the operation is complete,
        # get information from operation metadata
        metadata = discoveryengine.CreateEngineMetadata(operation.metadata)

        agent_builder_info = AgentBuildersInformation(
            agent_builder_name=agent.app_engine_name,
            agent_builder_id=engine_id,
            data_store_ids=agent.data_store_ids,
            company_slug=company,
            project_id=project_id,
            location="",
            company_id=company,
            description=agent.description
        )
        db.add(agent_builder_info)
        db.commit()
        db.refresh(agent_builder_info)
        # Handle the response
        print(response)
        print(metadata)
        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=operation.operation.name
        )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_APP_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.DELETE_APP_AGENT_BUILDER.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_delete_app(
        company: int,
        slug: str,
        engine_id: str,
        db: Session = Depends(get_db),
        location: Optional[str] = "global",
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        project_id = get_current_project_id(db)
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global" else None
        )

        client = discoveryengine.EngineServiceClient(client_options=client_options)

        # operation.result()

        # TODO: Create the full resource name of the engine
        # Format: "projects/{project_id}/locations/{location}/collections/default_collection/engines/{engine_id}"
        parent = client.collection_path(
            project=project_id,
            location=location,
            collection="default_collection",
        )
        engine_name = f"{parent}/engines/{engine_id}"

        request = discoveryengine.DeleteEngineRequest(name=engine_name)

        operation = client.delete_engine(request=request)
        print(f"Waiting for delete operation to complete: {operation.operation.name}")

        db_agent_builder = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company,
            # Companies.is_active == True,
            AgentBuildersInformation.agent_builder_id == engine_id,
            AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False
        ).first()
        if db_agent_builder:
            db_agent_builder.is_deleted = True
            db_agent_builder.is_active = False
            db_agent_builder.deleted_at = datetime.now()
            db.commit()
            db.refresh(db_agent_builder)

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=(
                f"Engine {engine_id} deleted successfully."
                if language == "jp"
                else f"エンジン {engine_id} が正常に削除されました。"
            )
        )

    except GatewayException as e:
        raise e

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_LIST_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.GET_LIST_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=DataResponse[Page[DSReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_list_data_store(
        company: int,
        slug: str,
        project_id: str = "musashino-rag",
        location: str = "global",
        page: PaginationParams = Depends(),
        search: Optional[str] = Query(None, description="Filter DataStores by ID or name prefix"),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        statement = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company,
            DatastoresInformation.is_active == True,
            DatastoresInformation.is_deleted == False
        )
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                DatastoresInformation.datastore_name.ilike(f"%{search}%", escape='\\'),
                DatastoresInformation.datastore_id.ilike(f"%{search}%", escape='\\')
            )
        list_data_store = CRUDBase(DatastoresInformation).list(db=db, query=statement, params=page)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=list_data_store)

        # client = discoveryengine.DataStoreServiceClient()
        # parent = f"projects/{project_id}/locations/{location}/collections/default_collection"
        #
        # response = client.list_data_stores(parent=parent)
        #
        # filtered_data_stores = []
        # prefix = f"DS_{slug}_"
        # search_lower = search.lower() if search else None
        # for datastore in response:
        #     if not datastore.display_name.startswith(prefix):
        #         continue
        #
        #     ds_id = datastore.name.split("/")[-1]
        #     ds_slug = getattr(datastore, "slug", None)
        #
        #     if search_lower:
        #         if (search_lower not in ds_id.lower() and
        #                 search_lower not in datastore.display_name.lower() and
        #                 (ds_slug is None or search_lower not in ds_slug.lower())):
        #             continue
        #     db_datastore_info = (db.query(DatastoresInformation)
        #                          .filter(
        #         DatastoresInformation.datastore_id == ds_id,
        #         DatastoresInformation.company_id == company
        #     )
        #                          .first())
        #     filtered_data_stores.append({
        #         "id": ds_id,
        #         "display_name": datastore.display_name,
        #         "industry_vertical": datastore.industry_vertical,
        #         "solution_types": [st.name for st in datastore.solution_types],
        #         "path": db_datastore_info.path if db_datastore_info else None,
        #         "description": db_datastore_info.description if db_datastore_info else None,
        #     })
        #
        # total_items = len(filtered_data_stores)
        # start_index = (page - 1) * page_size
        # end_index = start_index + page_size
        # paginated_data_stores = (
        #     filtered_data_stores[start_index:end_index]
        #     if start_index < total_items
        #     else []
        # )
        #
        # metadata = {
        #     "current_page": page,
        #     "page_size": page_size,
        #     "total_items": total_items,
        # }
        # return DataResponse.success(
        #     statusCode=200,
        #     data={
        #         "items": paginated_data_stores,
        #         "metadata": metadata,
        #     }
        # )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_LIST_APP_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.GET_LIST_APP_AGENT_BUILDER.endpoint_path,
    response_model=DataResponse[Page[ABReps]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_list_app(
        company: int,
        slug: str,
        project_id: str = "musashino-rag",
        location: str = "global",
        page: PaginationParams = Depends(),
        page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
        search: Optional[str] = Query(None, description="Filter App Agent by ID or name prefix"),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> DataResponse:
    try:
        statement = db.query(AgentBuildersInformation).filter(
            AgentBuildersInformation.company_id == company,
            AgentBuildersInformation.is_active == True,
            AgentBuildersInformation.is_deleted == False
        )
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                AgentBuildersInformation.agent_builder_name.ilike(f"%{search}%", escape='\\'),
                AgentBuildersInformation.agent_builder_id.ilike(f"%{search}%", escape='\\')
            )
        list_agent_builders = CRUDBase(AgentBuildersInformation).list(db=db, query=statement, params=page)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=list_agent_builders)
        # google = GoogleCloudPlatform()
        # client_options = (
        #     ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
        #     if location != "global"
        #     else None
        # )
        #
        # client = discoveryengine.EngineServiceClient(client_options=client_options)
        #
        # parent = client.collection_path(
        #     project=project_id,
        #     location=location,
        #     collection="default_collection",
        # )
        #
        # response = client.list_engines(parent=parent)
        #
        # filtered_engines = []
        # prefix = f"AB_{slug}_"
        # search_lower = search.lower() if search else None
        #
        # for engine in response:
        #     if not engine.display_name.startswith(prefix):
        #         continue
        #
        #     engine_id = engine.name.split("/")[-1]
        #     engine_slug = getattr(engine, "slug", None)
        #
        #     if search_lower:
        #         if (search_lower not in engine_id.lower() and
        #                 search_lower not in engine.display_name.lower() and
        #                 (engine_slug is None or search_lower not in engine_slug.lower())):
        #             continue
        #
        #     db_agent_builder_info = (db.query(AgentBuildersInformation)
        #                              .filter(
        #         AgentBuildersInformation.agent_builder_id == engine_id,
        #         AgentBuildersInformation.company_id == company
        #     )
        #                              .first())
        #     filtered_engines.append({
        #         "id": engine_id,
        #         "name": engine.display_name,
        #         "list_data_stores": db_agent_builder_info.data_store_ids if db_agent_builder_info else [],
        #         "description": db_agent_builder_info.description if db_agent_builder_info else None,
        #         "create_time": engine.create_time,
        #         "update_time": engine.update_time,
        #     })
        #
        # total_items = len(filtered_engines)
        # start_index = (page - 1) * page_size
        # end_index = start_index + page_size
        # paginated_engines = filtered_engines[start_index:end_index] if start_index < total_items else []
        #
        # metadata = {
        #     "current_page": page,
        #     "page_size": page_size,
        #     "total_items": total_items,
        # }
        #
        # return DataResponse.success(
        #     statusCode=200,
        #     data={
        #         "items": paginated_engines,
        #         "metadata": metadata
        #     }
        # )

    except GatewayException as e:
        raise e

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.TRIGGER_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.TRIGGER_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_trigger_data_store(
        company: int,
        slug: str,
        background_tasks: BackgroundTasks,
        project_id: str = "musashino-rag",
        location: str = "global",
        collection: str = "default_collection",
        data_store_id: str = "your-datastore-id",
        path: str = "your-bucket-name/your-folder-path/",
        language: Optional[str] = "jp",
) -> dict:
    """
    1) List all files in GCS (prefix)
    2) Download file, convert to text depending on format (PDF, DOCX, TXT,...)
    3) Create NDJSON file
    4) Upload NDJSON -> GCS
    5) Call import_documents to update DataStore
    """
    try:
        google = GoogleCloudPlatform()
        bucket_name, prefix = google.parse_bucket_and_prefix(path)
        ndjson_filename = f"{prefix}docs.ndjson"
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(ndjson_filename)

        if blob.exists():
            print(f"Found NDJSON: gs://{path}, deleting...")
            blob.delete()
        else:
            print("No NDJSON file found or doesn't exist, continuing...")
        background_tasks.add_task(
            google.create_data_store_AB_trigger, project_id, location, collection, data_store_id, path)
        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=(
                f"ファイルをテキストに変換し、データストア {data_store_id} を更新しました。"
                if language == "jp"
                else f"Converted files to text and updated datastore {data_store_id} successfully."
            )
        )

    except Exception as e:
        print(traceback.format_exc())
        error_msg = str(e)
        if "Given gcs input path matched no files" in error_msg:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("error_trigger_file", language)
            )
        else:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("bad_request", language) + f" ({error_msg})"
            )


# @router.get(
#     name=AccessEndpoint.TRIGGER_DATASTORE_AGENT_BUILDER.endpoint_name,
#     path=AccessEndpoint.TRIGGER_DATASTORE_AGENT_BUILDER.endpoint_path,
#     response_model=None,
#     include_in_schema=True,
#     dependencies=[Depends(AuthPermissionChecker(validate_admin))]
# )
# async def agent_builder_trigger_data_store(
#         company: int,
#         slug: str,
#         project_id: str = "musashino-rag",
#         location: str = "global",
#         collection: str = "default_collection",
#         data_store_id: str = "your-datastore-id",
#         # bucket_name: str = "musashino",
#         path: str = "your-folder-path",
#         language: Optional[str] = "jp",
# ) -> dict:
#     """
#     Trigger import from GCS to Datastore
#     """
#     try:
#         client = discoveryengine.DocumentServiceClient()
#
#         parent = f"projects/{project_id}/locations/{location}/collections/{collection}/dataStores/{data_store_id}/branches/default_branch"
#
#         print(f"Trigger Import data from GCS to Datastore: {data_store_id}")
#
#         # "gs://bucket-name/folder/*"
#         input_uris = [f"gs://{path}docs.ndjson"]
#
#         import_request = discoveryengine.ImportDocumentsRequest(
#             parent=parent,
#             gcs_source=discoveryengine.GcsSource(
#                 input_uris=input_uris,
#                 data_schema="document"
#             )
#         )
#
#         operation = client.import_documents(request=import_request)
#         operation.result()  # Chờ Import hoàn thành
#
#         return DataResponse().success(statusCode=http_status.HTTP_200_OK,
#                                       data=(
#                                           f"データストア {data_store_id} を正常に更新しました。"
#                                           if language == "jp"
#                                           else f"Update datastore {data_store_id} successfully."
#                                       ))
#
#     except Exception as e:
#         print(traceback.format_exc())
#         error_msg = str(e)
#         if "Given gcs input path matched no files" in error_msg:
#             raise GatewayException(
#                 status_code=http_status.HTTP_400_BAD_REQUEST,
#                 detail=get_message("error_trigger_file", language)
#             )
#         else:
#             raise GatewayException(
#                 status_code=http_status.HTTP_400_BAD_REQUEST,
#                 detail=get_message("bad_request", language)
#             )


@router.get(
    name=AccessEndpoint.IMPORT_DATASTORE_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.IMPORT_DATASTORE_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_import_data_store(
        location: Optional[str] = "global",
        collection: Optional[str] = "default_collection",
        data_store_id: str = "your-datastore-id",
        folder_path: str = "your-folder-path",
        import_option: Optional[str] = "incremental",
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> dict:
    """
    Trigger import from GCS to Datastore.
    import_option:
       - "incremental": import only new documents (default)
       - "full": overwrite existing documents, re-import all documents in the folder.
    """
    try:
        google = GoogleCloudPlatform()
        project_id = get_current_project_id(db)
        client_options = (
            ClientOptions(api_endpoint=f"{location}-discoveryengine.googleapis.com")
            if location != "global"
            else None
        )
        db_config = db.query(Configuration).filter(Configuration.config_key == Project.global_project).first()
        global_project_id = db_config.config_value if db_config else Project.global_project
        print(f"Global Project ID: {global_project_id}")
        if global_project_id == project_id:
            project_id = global_project_id
            synced_path = folder_path
        else:
            # TODO: Copy the folder to internal bucket
            full_path = folder_path
            relative_path = full_path.partition("customers/")[1] + full_path.partition("customers/")[2]
            db_project = db.query(GCPProject).filter(GCPProject.project_id == project_id).first()
            synced_path = await google.copy_gcs_folder_to_internal(
                bucket=db_project.bucket,
                dest_bucket="None",
                relative_path=relative_path,
                dest_project_id=project_id,
                dest_path_override=relative_path,
            )

        client = discoveryengine.DocumentServiceClient(client_options=client_options)

        parent = f"projects/{project_id}/locations/{location}/collections/{collection}/dataStores/{data_store_id}/branches/default_branch"

        print(f"Trigger Import data from GCS to Datastore: {data_store_id}")

        # Format: "gs://bucket-name/path/to/folder/*"
        input_uris = [f"gs://{synced_path}*"]

        # if import_option then "full", set overwrite_documents = True, opposite False
        overwrite_documents = True if import_option.lower() == "full" else False

        import_request = discoveryengine.ImportDocumentsRequest(
            parent=parent,
            gcs_source=discoveryengine.GcsSource(
                input_uris=input_uris,
                data_schema="document"
            ),
            overwrite_documents=overwrite_documents
        )

        operation = client.import_documents(request=import_request)
        operation.result()

        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=(
                f"データストア {data_store_id} を正常に更新しました。"
                if language == "jp"
                else f"Update datastore {data_store_id} successfully."
            )
        )

    except Exception as e:
        print(traceback.format_exc())
        error_msg = str(e)
        if "Given gcs input path matched no files" in error_msg:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="This folder doesnt have file to trigger"
            )
        else:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("bad_request", language)
            )


@router.post(
    name=AccessEndpoint.IMPORT_DOCUMENT_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.IMPORT_DOCUMENT_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    # dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_import_document(
        company: int,
        slug: str,
        background_tasks: BackgroundTasks,
        # project_id: Optional[str] = "musashino-rag",
        location: Optional[str] = "global",
        collection: Optional[str] = "default_collection",
        data_store_id: str = "your-datastore-id",
        folder_path: str = "your-folder-path",
        import_mode: str = "incremental",
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> JSONResponse | Any:
    try:
        project_id = get_current_project_id(db)

        db_data_store = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company,
            # Companies.is_active == True,
            DatastoresInformation.datastore_id == data_store_id,
            DatastoresInformation.is_active == True,
            DatastoresInformation.is_deleted == False
        ).first()

        # TODO : Check index progress
        def is_datastore_indexing(project_id: str, location: str, data_store_id: str) -> bool:
            client = discoveryengine.DataStoreServiceClient()
            operations_client = client.transport.operations_client

            parent = f"projects/{project_id}/locations/{location}/collections/default_collection/dataStores/{data_store_id}"

            response = operations_client.list_operations(name=parent,
                                                         filter_="metadata.@type:type.googleapis.com/google.cloud.discoveryengine.v1.ImportDocumentsMetadata")

            for op in response:
                if not op.done:
                    return True
            return False

        if is_datastore_indexing(db_data_store.project_id, location, data_store_id):
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=f"{data_store_id} は現在インデックス作成中です。しばらくしてからもう一度お試しください。" if language == "jp" else f"{data_store_id} is currently indexing. Please try again later.")

        google = GoogleCloudPlatform()
        db_config = db.query(Configuration).filter(Configuration.config_key == Project.global_project).first()
        global_project_id = db_config.config_value if db_config else Project.global_project
        print(f"Global Project ID: {global_project_id}")
        db_gcp_project = db.query(GCPProject).filter(GCPProject.project_id == project_id).first()
        db_get_prefix = db.query(Configuration).filter(Configuration.config_key == settings.BUCKET_PREFIX).first()
        if global_project_id == project_id:
            project_id = global_project_id
            synced_path = db_gcp_project.bucket + '/' + folder_path
        else:
            # TODO: Copy the folder to internal bucket
            full_path = folder_path
            prefixes = [db_get_prefix.config_value]
            for prefix in prefixes:
                relative_path = full_path.partition(prefix)[1] + full_path.partition(prefix)[2]
                if relative_path != '':
                    break
            if relative_path == '':
                relative_path = full_path
            db_project = db.query(GCPProject).filter(GCPProject.project_id == global_project_id).first()

            # TODO : Delete existing files in the folder if import_mode is "full" or "overwrite"
            def delete_blob(blob):
                try:
                    blob.delete()
                except Exception as e:
                    print(f"Error deleting {blob.name}: {e}")

            if import_mode.lower() in ["full", "overwrite"]:
                bucket_name = db_gcp_project.bucket
                prefix = relative_path

                if not prefix.endswith("/"):
                    prefix += "/"

                print(f"Preparing to delete folder: gs://{bucket_name}/{prefix}")
                storage_client = storage.Client(project=project_id)
                bucket = storage_client.bucket(bucket_name)

                try:
                    blobs = list(bucket.list_blobs(prefix=prefix))
                    total_files = len(blobs)

                    if not blobs:
                        print("No existing files found to delete.")
                    elif total_files > 200:
                        print(f"Found {total_files} files → using parallel deletion.")
                        with ThreadPoolExecutor(max_workers=32) as executor:
                            executor.map(delete_blob, blobs)
                        print("Parallel deletion complete.")
                    else:
                        print(f"Found {total_files} files → using standard batch deletion.")
                        bucket.delete_blobs(blobs)
                        print("Standard batch deletion complete.")
                except Exception as e:
                    print(f"Error deleting files from gs://{bucket_name}/{prefix}")
                    print(traceback.format_exc())
                    raise

            synced_path = await google.copy_gcs_folder_to_internal(
                bucket=db_project.bucket,
                dest_bucket=db_gcp_project.bucket,
                relative_path=relative_path,
                dest_project_id=project_id,
                dest_path_override=relative_path
            )
        bucket_name, prefix = google.parse_bucket_and_prefix(synced_path)
        ndjson_filename = f"{prefix}docs.ndjson"
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(ndjson_filename)

        if blob.exists():
            print(f"Found NDJSON: gs://{synced_path}, deleting...")
            blob.delete()
        else:
            print("No NDJSON file found or doesn't exist, continuing...")
        background_tasks.add_task(
            google.import_document, db_data_store.project_id, location, collection, data_store_id, synced_path,
            import_mode)

        # TODO: Save to table datastore_information_indexed
        if db_data_store:
            indexed_entry = DatastoresInformationIndexed(
                datastore_id=data_store_id,
                project_id=db_data_store.project_id,
                path_indexed=folder_path,
                import_mode=import_mode,
                is_deleted=False,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(indexed_entry)
            db.commit()
            db.refresh(indexed_entry)
        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data=(
                f"{data_store_id} の新しいデータのインポートに成功しました。"
                if language == "jp"
                else f"Import successful for {data_store_id}."
            )
        )

    except GatewayException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        error_msg = str(e)
        if "Given gcs input path matched no files" in error_msg:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("error_import_file", language)
            )
        else:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("bad_request", language) + f" ({error_msg})"
            )


@router.get(
    name=AccessEndpoint.GET_LIST_DOCUMENT_INDEXED_AGENT_BUILDER.endpoint_name,
    path=AccessEndpoint.GET_LIST_DOCUMENT_INDEXED_AGENT_BUILDER.endpoint_path,
    response_model=None,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def agent_builder_get_list_document_indexed(
        company: int,
        slug: str,
        project_id: Optional[str] = "musashino-rag",
        location: Optional[str] = "global",
        collection: Optional[str] = "default_collection",
        data_store_id: str = "your-datastore-id",
        search: Optional[str] = None,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
) -> dict:
    try:
        google = GoogleCloudPlatform()
        client = discoveryengine.DocumentServiceClient()

        db_data_store = db.query(DatastoresInformation).filter(
            DatastoresInformation.company_id == company,
            # Companies.is_active == True,
            DatastoresInformation.datastore_id == data_store_id,
            DatastoresInformation.is_deleted == False,
            DatastoresInformation.is_active == True
        ).first()

        parent = (
            f"projects/{db_data_store.project_id}/locations/{location}/collections/{collection}"
            f"/dataStores/{data_store_id}/branches/default_branch"
        )

        documents = client.list_documents(parent=parent)
        result = []
        for doc in documents:
            uri = doc.content.uri
            title = doc.struct_data.get("title", "")
            preview = doc.struct_data.get("text", "")[:100]
            view_url = google.generate_signed_url(uri)
            item = {
                "id": doc.id,
                "uri": uri,
                "view_url": view_url,
                "mimeType": doc.content.mime_type,
                "title": title,
                "preview": preview,
            }

            if search:
                if search.lower() in uri.lower():
                    result.append(item)
            else:
                result.append(item)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=result)

    except Exception as e:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


def get_current_project_id(db: Session = Depends(get_db)):
    config = db.query(Configuration).filter(
        Configuration.config_key == Project.key,
        Configuration.is_deleted == False
    ).first()
    if not config:
        raise GatewayException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail="Project configuration not found"
        )
    return config.config_value
