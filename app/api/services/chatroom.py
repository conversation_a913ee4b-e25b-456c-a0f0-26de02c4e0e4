import traceback
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, Request, status as http_status
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, validate_user, AuthPer<PERSON><PERSON><PERSON><PERSON>
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.services import (CreateChatRoom, UpdateChatRoom, ChatRoomResp, ChatRoomFilters, ChatRoomRespV2,
                                  ChatRoomRespV3)
from app.models.users import Users, Role, UserRole
from app.models.ai_services import Chat<PERSON>oom, UseC<PERSON>, ChatHistory, Providers
from app.services.base import CRUDBase
from app.utils.common import get_message
from app.helpers.endpoints import AccessEndpoint

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_CHATROOM.endpoint_name,
    path=AccessEndpoint.CREATE_CHATROOM.endpoint_path,
    response_model=DataResponse[ChatRoomResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_chatroom(
        chatroom: CreateChatRoom,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        is_chatroom_exists = db.query(exists().where(ChatRoom.title == chatroom.title)).scalar()
        if is_chatroom_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("chatroom_name_exist", language))

        db_chatroom = ChatRoom(**chatroom.dict())
        db.add(db_chatroom)
        db.commit()
        db.refresh(db_chatroom)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_chatroom)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CHATROOM.endpoint_name,
    path=AccessEndpoint.GET_CHATROOM.endpoint_path,
    response_model=DataResponse[ChatRoomRespV3],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_chatroom(
        request: Request,
        chatroom_id: str,
        company: Optional[int] = None,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp") -> DataResponse:
    try:
        auth = request.state.user.to_dict()
        client_id = auth.get('client_id')
        user_roles = db.query(Role).join(UserRole, UserRole.role_id == Role.id).join(Users,
                                                                                     Users.id == UserRole.user_id).filter(
            Role.is_active == True, Role.is_deleted == False,
            UserRole.user_id == client_id).all()
        db_chatroom = None
        for role in user_roles:
            if role.name == "Admin" and company:
                db_chatroom = db.query(ChatRoom).filter(
                    ChatRoom.id == chatroom_id,
                    ChatRoom.is_deleted == is_deleted
                ).first()
            elif company:
                db_chatroom = db.query(ChatRoom).filter(
                    ChatRoom.id == chatroom_id,
                    ChatRoom.is_deleted == is_deleted
                ).first()
            else:
                db_chatroom = db.query(ChatRoom).filter(
                    ChatRoom.user_id == client_id,
                    ChatRoom.id == chatroom_id,
                    ChatRoom.is_deleted == is_deleted
                ).first()
        if not db_chatroom:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        chat_his = db.query(ChatHistory).filter(ChatHistory.chatroom_id == chatroom_id).first()
        usecases = db.query(UseCases).filter(UseCases.id == chat_his.use_case_id).first()

        if not usecases:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_chatroom.use_case_title = usecases.use_case_title
        db_chatroom.ai_service = usecases.ai_service
        db_chatroom.llms_model_name = usecases.llms_model_name

        user = db.query(Users).filter(Users.id == db_chatroom.user_id).first()
        db_chatroom.avatar_url = user.avatar_url
        db_provider = db.query(Providers).filter(Providers.id == db_chatroom.provider_id,
                                                 Providers.is_active == True,
                                                 Providers.is_deleted == False).first()
        if db_provider:
            db_chatroom.llms_model = db_chatroom.llms_model = {
                "id": db_provider.id,
                "ai_service": db_provider.ai_service,
                "service_name": db_provider.service_name,
                "model_name": db_provider.model_name,
                "llm_model_id": db_provider.llm_model_id
            }

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_chatroom)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_YOUR_CHATROOM.endpoint_name,
    path=AccessEndpoint.GET_YOUR_CHATROOM.endpoint_path,
    response_model=DataResponse[Page[ChatRoomResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_your_chatroom(request: Request, is_deleted: bool = False, page: PaginationParams = Depends(),
                            db: Session = Depends(get_db),
                            language: Optional[str] = "jp") -> DataResponse:
    try:
        auth = request.state.user.to_dict()
        client_id = auth.get('client_id')
        statement = db.query(ChatRoom).filter(
            ChatRoom.user_id == client_id,
            ChatRoom.is_active == True,
            ChatRoom.is_deleted == is_deleted
        )
        chatrooms = CRUDBase(ChatRoom).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=chatrooms)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CHATROOMS.endpoint_name,
    path=AccessEndpoint.GET_CHATROOMS.endpoint_path,
    response_model=DataResponse[Page[ChatRoomRespV2]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_chatroom(
        company: Optional[int] = None,
        user_id: Optional[int] = None,
        search: Optional[str] = Query(None, description="Search by Chat Room Title, User Name, or User Email"),
        filters: ChatRoomFilters = Depends(),
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(ChatRoom).filter(
            ChatRoom.is_active == True,
            ChatRoom.is_deleted == False
        )

        if user_id is not None:
            statement = statement.filter(ChatRoom.user_id == user_id)

        if company or (search and search.strip()):
            statement = statement.join(Users, Users.id == ChatRoom.user_id)

        if search and search.strip():
            safe_search = search.replace("%", "\\%").replace("_", "\\_")
            statement = statement.filter(
                (ChatRoom.title.ilike(f"%{safe_search}%")) |
                (Users.full_name.ilike(f"%{safe_search}%")) |
                (Users.email.ilike(f"%{safe_search}%"))
            )

        if company:
            statement = statement.filter(Users.company_id == company)

        if filters.use_case_title is not None:
            statement = statement.join(UseCases, ChatRoom.use_case_id == UseCases.id) \
                .filter(UseCases.use_case_title.in_(filters.use_case_title.split(",")))

        if filters.service is not None or filters.model is not None:
            statement = statement.join(UseCases, ChatRoom.use_case_id == UseCases.id)

        if filters.service is not None:
            statement = statement.filter(UseCases.ai_service == filters.service)

        if filters.model is not None:
            statement = statement.filter(UseCases.llms_model_name == filters.model)

        if filters.reaction is not None:
            if filters.reaction == "empty":
                statement = statement.join(ChatHistory, ChatRoom.id == ChatHistory.chatroom_id) \
                    .filter(ChatHistory.reactions.is_(None)).group_by(ChatRoom.id)
            else:
                statement = statement.join(ChatHistory, ChatRoom.id == ChatHistory.chatroom_id) \
                    .filter(ChatHistory.reactions == filters.reaction)
        chatroom = CRUDBase(ChatRoom).list(db=db, query=statement, params=page)
        for item in chatroom.items:
            users = db.query(Users).filter(Users.id == item.user_id).first()
            usecases = db.query(UseCases).filter(UseCases.id == item.use_case_id).first()
            item.avatar_url = users.avatar_url
            if users:
                item.user_name = users.full_name
                item.user_email = users.email
            else:
                item.user_name = None
                item.user_email = None

            if usecases:
                item.use_case_title = usecases.use_case_title
                item.ai_service = usecases.ai_service
                item.llms_model_name = usecases.llms_model_name
            else:
                item.use_case_title = None
                item.ai_service = None
                item.llms_model_name = None

            db_provider = db.query(Providers).filter(Providers.id == item.provider_id,
                                                     Providers.is_active == True,
                                                     Providers.is_deleted == False).first()
            if db_provider:
                item.llms_model = {
                    "id": db_provider.id,
                    "ai_service": db_provider.ai_service,
                    "service_name": db_provider.service_name,
                    "model_name": db_provider.model_name,
                    "llm_model_id": db_provider.llm_model_id
                }

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=chatroom)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_CHATROOM.endpoint_name,
    path=AccessEndpoint.UPDATE_CHATROOM.endpoint_path,
    response_model=DataResponse[ChatRoomResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def update_chatroom(chatroom_id: str, upd: UpdateChatRoom, db: Session = Depends(get_db),
                          language: Optional[str] = "jp") -> DataResponse:
    try:
        db_chatroom = db.query(ChatRoom).filter(ChatRoom.id == chatroom_id).first()
        if not db_chatroom:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_chatroom.is_active = True
        db_chatroom.title = upd.title
        db.commit()
        db.refresh(db_chatroom)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_chatroom)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_CHATROOM.endpoint_name,
    path=AccessEndpoint.DELETE_CHATROOM.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def delete_chatroom(chatroom_id: int, db: Session = Depends(get_db),
                          language: Optional[str] = "jp") -> DataResponse:
    try:
        db_chatroom = db.query(ChatRoom).filter(ChatRoom.id == chatroom_id).first()
        if not db_chatroom:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_chatroom.is_deleted = True
        db_chatroom.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_chatroom)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
