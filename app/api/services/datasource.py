import os
import traceback
import time
import asyncio
import locale
from collections import defaultdict
from datetime import timedelta, datetime
from google.cloud import storage, bigquery
from typing import Optional, List
from fastapi import APIRouter, Depends, Request, Query, UploadFile, File, status as http_status
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.config import settings
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, validate_user, AuthPermissionChecker
from app.helpers.endpoints import AccessEndpoint
from app.helpers.decorators import track_action
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.services import CreateDataSource, DataSourceResp, DatasourceFilter
from app.models.ai_services import DataSources, UseCases
from app.models.users import Users, UserGroup
from app.models.companies import Companies
from app.services.base import CRUDBase
from app.services.cloud_engine_service import DiscoveryEngineService, StorageService
from app.utils.common import get_message
from collections import defaultdict
from datetime import timedelta

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_DATASOURCE.endpoint_name,
    path=AccessEndpoint.CREATE_DATASOURCE.endpoint_path,
    response_model=DataResponse[DataSourceResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_datasource(
        datasource: CreateDataSource,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        # TODO: Check if datasource exist
        datasource_exist = db.query(exists().where(
            DataSources.title == datasource.title
        )).scalar()
        if datasource_exist:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("datasource_exist", language))

        # TODO: Create datasource
        db_datasource = DataSources(**datasource.dict())
        db.add(db_datasource)
        db.commit()
        db.refresh(db_datasource)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_datasource)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DATASOURCE.endpoint_name,
    path=AccessEndpoint.GET_DATASOURCE.endpoint_path,
    response_model=DataResponse[DataSourceResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_datasource(
        datasource_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_datasource = db.query(DataSources).filter(DataSources.id == datasource_id,
                                                     DataSources.is_deleted == is_deleted).first()
        if not db_datasource:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_datasource)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ALL_YOUR_DATASOURCE.endpoint_name,
    path=AccessEndpoint.GET_ALL_YOUR_DATASOURCE.endpoint_path,
    response_model=DataResponse[Page[DataSourceResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_your_datasource(
        request: Request,
        search: Optional[str] = Query(None, description="Search by Title Datasource"),
        filters: DatasourceFilter = Depends(),
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth = request.state.user.to_dict()
        user_id = auth.get('client_id')

        user_groups = db.query(UserGroup).filter(
            UserGroup.user_id.contains([user_id]),
            UserGroup.is_deleted == False,
            UserGroup.is_active == True
        ).all()

        use_case_ids = []
        for group in user_groups:
            if group.use_case:
                use_case_ids.extend(group.use_case)

        datasource_ids = db.query(UseCases.datasource_id).filter(
            UseCases.id.in_(use_case_ids),
            UseCases.is_deleted == False
        ).distinct().all()
        datasource_ids = [ds_id[0] for ds_id in datasource_ids]

        statement = db.query(DataSources).filter(
            DataSources.id.in_(datasource_ids),
            DataSources.is_active == filters.is_active,
            DataSources.is_deleted == filters.is_deleted
        )

        if search:
            statement = statement.filter(
                DataSources.title.ilike(f"%{search}%")
            )

        datasource = CRUDBase(DataSources).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datasource)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_DATASOURCES.endpoint_name,
    path=AccessEndpoint.GET_DATASOURCES.endpoint_path,
    response_model=DataResponse[Page[DataSourceResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_datasource(
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        filters: DatasourceFilter = Depends(),
        search: Optional[str] = Query(None, description="Search by Title Datasource"),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(DataSources).filter(
            DataSources.is_active == filters.is_active,
            DataSources.is_deleted == filters.is_deleted
        )

        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (DataSources.title.ilike(f"%{search}%"))
            )

        datasource = CRUDBase(DataSources).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datasource)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_DATASOURCE.endpoint_name,
    path=AccessEndpoint.UPDATE_DATASOURCE.endpoint_path,
    response_model=DataResponse[DataSourceResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_datasource(
        datasource_id: str,
        requestDataSource: CreateDataSource,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_datasource = db.query(DataSources).filter(DataSources.id == datasource_id).first()
        if not db_datasource:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        print("case 1", requestDataSource.list_group)
        # TODO Delete list_group when null
        if not requestDataSource.list_group:
            print("case 2", requestDataSource.list_group)
            db_datasource.list_group = []

        # TODO Delete group_id not in request
        else:
            print("case 3", requestDataSource.list_group)
            db_datasource.list_group = [list_gr for list_gr in db_datasource.list_group if
                                        list_gr in requestDataSource.list_group]

            # TODO Add group_id not in request
            for list_gr in requestDataSource.list_group:
                if list_gr not in db_datasource.list_group:
                    db_datasource.list_group.append(list_gr)

        db_datasource.title = requestDataSource.title
        db_datasource.description = requestDataSource.description
        db_datasource.provider_id = requestDataSource.provider_id
        db_datasource.retriever_type = requestDataSource.retriever_type
        db_datasource.ai_search_data_store_id = requestDataSource.ai_search_data_store_id
        db_datasource.engine_compute_id = requestDataSource.engine_compute_id
        db_datasource.max_document = requestDataSource.max_document
        db_datasource.max_extractive_answer_count = requestDataSource.max_extractive_answer_count
        db_datasource.ai_service = requestDataSource.ai_service
        db_datasource.llms_model = requestDataSource.llms_model
        db_datasource.is_deleted = False
        db_datasource.is_active = True
        db.commit()
        db.refresh(db_datasource)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_datasource)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_DATASOURCE.endpoint_name,
    path=AccessEndpoint.DELETE_DATASOURCE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_datasource(
        datasource_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_datasource = db.query(DataSources).filter(DataSources.id == datasource_id).first()
        if not db_datasource:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_datasource.is_deleted = True
        db_datasource.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_datasource)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ALL_DATASOURCE_FOR_USER.endpoint_name,
    path=AccessEndpoint.GET_ALL_DATASOURCE_FOR_USER.endpoint_path,
    response_model=DataResponse[Page[DataSourceResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_datasource_for_user(
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(DataSources).filter(DataSources.is_deleted == is_deleted)
        datasource = CRUDBase(DataSources).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datasource)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.GOOGLE_CREATE_DATASTORE.endpoint_name,
    path=AccessEndpoint.GOOGLE_CREATE_DATASTORE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def google_create_datastore(
        data_store_name: str,
        project_id: str,
        location: str = 'global',
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        data_store_id = f"{data_store_name}-{int(datetime.now().timestamp())}"
        discovery_service = DiscoveryEngineService()
        datastore = discovery_service.create_data_store(project_id=project_id, location=location,
                                                        data_store_name=data_store_name, data_store_id=data_store_id)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datastore)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ALL_GOOGLE_DATASTORE.endpoint_name,
    path=AccessEndpoint.GET_ALL_GOOGLE_DATASTORE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_google_datastore(project_id: str, location: str = 'global',
                                   language: Optional[str] = "jp") -> DataResponse:
    try:
        discovery_service = DiscoveryEngineService()
        datastore = discovery_service.list_data_stores(project_id=project_id, location=location)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=datastore)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_LIST_DOCUMENT_IN_DATASTORE.endpoint_name,
    path=AccessEndpoint.GET_LIST_DOCUMENT_IN_DATASTORE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_list_documents_in_datastore(
        datastore_id: str, project_id: str,
        location: str = 'global',
        page_size: int = 50,
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        discovery_service = DiscoveryEngineService()
        list_documents = discovery_service.list_documents(project_id=project_id, location=location,
                                                          data_store_id=datastore_id, page_size=page_size)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=list_documents)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_TREE_DOCUMENTS.endpoint_name,
    path=AccessEndpoint.GET_TREE_DOCUMENTS.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_tree_documents(
        bucket_name: str = "musashino-rag",
        prefix: str = "",
        max_depth: int = 5,
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        storage_service = StorageService()
        tree = storage_service.get_tree_documents(bucket_name=bucket_name, prefix=prefix, max_depth=max_depth)
        data = {f"{bucket_name}": tree}

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GOOGLE_DETAIL_DATASTORE_ID.endpoint_name,
    path=AccessEndpoint.GET_GOOGLE_DETAIL_DATASTORE_ID.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_google_detail_datastore_id(
        datastore_id: str, project_id: str,
        location: str = 'global',
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        discovery_service = DiscoveryEngineService()
        data_store = discovery_service.get_detail_data_store_id(project_id=project_id, location=location,
                                                                data_store_id=datastore_id)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data_store)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_GOOGLE_DETAIL_DATASTORE_ID_STATUS.endpoint_name,
    path=AccessEndpoint.GET_GOOGLE_DETAIL_DATASTORE_ID_STATUS.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_google_datastore_id_status(
        datastore_id: str, project_id: str,
        location: str = 'global',
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        discovery_service = DiscoveryEngineService()
        data_store_exists = discovery_service.check_data_store_exists(project_id=project_id, location=location,
                                                                      data_store_id=datastore_id)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data_store_exists)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.endpoint_name,
    path=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
@track_action(action_name=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.name)
async def generate_google_signed_url(
        request: Request,
        source_url: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()

        storage_service = StorageService()
        if source_url.startswith("gs://"):
            source_url = source_url[5:]

        bucket_name, blob_name = source_url.split('/', 1)
        doc_url = storage_service.generate_signed_url_with_access(user_email=user.email, bucket_name=bucket_name,
                                                                  blob_name=blob_name)
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=doc_url)

    except GatewayException:
        print(traceback.format_exc())
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL_V2.endpoint_name,
    path=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL_V2.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
@track_action(action_name=AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.name)
async def generate_google_signed_url_v2(
        request: Request,
        usecase_id: int,
        source_url: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()

        storage_service = StorageService()
        if source_url.startswith("gs://"):
            source_url = source_url[5:]

        bucket_name, blob_name = source_url.split('/', 1)
        doc_url = storage_service.generate_signed_url_with_access(user_email=user.email, bucket_name=bucket_name,
                                                                  blob_name=blob_name)
        client = bigquery.Client()

        table_id = settings.BIG_QUERY_LOGGING

        db_company = (
            db.query(Companies)
            .filter(
                Companies.id == user.company_id,
                Companies.is_deleted == False
            )
            .first()
        )
        db_usecase = db.query(UseCases).filter(
            UseCases.id == usecase_id,
            UseCases.is_deleted == False
        ).first()

        if not db_usecase:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        document_name = blob_name.split('/')[-1]
        rows_to_insert = [
            {"user_id": user.id, "company_id": user.company_id, "company_name": db_company.company_name,
             "use_case_title": db_usecase.use_case_title, "use_case_code": db_usecase.use_case_code,
             'document_name': document_name,
             "action_name": AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.name,
             "endpoint": AccessEndpoint.GENERATE_GOOGLE_SIGNED_URL.endpoint_path, "method": 'GET',
             "document_url": doc_url, "created_at": datetime.now().isoformat(), "email": user.email,
             "full_name": user.full_name},
        ]

        errors = client.insert_rows_json(table_id, rows_to_insert)
        if errors:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Error inserting rows into BigQuery: {errors}"
            )
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=doc_url)

    except GatewayException:
        print(traceback.format_exc())
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ALL_PROJECT_ID.endpoint_name,
    path=AccessEndpoint.GET_ALL_PROJECT_ID.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_project_id(
        language: Optional[str] = "jp") -> DataResponse:
    try:
        discovery_service = DiscoveryEngineService()
        data = discovery_service.list_project_id()
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.GOOGLE_APPLICATION_CREDENTIALS
client = storage.Client()


@router.post(
    name=AccessEndpoint.CREATE_FOLDER_IN_COMPANY_OF_BUCKET.endpoint_name,
    path=AccessEndpoint.CREATE_FOLDER_IN_COMPANY_OF_BUCKET.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
def google_cloud_create_folder_in_bucket(
        folder_name: str,
        path: str = "",
        bucket_name: str = "musashino-rag",
        root_folder: str = "customers",
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)

        if not bucket.exists():
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        if not bucket.iam_configuration.uniform_bucket_level_access_enabled:
            bucket.iam_configuration.uniform_bucket_level_access_enabled = True
            bucket.patch()

        if bucket.iam_configuration.public_access_prevention != 'enforced':
            bucket.iam_configuration.public_access_prevention = 'enforced'
            bucket.patch()

        full_path = f"{root_folder}/{path}/{folder_name}/".replace("//", "/")

        blob = bucket.blob(full_path)
        if blob.exists():
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("duplicate_folder", language)
            )

        blob.upload_from_string('')

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data=full_path
        )

    except GatewayException as e:
        print(traceback.format_exc())
        raise e
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.post(
    name=AccessEndpoint.DELETE_FOLDER_IN_COMPANY_OF_BUCKET.endpoint_name,
    path=AccessEndpoint.DELETE_FOLDER_IN_COMPANY_OF_BUCKET.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
# @track_action(action_name=AccessEndpoint.DELETE_FOLDER_IN_COMPANY_OF_BUCKET.name)
async def google_cloud_delete_file_or_folder(
        request: Request,
        company: int,
        slug: str,
        path: str,
        bucket_name: str = "musashino-rag",
        root_folder: str = "customers",
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        full_path = f"{root_folder}/COMPANY_{slug}/{path}"
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        client = bigquery.Client()

        table_id = settings.BIG_QUERY_LOGGING_FULL

        db_company = (
            db.query(Companies)
            .filter(
                Companies.id == user.company_id,
                Companies.is_deleted == False
            )
            .first()
        )
        rows_to_insert = [
            {"user_id": user.id, "company_id": user.company_id,
             "action_name": AccessEndpoint.DELETE_FOLDER_IN_COMPANY_OF_BUCKET.name,
             "endpoint": AccessEndpoint.DELETE_FOLDER_IN_COMPANY_OF_BUCKET.endpoint_path, "method": 'POST',"request_data": f"{[full_path]}",
             "response_data": f"{[user.email], [user.full_name]}",
             "language": language,
             "created_at": datetime.now().isoformat()},
        ]

        errors = client.insert_rows_json(table_id, rows_to_insert)
        if errors:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Error inserting rows into BigQuery: {errors}"
            )
        if not bucket.exists():
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        blobs = list(bucket.list_blobs(prefix=full_path))
        if not blobs:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        for blob in blobs:
            blob.delete()


        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data={"deleted_count": len(blobs)}
        )

    except GatewayException as e:
        print(traceback.format_exc())
        raise e
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.post(
    name=AccessEndpoint.UPLOAD_DOCUMENT_IN_FOLDER.endpoint_name,
    path=AccessEndpoint.UPLOAD_DOCUMENT_IN_FOLDER.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
@router.post(
    name=AccessEndpoint.UPLOAD_DOCUMENT_IN_FOLDER.endpoint_name,
    path=AccessEndpoint.UPLOAD_DOCUMENT_IN_FOLDER.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def google_cloud_upload_document(
        folder_name: str,
        files: List[UploadFile] = File(...),
        bucket_name: str = "musashino-rag",
        root_folder: str = "customers",
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)

        if not bucket.exists():
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )

        if not bucket.iam_configuration.uniform_bucket_level_access_enabled:
            bucket.iam_configuration.uniform_bucket_level_access_enabled = True
            bucket.patch()

        if bucket.iam_configuration.public_access_prevention != 'enforced':
            bucket.iam_configuration.public_access_prevention = 'enforced'
            bucket.patch()

        if not folder_name.endswith("/"):
            folder_name += "/"

        uploaded_files = []
        for file in files:
            timestamp = int(time.time())
            file_extension = os.path.splitext(file.filename)[1]
            file_name_without_ext = os.path.splitext(file.filename)[0]
            new_file_name = f"{file_name_without_ext}_{timestamp}{file_extension}"
            full_path = f"{root_folder}/{folder_name}{new_file_name}".strip("/")

            blob = bucket.blob(full_path)

            temp_file_path = f"/tmp/{file.filename}"
            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)

            blob.upload_from_filename(temp_file_path)
            os.remove(temp_file_path)

            uploaded_files.append(full_path)

        return DataResponse.success(
            statusCode=http_status.HTTP_200_OK,
            data={"uploaded_files": uploaded_files}
        )

    except GatewayException as e:
        print(traceback.format_exc())
        raise e
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


@router.get(
    name=AccessEndpoint.GET_LIST_FILE_IN_BUCKET.endpoint_name,
    path=AccessEndpoint.GET_LIST_FILE_IN_BUCKET.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def google_cloud_get_list_file(
        company: int,
        slug: str,
        search: Optional[str] = "",
        path: Optional[str] = "",
        bucket_name: str = "musashino",
        root_folder: str = "customers",
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        if not path:
            new_path = f"{root_folder}/COMPANY_{slug}/"
        else:
            new_path = f"{root_folder}/COMPANY_{slug}/{path}/"

        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blobs_iter = bucket.list_blobs(prefix=new_path, delimiter='/')

        child_items = []
        folder_sizes = defaultdict(int)

        for blob in blobs_iter:
            if not blob.name.endswith("/"):
                parent_folder = "/".join(blob.name.split("/")[:-1]) + "/"
                folder_sizes[parent_folder] += blob.size

                view_url = blob.generate_signed_url(
                    version="v4",
                    expiration=timedelta(hours=1),
                    method="GET"
                )
                download_url = blob.generate_signed_url(
                    version="v4",
                    expiration=timedelta(hours=1),
                    method="GET",
                    response_disposition="attachment"
                )
                child_items.append({
                    "name": blob.name,
                    "size": blob.size,
                    "content_type": blob.content_type,
                    "updated": blob.updated,
                    "is_directory": False,
                    "view_url": view_url,
                    "download_url": download_url
                })
        for prefix in blobs_iter.prefixes:
            total_size = await asyncio.to_thread(calculate_total_size, bucket, prefix)
            child_items.append({
                "name": prefix,
                "size": total_size,
                "content_type": "folder",
                "updated": None,
                "is_directory": True,
                "view_url": None,
                "download_url": None
            })
        current_folder_size = await asyncio.to_thread(calculate_total_size, bucket, new_path)
        current_folder_entry = {
            "name": new_path,
            "size": current_folder_size,
            "content_type": "folder",
            "updated": None,
            "is_directory": True,
            "view_url": None,
            "download_url": None
        }
        child_items.insert(0, current_folder_entry)

        try:
            locale.setlocale(locale.LC_COLLATE, "ja_JP.UTF-8")
            child_items = sorted(child_items, key=lambda x: locale.strxfrm(x['name']))
        except Exception as e:
            child_items = sorted(child_items, key=lambda x: x['name'])
        if search:
            current_folder = child_items[0]
            remaining_items = child_items[1:]
            filtered_items = [item for item in remaining_items if search.lower() in item['name'].lower()]
            child_items = [current_folder] + filtered_items
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=child_items)

    except GatewayException:
        print(traceback.format_exc())
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=get_message("bad_request", language)
        )


def calculate_total_size(bucket, prefix):
    total = 0
    for blob in bucket.list_blobs(prefix=prefix):
        if not blob.name.endswith("/"):
            total += blob.size
    return total
