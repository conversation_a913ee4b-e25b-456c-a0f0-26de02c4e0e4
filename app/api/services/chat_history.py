import traceback
from datetime import datetime
from typing import Optional
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Request, status as http_status
from sqlalchemy.orm import Session
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_admin, validate_user, Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.helpers.endpoints import AccessEndpoint
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.services import CreateChatHistory, ChatHistoryResp, CreateReactionForChat, ChatHistoryRespWithReaction
from app.models.ai_services import ChatHistory
from app.models.users import Users
from app.services.base import CRUDBase
from app.utils.common import get_message

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_CHAT_HISTORY.endpoint_name,
    path=AccessEndpoint.CREATE_CHAT_HISTORY.endpoint_path,
    response_model=DataResponse[ChatHistoryResp],
    include_in_schema=True,
    dependencies=[Depends(Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(validate_admin))]
)
async def create_chat_history(
        chat_history: CreateChatHistory,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_chat_history = ChatHistory(**chat_history.dict())
        db.add(db_chat_history)
        db.commit()
        db.refresh(db_chat_history)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_chat_history)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CHAT_HISTORY.endpoint_name,
    path=AccessEndpoint.GET_CHAT_HISTORY.endpoint_path,
    response_model=DataResponse[ChatHistoryRespWithReaction],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_chat_history(
        chat_hist_id: str,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_chat_hist = db.query(ChatHistory).filter(ChatHistory.id == chat_hist_id,
                                                    ChatHistory.is_deleted == is_deleted).first()
        # list_reaction = db.query(
        #     ChatHistoryReaction.reaction_type,
        #     func.count(ChatHistoryReaction.reaction_type).label('count')
        # ).filter(
        #     ChatHistoryReaction.user_id == db_chat_hist.user_id,
        #     ChatHistoryReaction.chatroom_id == db_chat_hist.chatroom_id,
        #     ChatHistoryReaction.message_id == db_chat_hist.id,
        #     ChatHistoryReaction.use_case_id == db_chat_hist.use_case_id
        # ).group_by(ChatHistoryReaction.reaction_type).all()

        # reactions = [{"reaction_type": reaction_type, "count": count} for reaction_type, count in list_reaction]
        response_data = ChatHistoryRespWithReaction(
            id=db_chat_hist.id,
            user_id=db_chat_hist.user_id,
            chatroom_id=db_chat_hist.chatroom_id,
            message_id=db_chat_hist.message_id,
            chat_id=db_chat_hist.chat_id,
            session_id=db_chat_hist.session_id,
            service_name=db_chat_hist.service_name,
            llm_model_id=db_chat_hist.llm_model_id,
            use_case_id=db_chat_hist.use_case_id,
            user_message=db_chat_hist.user_message,
            ai_response=db_chat_hist.ai_response,
            language=db_chat_hist.language,
            sentiment=db_chat_hist.sentiment,
            input_length=db_chat_hist.input_length,
            output_length=db_chat_hist.output_length,
            input_tokens=db_chat_hist.input_tokens,
            output_tokens=db_chat_hist.output_tokens,
            response_time_ms=db_chat_hist.response_time_ms,
            reactions=db_chat_hist.reactions,
            is_resolved=db_chat_hist.is_resolved,
            source_info=db_chat_hist.source_info,
            has_images=db_chat_hist.has_images,
            has_files=db_chat_hist.has_files,
            image_list=db_chat_hist.image_list,
            file_list=db_chat_hist.file_list,
            references=db_chat_hist.references,
            is_deleted=db_chat_hist.is_deleted,
            deleted_at=db_chat_hist.deleted_at,
            is_active=db_chat_hist.is_active,
            created_at=db_chat_hist.created_at,
            updated_at=db_chat_hist.updated_at
        )
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=response_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_YOUR_CHAT_HISTORIES.endpoint_name,
    path=AccessEndpoint.GET_YOUR_CHAT_HISTORIES.endpoint_path,
    response_model=DataResponse[Page[ChatHistoryResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_your_chat_history(
        request: Request,
        page: PaginationParams = Depends(),
        chatroom_id: Optional[int] = None,
        is_deleted: bool = False, db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth = request.state.user.to_dict()
        client_id = auth.get('client_id')
        statement = db.query(ChatHistory).filter(
            ChatHistory.user_id == client_id,
            ChatHistory.is_active == True,
            ChatHistory.is_deleted == is_deleted
        )

        if chatroom_id is not None:
            statement = statement.filter(ChatHistory.chatroom_id == chatroom_id)
        chat_hists = CRUDBase(ChatHistory).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=chat_hists)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_CHAT_HISTORIES.endpoint_name,
    path=AccessEndpoint.GET_CHAT_HISTORIES.endpoint_path,
    response_model=DataResponse[Page[ChatHistoryResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_chat_history(
        page: PaginationParams = Depends(),
        chatroom_id: Optional[int] = None,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        statement = db.query(ChatHistory).filter(ChatHistory.is_deleted == is_deleted)
        if chatroom_id is not None:
            statement = statement.filter(ChatHistory.chatroom_id == chatroom_id)
        chat_hists = CRUDBase(ChatHistory).list(db=db, query=statement, params=page)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=chat_hists)

    except GatewayException:
        raise
    except Exception:
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_CHAT_HISTORY.endpoint_name,
    path=AccessEndpoint.UPDATE_CHAT_HISTORY.endpoint_path,
    response_model=DataResponse[ChatHistoryResp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def update_chat_hist(
        chatroom_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_chat_hist = db.query(ChatHistory).filter(ChatHistory.id == chatroom_id).first()
        if not db_chat_hist:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_chat_hist.is_active = True
        db.commit()
        db.refresh(db_chat_hist)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_chat_hist)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_CHAT_HISTORY.endpoint_name,
    path=AccessEndpoint.DELETE_CHAT_HISTORY.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_chat_hist(
        chat_hist_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_chat_hist = db.query(ChatHistory).filter(ChatHistory.id == chat_hist_id).first()
        if not db_chat_hist:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        db_chat_hist.is_deleted = True
        db_chat_hist.deleted_at = datetime.now()
        db.commit()
        db.refresh(db_chat_hist)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.post(
    name=AccessEndpoint.REACTION_FOR_CHAT.endpoint_name,
    path=AccessEndpoint.REACTION_FOR_CHAT.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def create_reaction_for_chat(
        chat_history_reaction: CreateReactionForChat,
        request: Request,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = request.state.user.to_dict()
        user_id = auth_info.get('client_id')
        if not user_id:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("something_wrong", language))

        user = db.query(Users).filter(Users.id == user_id, Users.is_active == True).first()
        if not user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))

        db_chat_hist = db.query(ChatHistory).filter(ChatHistory.id == chat_history_reaction.chat_hist_id,
                                                    ChatHistory.is_deleted == False,
                                                    ChatHistory.is_active == True).first()
        reaction = db_chat_hist.reactions
        if chat_history_reaction.reaction_type == reaction:
            db_chat_hist.reactions = None
        else:
            db_chat_hist.reactions = chat_history_reaction.reaction_type
        db_chat_hist.user_id = user_id
        db.commit()
        db.refresh(db_chat_hist)
        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
