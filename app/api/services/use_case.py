import traceback
from datetime import datetime
from typing import List, Optional
import csv
from io import String<PERSON>
from fastapi import APIRouter, Depends, Request, status as http_status
from fastapi import Query
from fastapi.responses import Response
from sqlalchemy.orm import Session
from sqlalchemy.sql import exists
from app.helpers.paging import Page, PaginationParams
from app.helpers.exceptions import GatewayException
from app.helpers.security import validate_user, AuthPermission<PERSON><PERSON><PERSON>, validate_admin
from app.db.base import get_db
from app.schemas.base import DataResponse
from app.schemas.services import CreateUseCase, UseCaseResp, UsecaseFilter, UseCaseV2Resp, UpdateUseCase, UseCaseRespV2, \
    UseCaseRespV3
from app.models.ai_services import UseCases, GroupUseCase, ChatRoom, ChatHistory
from app.models.users import UserGroup, Users, Group
from app.services.base import CRUDBase
from app.utils.common import generate_id_code, get_message
from app.helpers.endpoints import AccessEndpoint
from fastapi.encoders import jsonable_encoder
from app.models.ai_services import Providers

router = APIRouter()


@router.post(
    name=AccessEndpoint.CREATE_USECASE.endpoint_name,
    path=AccessEndpoint.CREATE_USECASE.endpoint_path,
    response_model=DataResponse[UseCaseRespV2],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def create_usecase(
        company: int,
        request: Request,
        usecase: CreateUseCase,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        if not user:
            return DataResponse().errors(statusCode=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                         error=[get_message("account_incorrect", language)])
        is_usecase_exists = db.query(
            exists().where(UseCases.use_case_title == usecase.use_case_title, UseCases.company_id == company,
                           UseCases.is_deleted == False)).scalar()
        if is_usecase_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("usecase_exist", language))

        db_usecase = UseCases(**usecase.dict(exclude={"list_group_id", "use_case_code"}))
        db_usecase.use_case_code = generate_id_code(length=20)
        db_usecase.created_at = datetime.now()
        db_usecase.updated_at = datetime.now()
        db_usecase.company_id = user.company_id
        db_usecase.created_by = user.id
        db_usecase.updated_by = user.id
        db.add(db_usecase)
        db.commit()
        db.refresh(db_usecase)

        if usecase.list_group_id:
            for group_id in usecase.list_group_id:
                db_usecase.list_group_id = usecase.list_group_id
                db_group = GroupUseCase(use_case_id=db_usecase.id, group_id=group_id)
                db.add(db_group)
                db.commit()
                db.refresh(db_group)

        return DataResponse.success(statusCode=http_status.HTTP_200_OK, data=db_usecase)

    except GatewayException:
        raise

    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USECASE.endpoint_name,
    path=AccessEndpoint.GET_USECASE.endpoint_path,
    response_model=DataResponse[UseCaseRespV3],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_usecase(
        usecase_id: int,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        db_usecase = db.query(UseCases).filter(
            UseCases.id == usecase_id,
            UseCases.is_deleted == is_deleted
        ).first()

        if not db_usecase:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        group_use_case = db.query(Group).join(GroupUseCase, Group.id == GroupUseCase.group_id).filter(
            GroupUseCase.use_case_id == usecase_id, GroupUseCase.is_active == True).all()
        # group_use_case = db.query(GroupUseCase).filter(GroupUseCase.use_case_id == usecase_id,
        #                                                GroupUseCase.is_active == True).all()
        # if group_use_case:
        #     db_usecase.list_group_id = [group.group_id for group in group_use_case]
        db_usecase.list_group_id = group_use_case
        list_models = []
        if db_usecase.list_provider_ids:
            list_models = db.query(Providers).filter(Providers.is_deleted == False, Providers.is_active == True,
                                                     Providers.id.in_(db_usecase.list_provider_ids)).all()
        db_usecase.list_models = [jsonable_encoder(model) for model in list_models]
        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_usecase)
    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_USECASES.endpoint_name,
    path=AccessEndpoint.GET_USECASES.endpoint_path,
    response_model=DataResponse[Page[UseCaseResp]],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def get_all_usecase(
        request: Request,
        is_deleted: bool = False,
        page: PaginationParams = Depends(),
        db: Session = Depends(get_db),
        language: Optional[str] = "jp",
        search: Optional[str] = Query(None, description="Search by Use case title"),
        filters: UsecaseFilter = Depends(),
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        if search and all(char in "%_" for char in search):
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=get_message("no_results_found", language)
            )
        statement = db.query(UseCases).filter(
            UseCases.company_id == user.company_id,
            UseCases.is_deleted == is_deleted
        )
        if filters.is_active:
            if filters.is_active == "true" or filters.is_active == "True":
                statement = statement.filter(UseCases.is_active == True)
            elif filters.is_active == "false" or filters.is_active == "False":
                statement = statement.filter(UseCases.is_active == False)
            else:
                statement = statement
        if filters.data_store_id:
            statement = statement.filter(UseCases.data_store_id == filters.data_store_id)
        if filters.group_name:
            statement = statement.join(GroupUseCase, GroupUseCase.use_case_id == UseCases.id).join(
                Group, Group.id == GroupUseCase.group_id).filter(Group.name == filters.group_name)
        if search:
            search = search.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
            statement = statement.filter(
                (UseCases.use_case_title.ilike(f"%{search}%"))
            )

        data = CRUDBase(UseCases).list(db=db, query=statement.order_by(UseCases.priority.asc()), params=page)
        for use_case in data.items:
            user_created = db.query(Users).filter(Users.id == use_case.created_by,
                                          Users.is_deleted == False).first()
            use_case.created_by_full_name = user_created.full_name if user_created else None
            use_case.created_by_email = user_created.email if user_created else None
            user_updated = db.query(Users).filter(Users.id == use_case.updated_by,
                                          Users.is_deleted == False).first()
            use_case.updated_by_full_name = user_updated.full_name if user_updated else None
            use_case.updated_by_email = user_updated.email if user_updated else None
        for groups in statement:
            group_use_case = db.query(GroupUseCase).filter(GroupUseCase.use_case_id == groups.id,
                                                           GroupUseCase.is_active == True).all()
            if group_use_case:
                groups.list_group_id = [group.group_id for group in group_use_case]
            list_models = []

            if groups.list_provider_ids:
                list_models = db.query(Providers).filter(Providers.is_deleted == False, Providers.is_active == True,
                                                         Providers.id.in_(groups.list_provider_ids)).all()
            groups.list_models = [jsonable_encoder(model) for model in list_models]

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_USECASE.endpoint_name,
    path=AccessEndpoint.UPDATE_USECASE.endpoint_path,
    response_model=DataResponse[UseCaseRespV2],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_usecase(
        request: Request,
        company: int,
        usecase_id: int,
        usecase: UpdateUseCase,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        db_use_case = db.query(UseCases).filter(UseCases.id == usecase_id, UseCases.company_id == company,
                                                UseCases.is_deleted == False).first()
        if not db_use_case:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        # TODO: Check if use case title already exists but not the current use case
        is_usecase_title_exists = db.query(exists().where(
            UseCases.use_case_title == usecase.use_case_title,
            UseCases.id != usecase_id,
            UseCases.company_id == company,
            UseCases.is_deleted == False
        )).scalar()
        if is_usecase_title_exists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("usecase_title_exist", language))

        db_use_case.use_case_title = usecase.use_case_title
        db_use_case.use_case_private = usecase.use_case_private
        db_use_case.description = usecase.description
        db_use_case.provider_id = usecase.provider_id
        db_use_case.project_id = usecase.project_id
        db_use_case.location = usecase.location
        db_use_case.engine_id = usecase.engine_id
        db_use_case.data_store_id = usecase.data_store_id
        db_use_case.max_documents = usecase.max_documents
        db_use_case.max_extractive_answer_count = usecase.max_extractive_answer_count
        db_use_case.get_extractive_answers = usecase.get_extractive_answers
        db_use_case.engine_data_type = usecase.engine_data_type
        db_use_case.ai_service = usecase.ai_service
        db_use_case.llms_model_name = usecase.llms_model_name
        db_use_case.llms_model_version = usecase.llms_model_version
        db_use_case.system_prompt = usecase.system_prompt
        db_use_case.chain_prompt = usecase.chain_prompt
        db_use_case.answer_language_code = usecase.answer_language_code
        db_use_case.chain_of_thought_prompt = usecase.chain_of_thought_prompt
        db_use_case.search_type = usecase.search_type
        db_use_case.can_multimodal = usecase.can_multimodal
        db_use_case.is_multimodal = usecase.is_multimodal
        db_use_case.multimodal_extensions = usecase.multimodal_extensions
        db_use_case.retriever_type = usecase.retriever_type
        db_use_case.result_count = usecase.result_count
        db_use_case.related_questions = usecase.related_questions
        db_use_case.snippets_or_extractive = usecase.snippets_or_extractive
        db_use_case.autocomplete_suggestions = usecase.autocomplete_suggestions
        db_use_case.feedback = usecase.feedback
        db_use_case.human_prompt = usecase.human_prompt
        db_use_case.format_output = usecase.format_output
        db_use_case.use_ragsource = usecase.use_ragsource
        db_use_case.priority = usecase.priority
        db_use_case.is_active = usecase.is_active
        db_use_case.list_provider_ids = usecase.list_provider_ids
        db_use_case.updated_by = user.id
        db_use_case.is_public = usecase.is_public
        db_use_case.updated_at = datetime.now()
        db.commit()
        db.refresh(db_use_case)

        if usecase.list_group_id:
            existing_group_use_cases = db.query(GroupUseCase).filter(GroupUseCase.use_case_id == usecase_id).all()
            existing_group_ids = {group_use_case.group_id for group_use_case in existing_group_use_cases}

            new_group_ids = set(usecase.list_group_id)

            # TODO: soft delete group_use_case entries
            for group_use_case in existing_group_use_cases:
                if group_use_case.group_id not in new_group_ids:
                    # group_use_case.is_active = False
                    # group_use_case.is_deleted = True
                    db.delete(group_use_case)
                    db.commit()

            # TODO: Add new group_use_case entries
            for group_id in new_group_ids - existing_group_ids:
                db_group = GroupUseCase(use_case_id=usecase_id, group_id=group_id)
                db.add(db_group)
                db.commit()
                db.refresh(db_group)
        else:
            existing_group_use_cases = db.query(GroupUseCase).filter(GroupUseCase.use_case_id == usecase_id).all()
            for group_use_case in existing_group_use_cases:
                db.delete(group_use_case)
                db.commit()

        db_use_case.list_group_id = usecase.list_group_id

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=db_use_case)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.delete(
    name=AccessEndpoint.DELETE_USECASE.endpoint_name,
    path=AccessEndpoint.DELETE_USECASE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def delete_usecase(
        request: Request,
        usecase_id: str,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        db_use_case = db.query(UseCases).filter(UseCases.id == usecase_id).first()
        if not db_use_case:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))

        # TODO: soft delete group_use_case entries
        group_use_case = db.query(GroupUseCase).filter(GroupUseCase.use_case_id == usecase_id).all()
        if group_use_case:
            for group in group_use_case:
                db.delete(group)
                db.commit()
        db_use_case.is_deleted = True
        db_use_case.deleted_at = datetime.now()
        db_use_case.updated_by = user.id
        db.commit()
        db.refresh(db_use_case)

        chatroom = db.query(ChatRoom).filter(ChatRoom.use_case_id == usecase_id).all()
        for chat in chatroom:
            chat.is_active = False
            chat.is_deleted = True
            db.commit()

        chat_history = db.query(ChatHistory).filter(ChatHistory.use_case_id == usecase_id).all()
        for history in chat_history:
            history.is_active = False
            history.is_deleted = True
            db.commit()

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.GET_ALL_USECASE_FOR_USER.endpoint_name,
    path=AccessEndpoint.GET_ALL_USECASE_FOR_USER.endpoint_path,
    response_model=DataResponse[UseCaseV2Resp],
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def get_all_usecase_for_user(
        request: Request,
        is_deleted: bool = False,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    global list_use_case
    try:
        auth_info = request.state.user.to_dict()
        uid = auth_info["uid"]
        if not uid:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("something_wrong", language))
        # Get user
        user_db = db.query(Users).filter(Users.uid == uid, Users.is_active == True).first()

        list_user_groups = db.query(UserGroup).filter(UserGroup.user_id == user_db.id).all()
        use_cases = []
        list_models = []
        if list_user_groups:
            for user_group in list_user_groups:
                group_data = db.query(Group).filter(Group.id == user_group.group_id).first()
                group_usecase = db.query(GroupUseCase).filter(GroupUseCase.group_id == group_data.id).all()
                if group_usecase:
                    for group in group_usecase:
                        use_case = db.query(UseCases).filter(
                            (UseCases.is_public == True) |
                            (
                                    (UseCases.is_public == False) &
                                    (UseCases.id == group.use_case_id)
                            ),
                            UseCases.company_id == user_db.company_id,
                            UseCases.is_deleted == False,
                            UseCases.is_active == True
                        ).order_by(UseCases.priority.asc()).all()
                        for u in use_case:
                            if u.list_provider_ids:
                                list_models = db.query(Providers).filter(Providers.is_deleted == False,
                                                                         Providers.is_active == True,
                                                                         Providers.id.in_(
                                                                             u.list_provider_ids)).all()
                            u.list_models = [jsonable_encoder(model) for model in list_models]
                            use_cases.append(UseCaseResp(**u.__dict__))
                else:
                    use_case = db.query(UseCases).filter(
                        UseCases.is_public == True,
                        UseCases.company_id == user_db.company_id,
                        UseCases.is_deleted == False,
                        UseCases.is_active == True
                    ).order_by(UseCases.priority.asc()).all()
                    for u in use_case:
                        if u.list_provider_ids:
                            list_models = db.query(Providers).filter(Providers.is_deleted == False,
                                                                     Providers.is_active == True,
                                                                     Providers.id.in_(
                                                                         u.list_provider_ids)).all()
                        u.list_models = [jsonable_encoder(model) for model in list_models]
                        use_cases.append(UseCaseResp(**u.__dict__))
        else:
            use_case = db.query(UseCases).filter(
                UseCases.is_public == True,
                UseCases.company_id == user_db.company_id,
                UseCases.is_deleted == False,
                UseCases.is_active == True
            ).order_by(UseCases.priority.asc()).all()
            for u in use_case:
                if u.list_provider_ids:
                    list_models = db.query(Providers).filter(Providers.is_deleted == False,
                                                             Providers.is_active == True,
                                                             Providers.id.in_(
                                                                 u.list_provider_ids)).all()
                u.list_models = [jsonable_encoder(model) for model in list_models]
                use_cases.append(UseCaseResp(**u.__dict__))
        unique_use_cases = []
        seen_use_case_ids = set()

        for use_case in use_cases:
            if use_case.id not in seen_use_case_ids:
                unique_use_cases.append(use_case)
                seen_use_case_ids.add(use_case.id)

        unique_use_cases = sorted(unique_use_cases, key=lambda uc: uc.priority)

        response_data = UseCaseV2Resp(list_use_case=unique_use_cases)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=response_data)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.put(
    name=AccessEndpoint.UPDATE_STATUS_USECASE.endpoint_name,
    path=AccessEndpoint.UPDATE_STATUS_USECASE.endpoint_path,
    response_model=DataResponse,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_admin))]
)
async def update_status_usecase(
        request: Request,
        usecase_id: int,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
) -> DataResponse:
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        db_use_case = db.query(UseCases).filter(UseCases.id == usecase_id).first()
        if not db_use_case:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        status = False
        if db_use_case.is_active == False:
            status = True
        elif db_use_case.is_active == True:
            status = False

        db_use_case.is_active = status
        db_use_case.updated_at = datetime.now()
        db_use_case.updated_by = user.id
        db.commit()
        db.refresh(db_use_case)

        return DataResponse().success(statusCode=http_status.HTTP_200_OK, data=None)

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))


@router.get(
    name=AccessEndpoint.EXPORT_USECASE_CSV.endpoint_name,
    path=AccessEndpoint.EXPORT_USECASE_CSV.endpoint_path,
    include_in_schema=True,
    dependencies=[Depends(AuthPermissionChecker(validate_user))]
)
async def export_usecase_csv(
        request: Request,
        db: Session = Depends(get_db),
        language: Optional[str] = "jp"
):
    try:
        auth_info = jsonable_encoder(request.state.user)
        user: Users = db.query(Users).filter(Users.id == auth_info["client_id"], Users.is_active == True,
                                             Users.is_deleted == False).first()
        
        if not user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("no_results_found", language))
        
        # Get all use cases for this company
        usecases = db.query(UseCases).filter(
            UseCases.company_id == user.company_id,
            UseCases.is_deleted == False
        ).order_by(UseCases.priority.asc()).all()
        
        # Create CSV content
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header with Japanese/English columns
        # headers = [
        #     "Title",
        #     "Giải thích",
        #     "Mức độ ưu tiên", 
        #     "System prompt",
        #     "Định dạng đầu ra / Output format",
        #     "Chain-of-thought prompt",
        #     "Mô hình ngôn ngữ quy mô lớn (LLM)"
        # ]
        headers = [
            "タイトル",
            "説明",
            "優先度",
            "システムプロンプト",
            "出力形式",
            "連鎖思考プロンプト",
            "大規模言語モデル" 
        ]
        writer.writerow(headers)
        
        # Write data rows
        for usecase in usecases:
            row = [
                usecase.use_case_title or "",
                usecase.description or "",
                usecase.priority or "",
                usecase.system_prompt or "",
                usecase.format_output or "",
                usecase.chain_prompt or "",
                usecase.llms_model_name or ""
            ]
            writer.writerow(row)
        
        csv_content = output.getvalue()
        output.close()
        
        # Return CSV as response with proper headers for download
        headers = {
            'Content-Disposition': 'attachment; filename="usecase_export.csv"',
            'Content-Type': 'text/csv; charset=utf-8'
        }
        
        return Response(
            content=csv_content.encode('utf-8-sig'),  # Use UTF-8 BOM for Excel compatibility
            media_type='text/csv',
            headers=headers
        )

    except GatewayException:
        raise
    except Exception:
        print(traceback.format_exc())
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("bad_request", language))
