import os
import openpyxl
import uuid
import csv
import io
from google.api_core.exceptions import NotFound
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from app.core.cloud_engine.zoho import ZohoPlatform
from openpyxl.utils import get_column_letter
from openpyxl.styles import Ali<PERSON><PERSON>, Pat<PERSON>Fill, Font
from email_validator import validate_email, EmailNotValidError
from app.helpers.security import hash_password, check_password_strength
from datetime import datetime
from app.utils.common import get_message
from app.models.users import Users, Role, UserRole, Group, UserGroup
from sqlalchemy.orm import Session
from app.schemas.base import DataResponse
from sqlalchemy import or_, and_
from app.models.service_packages import ServicePackage
from app.models.company_subscriptions import CompanySubscriptions
from app.models.companies import Companies
import re
from sqlalchemy import case


class UserService(ZohoPlatform):

    def list_user_from_zoho(self):
        try:
            list_user = self.get_list_users_zoho
            return list_user
        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"Users not found.")


# def template_create_users(language, company_id):
#     try:
#         if language == "jp":
#             template_path = "templates/template_import_user_with_role_group_jp.xlsx"
#         else:
#             template_path = "templates/template_import_user_with_role_group_en.xlsx"
#
#         # Mở file template
#         workbook = openpyxl.load_workbook(template_path)
#         sheet = workbook.active
#
#         data = [
#             ["DEMO", "<EMAIL>", "T123456aA@", "○"]
#         ]
#
#         for i, row in enumerate(data, start=3):
#             sheet.cell(row=i, column=1, value=row[0])
#             sheet.cell(row=i, column=2, value=row[1])
#             sheet.cell(row=i, column=3, value=row[2])
#             sheet.cell(row=i, column=4, value=row[3])
#
#         # Ensure the directory exists
#         os.makedirs("uploads", exist_ok=True)
#
#         # Save the file to the server
#         now = datetime.now().strftime("%Y%m%d%H%M%S")
#         if language == "jp":
#             file_path = os.path.join("uploads", f"{now}_{company_id}_スタッフ追加.xlsx")
#         else:
#             file_path = os.path.join("uploads", f"{now}_{company_id}_ins_staff.xlsx")
#         workbook.save(file_path)
#
#         return file_path
#     except NotFound:
#         raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
#                                detail=f"Users not found.")


def template_update_users(userUpdateExcel, language, db: Session, company_id):
    try:
        if language == "jp":
            template_path = "templates/template_update_user_jp.xlsx"
        else:
            template_path = "templates/template_update_user_en.xlsx"

        workbook = openpyxl.load_workbook(template_path)
        sheet = workbook.active

        if userUpdateExcel.list_user is None:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=get_message("something_wrong", language))

        for user_id in userUpdateExcel.list_user:
            db_user = db.query(Users).filter(Users.id == user_id).first()
            if not db_user:
                raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                       detail=get_message("user_not_found", language))
        # Add warning text
        if language == "jp":
            warning_text1 = "※2行目までは削除しないでください"
            warning_text2 = "※グレーのセルは変更しても反映されません"
        else:
            warning_text1 = "※Do not delete the first two rows"
            warning_text2 = "※Gray cells will not be updated even if you change them"

        roles = []
        role_db = db.query(Role).filter(
            Role.is_deleted == False,
            Role.is_active == True,
            or_(
                Role.company_id == 1,
                and_(Role.name == 'Admin', Role.important == True),
                and_(Role.name == 'User', Role.important == True)
            )
        ).all()
        for role in role_db:
            roles.append(role.name)
        groups = []
        group_db = db.query(Group).filter(Group.is_deleted == False, Group.is_active == True,
                                          Group.company_id == company_id).all()
        for group in group_db:
            groups.append(group.name)

        users = []
        order = case(
            {user_id: index for index, user_id in enumerate(userUpdateExcel.list_user)},
            value=Users.id
        )

        user_db = db.query(Users).filter(
            Users.is_deleted == False,
            Users.is_musashino == False,
            Users.company_id == company_id,
            Users.id.in_(userUpdateExcel.list_user)
        ).order_by(order).all()
        # user_db = db.query(Users).filter(
        #     Users.is_deleted == False,
        #     # Users.is_active == True,
        #     Users.is_musashino == False,
        #     Users.company_id == company_id,
        #     Users.id.in_(userUpdateExcel.list_user)
        # ).all()
        for user in user_db:
            user_roles = db.query(Role).join(UserRole, UserRole.role_id == Role.id).join(Users,
                                                                                         Users.id == UserRole.user_id).filter(
                Role.is_active == True, Role.is_deleted == False, Role.company_id == company_id,
                UserRole.user_id == user.id).all()
            user_groups = db.query(Group).join(UserGroup, UserGroup.group_id == Group.id).join(Users,
                                                                                               Users.id == UserGroup.user_id).filter(
                Group.is_active == True, Group.is_deleted == False, Group.company_id == company_id,
                UserGroup.user_id == user.id).all()

            role_user = [role.name for role in user_roles]

            # Extract group names from the user_groups query
            group_user = [group.name for group in user_groups]

            # Add the user data to the users list in the specified format
            users.append([user.full_name, user.email, role_user, group_user])
        for i, row in enumerate(users, start=3):
            # sheet.cell(row=i, column=1, value=i - 2)
            sheet.cell(row=i, column=1, value=row[0])
            sheet.cell(row=i, column=2, value=row[1])
        #
        # if language == "jp":
        #     sheet2 = workbook.create_sheet(title="ユーザーの役割")
        #     sheet3 = workbook.create_sheet(title="ユーザーのグループ")
        # else:
        #     sheet2 = workbook.create_sheet(title="Users Role")
        #     sheet3 = workbook.create_sheet(title="Users Group")
        #
        # border_color = "31869b"
        # header_fill = PatternFill(start_color=border_color, end_color=border_color, fill_type="solid")
        # white_font = Font(color="FFFFFF")
        #
        # gray_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
        #
        # # Headers for sheet
        # # TODO: GET DATA OF sheet 2
        # # Headers for sheet 2
        # if language == "jp":
        #     sheet2["A1"] = "番号"
        #     sheet2["B1"] = "名前"
        #     sheet2["C1"] = "メール"
        # else:
        #     sheet2["A1"] = "N°"
        #     sheet2["B1"] = "FULL NAME"
        #     sheet2["C1"] = "EMAIL"
        #
        # for idx, role in enumerate(roles, start=4):  # Start from column 4 to accommodate the new "No" column
        #     sheet2.cell(row=1, column=idx, value=role)  # Add role to header
        #     sheet2.cell(row=1, column=idx).alignment = Alignment(horizontal="center")
        #     sheet2.cell(row=1, column=idx).fill = header_fill
        #     sheet2.cell(row=1, column=idx).font = white_font
        #
        # sheet2["A1"].alignment = Alignment(horizontal="center")
        # sheet2["A1"].fill = header_fill
        # sheet2["A1"].font = white_font
        #
        # sheet2["B1"].alignment = Alignment(horizontal="center")
        # sheet2["B1"].fill = header_fill
        # sheet2["B1"].font = white_font
        #
        # sheet2["C1"].alignment = Alignment(horizontal="center")
        # sheet2["C1"].fill = header_fill
        # sheet2["C1"].font = white_font
        #
        # for cell in sheet2[2]:
        #     cell.alignment = Alignment(horizontal="center")
        #     cell.fill = header_fill
        #     cell.font = white_font
        #
        # custom_header_column = len(roles) + 4  # Adjust for the new "No" column
        # # Add warning_text1 to row 1
        # sheet2.cell(row=1, column=custom_header_column, value=warning_text1)
        # sheet2.cell(row=1, column=custom_header_column).alignment = Alignment(horizontal="left")
        #
        # # Add warning_text2 to row 2
        # sheet2.cell(row=2, column=custom_header_column, value=warning_text2)
        # sheet2.cell(row=2, column=custom_header_column).alignment = Alignment(horizontal="left")
        # sheet2.column_dimensions[get_column_letter(custom_header_column)].width = 60
        #
        # for i, user in enumerate(users, start=3):  # Start from row 3 to accommodate the title rows
        #     sheet2.cell(row=i, column=1, value=i - 2)  # Serial number
        #     sheet2.cell(row=i, column=2, value=user[0])  # FULL NAME
        #     sheet2.cell(row=i, column=3, value=user[1])  # EMAIL
        #
        #     sheet2.cell(row=i, column=1, value=i - 2).fill = gray_fill  # No
        #     sheet2.cell(row=i, column=2, value=user[0]).fill = gray_fill  # FULL NAME
        #     sheet2.cell(row=i, column=3, value=user[1]).fill = gray_fill  # EMAIL
        #     for j, role in enumerate(roles, start=4):  # Start from column 4 to accommodate the new "No" column
        #         if language == "jp":
        #             checkbox_value = "○" if role in user[2] else ""
        #         else:
        #             checkbox_value = "○" if role in user[2] else ""
        #         cell = sheet2.cell(row=i, column=j, value=checkbox_value)
        #         cell.alignment = Alignment(horizontal="center")
        #
        #     # Set column widths for sheet2
        #     sheet2.column_dimensions["A"].width = 5  # Set width for No column
        #     sheet2.column_dimensions["B"].width = 25  # Set width for FULL NAME column
        #     sheet2.column_dimensions["C"].width = 25  # Set width for EMAIL column
        #     for idx in range(4, len(roles) + 4):  # Adjust for the new "No" column
        #         sheet2.column_dimensions[get_column_letter(idx)].width = 15  # Set width for role columns
        #     sheet2.column_dimensions[
        #         get_column_letter(custom_header_column)].width = 40  # Set width for the warning column
        #
        # # Headers for sheet
        # # TODO: GET DATA OF sheet 3
        # # Headers for sheet 3
        # if language == "jp":
        #     sheet3["A1"] = "番号"
        #     sheet3["B1"] = "名前"
        #     sheet3["C1"] = "メール"
        # else:
        #     sheet3["A1"] = "N°"
        #     sheet3["B1"] = "FULL NAME"
        #     sheet3["C1"] = "EMAIL"
        #
        # for idx, group in enumerate(groups, start=4):  # Start from column 4 to accommodate the new "No" column
        #     sheet3.cell(row=1, column=idx, value=group)  # Add group to header
        #     sheet3.cell(row=1, column=idx).alignment = Alignment(horizontal="center")
        #     sheet3.cell(row=1, column=idx).fill = header_fill
        #     sheet3.cell(row=1, column=idx).font = white_font
        #
        # # Apply styles to row 1
        # sheet3["A1"].alignment = Alignment(horizontal="center")
        # sheet3["A1"].fill = header_fill
        # sheet3["A1"].font = white_font
        #
        # sheet3["B1"].alignment = Alignment(horizontal="center")
        # sheet3["B1"].fill = header_fill
        # sheet3["B1"].font = white_font
        #
        # sheet3["C1"].alignment = Alignment(horizontal="center")
        # sheet3["C1"].fill = header_fill
        # sheet3["C1"].font = white_font
        #
        # for cell in sheet3[2]:
        #     cell.alignment = Alignment(horizontal="center")
        #     cell.fill = header_fill
        #     cell.font = white_font
        #
        # custom_header_column = len(groups) + 4  # Adjust for the new "No" column
        # sheet3.cell(row=1, column=custom_header_column, value=warning_text1)
        # sheet3.cell(row=1, column=custom_header_column).alignment = Alignment(horizontal="left")
        #
        # sheet3.cell(row=2, column=custom_header_column, value=warning_text2)
        # sheet3.cell(row=2, column=custom_header_column).alignment = Alignment(horizontal="left")
        # sheet3.column_dimensions[get_column_letter(custom_header_column)].width = 60
        #
        # for i, user in enumerate(users, start=3):  # Start from row 3 to accommodate the title rows
        #     sheet3.cell(row=i, column=1, value=i - 2)  # Serial number
        #     sheet3.cell(row=i, column=2, value=user[0])  # FULL NAME
        #     sheet3.cell(row=i, column=3, value=user[1])  # EMAIL
        #
        #     sheet3.cell(row=i, column=1, value=i - 2).fill = gray_fill  # No
        #     sheet3.cell(row=i, column=2, value=user[0]).fill = gray_fill  # FULL NAME
        #     sheet3.cell(row=i, column=3, value=user[1]).fill = gray_fill  # EMAIL
        #
        #     for j, group in enumerate(groups, start=4):  # Start from column 4 to accommodate the new "No" column
        #         if language == "jp":
        #             checkbox_value = "○" if group in user[3] else ""
        #         else:
        #             checkbox_value = "○" if group in user[3] else ""
        #         cell = sheet3.cell(row=i, column=j, value=checkbox_value)
        #         cell.alignment = Alignment(horizontal="center")
        #
        #     sheet3.column_dimensions["A"].width = 5  # Set width for No column
        #     sheet3.column_dimensions["B"].width = 25  # Set width for FULL NAME column
        #     sheet3.column_dimensions["C"].width = 25  # Set width for EMAIL column
        #     for idx in range(4, len(groups) + 4):  # Adjust for the new "No" column
        #         sheet3.column_dimensions[get_column_letter(idx)].width = 15  # Set width for group columns
        #     sheet3.column_dimensions[
        #         get_column_letter(custom_header_column)].width = 40  # Set width for the warning column

        os.makedirs("uploads", exist_ok=True)

        now = datetime.now().strftime("%Y%m%d%H%M%S")
        if language == "jp":
            file_path = os.path.join("uploads", f"スタッフ更新.xlsx")
        else:
            file_path = os.path.join("uploads", f"upd_staff.xlsx")
        workbook.save(file_path)
        return file_path

    except NotFound:
        raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                               detail=f"Users not found.")


EXPECTED_HEADERS_EN = ["FULLNAME", "EMAIL", "PASSWORD", "ACTIVE", "※Do not delete the first two rows"]
EXPECTED_HEADERS_JP = ["フルネーム", "メール", "パスワード", "アクティブ", "※2行目までは削除しないでください"]

EXPECTED_HEADERS_UPDATE_EN = ["FULLNAME", "EMAIL",
                              '※Do not delete the first two rows\n※Gray cells will not be updated even if you change them\n※Please do not change your email address']
EXPECTED_HEADERS_UPDATE_JP = ["フルネーム", "メール",
                              '※2行目までは削除しないでください\n※グレーのセルは変更しても反映されません\n※メールは変更しないでください']


def upload_users_and_process(file, language, db: Session, company_id):
    try:
        base_name = os.path.basename(file.filename)
        name_without_extension = os.path.splitext(base_name)[0]
        case = "_".join(name_without_extension.split("_")[2:])
        case = re.sub(r"[()\d\s]+$", "", name_without_extension)
        if case == 'ins_staff' or case == 'スタッフ追加':
            workbook = openpyxl.load_workbook(file.file)
            sheet = workbook.active

            headers = [cell.value for cell in sheet[1]]
            if language == "en" and headers != EXPECTED_HEADERS_EN:
                # raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                #                        detail=f"列ヘッダーが無効です。予想される値: {EXPECTED_HEADERS_EN}。見つかりました: {headers}" if language == "jp" else f"Invalid column header. Expected: {EXPECTED_HEADERS_EN}. Found: {headers}")
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail=f"Excel ファイルの形式が正しくありません。" if language == "jp" else f"Excel file has invalid format.")
            if language == "jp" and headers != EXPECTED_HEADERS_JP:
                # raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                #                        detail=f"列ヘッダーが無効です。予想される値: {EXPECTED_HEADERS_JP}。見つかりました: {headers}" if language == "jp" else f"Invalid column header. Expected: {EXPECTED_HEADERS_JP}. Found: {headers}")
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail=f"Excel ファイルの形式が正しくありません。" if language == "jp" else f"Excel file has invalid format.")

            data = []
            emails = set()
            duplicate_emails = []
            for row_num, row in enumerate(sheet.iter_rows(min_row=3, values_only=True), start=3):
                email = row[1]
                if email is not None and email in emails:
                    duplicate_emails.append(email)
                else:
                    emails.add(email)
                if not any(cell is not None and str(cell).strip() != "" for cell in row):
                    continue
                if len(row) < 2:
                    continue

                if row[0] is None and row[1] is None:
                    continue
                if row[0] is None and row[1] and row[2]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"名前を空白にすることはできません: 行番号{row_num}" if language == "jp" else f"Name cannot be blank: Line {row_num}")
                if row[1] is None and row[0] and row[2]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"電子メールを空白にすることはできません: 行番号{row_num}" if language == "jp" else f"Email cannot be blank: Line {row_num}")
                if row[2] is None and row[0] and row[1]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"パスワードは空白にできません: 行番号{row_num}" if language == "jp" else f"Password cannot be blank: Line {row_num}")

                if row[0] and row[1] and row[2]:
                    try:
                        validate_email(row[1])
                    except EmailNotValidError:
                        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                               detail=f"メールアドレスが無効です: {row[1]} (行番号{row_num})" if language == "jp" else f"Email is not valid: {row[1]} (Line {row_num})")
                    users = db.query(Users).filter(Users.email == row[1]).first()
                    if users:
                        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                               detail=f"ユーザーはシステム上にすでに存在します: {row[1]} (行番号{row_num})" if language == "jp" else f"User already exists on the system: {row[1]} (Line {row_num})")

                    is_strong, score, message = check_password_strength(row[2], language)
                    if not is_strong:
                        raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                               detail=f"ユーザーのパスワードは、特殊文字、大文字、数字、小文字を含む 8 文字以上である必要があります。: {row[1]} (行番号{row_num})" if language == "jp" else f"Password must be at least 8 characters, including special characters, uppercase letters, numbers and lowercase letters for user: {row[1]} (Line {row_num})")
                    data.append({
                        "full_name": row[0],
                        "email": row[1],
                        "PASSWORD": row[2],
                        "ACTIVE": row[3],
                        # "roles": ["User"],
                        # "groups": ["初期設定グループ"]
                    })
            if duplicate_emails:
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail="重複したメール: " + str(
                                           duplicate_emails) if language == "jp" else "Duplicate email: " + str(
                                           duplicate_emails))
            data = [row for row in data if not (row["full_name"] == "DEMO" and row["email"] == "<EMAIL>")]
            user_active_count = sum(1 for record in data if record.get('ACTIVE') is not None)
            package = db.query(ServicePackage).join(CompanySubscriptions,
                                                    ServicePackage.id == CompanySubscriptions.package_id).filter(
                CompanySubscriptions.company_id == company_id, CompanySubscriptions.status == 1,
                CompanySubscriptions.is_active == True).first()
            count_user = db.query(Users).join(Companies, Users.company_id == Companies.id).join(CompanySubscriptions,
                                                                                                Companies.id == CompanySubscriptions.company_id).join(
                ServicePackage, ServicePackage.id == CompanySubscriptions.package_id).filter(
                Users.company_id == company_id,
                Users.is_musashino == False,
                Users.is_active == True,
                Users.is_deleted == False,
                CompanySubscriptions.is_active == True,
                CompanySubscriptions.status == 1
            ).count()
            if package.max_users < user_active_count + count_user:
                raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                       detail=get_message("user_exceed", language))

            data = [user for user in data if not all(field is None for field in user)]
            for user in data:
                if user.get("ACTIVE") is not None and user["ACTIVE"] == 1 :
                    active = False
                elif user.get("ACTIVE") is None:
                    active = True
                else:
                    raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                           detail=get_message("required_import_users", language))
                db_user = Users(
                    full_name=user["full_name"],
                    email=user["email"],
                    hashed_password=hash_password(user["PASSWORD"]),
                    uid=f"{uuid.uuid4().hex}".upper(),
                    is_active=active,
                    is_deleted=False,
                    is_first_login=True,
                    company_id=company_id,
                    is_musashino=False
                )
                db.add(db_user)
                db.commit()
                db.refresh(db_user)

                role = db.query(Role).filter(Role.name.ilike("%User%")).first()
                user_roles = [UserRole(user_id=db_user.id, role_id=role.id)]
                db.bulk_save_objects(user_roles)
                db.commit()

                # group = db.query(Group).filter(Group.name == "初期設定グループ",
                #                                Group.company_id == company_id).first()
                # if not group:
                #     group = Group(name="初期設定グループ", description="デフォルトグループ",
                #                   is_active=True,
                #                   is_deleted=False, company_id=company_id)
                #     db.add(group)
                #     db.commit()
                #     db.refresh(group)
                # user_groups = [UserGroup(user_id=db_user.id, group_id=group.id)]
                # db.bulk_save_objects(user_groups)
                # db.commit()

            for user in data:
                user["is_active"] = True if user.get("ACTIVE") is None else False
                user.pop("ACTIVE", None)
                user.pop("PASSWORD", None)
            return data
        elif case == "upd_staff" or case == "スタッフ更新":
            workbook = openpyxl.load_workbook(file.file)
            sheet = workbook.active

            headers = [cell.value for cell in sheet[1]]
            if language == "en" and headers != EXPECTED_HEADERS_UPDATE_EN:
                # raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                #                        detail=f"列ヘッダーが無効です。予想される値: {EXPECTED_HEADERS_UPDATE_EN}。見つかりました: {headers}" if language == "jp" else f"Invalid column header. Expected: {EXPECTED_HEADERS_UPDATE_EN}. Found: {headers}")
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail=f"Excel ファイルの形式が正しくありません。" if language == "jp" else f"Excel file has invalid format.")
            if language == "jp" and headers != EXPECTED_HEADERS_UPDATE_JP:
                # raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                #                        detail=f"列ヘッダーが無効です。予想される値: {EXPECTED_HEADERS_UPDATE_JP}。見つかりました: {headers}" if language == "jp" else f"Invalid column header. Expected: {EXPECTED_HEADERS_UPDATE_JP}. Found: {headers}")
                raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                       detail=f"Excel ファイルの形式が正しくありません。" if language == "jp" else f"Excel file has invalid format.")

            sheet1_data = []

            # if language == "jp":
            #     sheet2_name = "ユーザーの役割"
            #     sheet3_name = "ユーザーのグループ"
            # else:
            #     sheet2_name = "Users Role"
            #     sheet3_name = "Users Group"

            sheet1 = workbook.active
            # sheet2 = workbook[sheet2_name]
            # sheet3 = workbook[sheet3_name]

            for row_num, row in enumerate(sheet1.iter_rows(min_row=3, values_only=True), start=3):
                if not any(cell is not None and str(cell).strip() != "" for cell in row):
                    continue
                if len(row) < 2:
                    continue

                if row[0] is None and row[1] is None:
                    continue
                if row[0] is None and row[1]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"名前を空白にすることはできません: 行番号{row_num}" if language == "jp" else f"Name cannot be blank: Line {row_num}")
                if row[1] is None and row[0]:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"電子メールを空白にすることはできません: 行番号{row_num}" if language == "jp" else f"Email cannot be blank: Line {row_num}")
                query_user = db.query(Users).filter(Users.email == row[1], Users.is_deleted == False).first()
                if not query_user:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail=f"ユーザーが見つかりません: {row[1]} (行番号{row_num})" if language == "jp" else f"User not found: {row[1]} (Line {row_num})")
            # for row_num, row in enumerate(sheet2.iter_rows(min_row=3, values_only=True), start=3):
            #     email = row[0]
            #     if email is None:
            #         raise GatewayException(
            #             status_code=http_status.HTTP_400_BAD_REQUEST,
            #             detail=f"メールアドレスが空白です: シート2の行番号{row_num}" if language == "jp" else f"Email is blank: Sheet 2, Line {row_num}"
            #         )
            #
            # for row_num, row in enumerate(sheet3.iter_rows(min_row=3, values_only=True), start=3):
            #     email = row[0]
            #     if email is None:
            #         raise GatewayException(
            #             status_code=http_status.HTTP_400_BAD_REQUEST,
            #             detail=f"メールアドレスが空白です: シート3の行番号{row_num}" if language == "jp" else f"Email is blank: Sheet 3, Line {row_num}"
            #         )

            sheet1_rows = sheet1.max_row
            # sheet2_rows = sheet2.max_row
            # sheet3_rows = sheet3.max_row

            remove_last_column(sheet1)
            # remove_last_column(sheet2)
            # remove_last_column(sheet3)

            # if sheet1_rows != sheet2_rows or sheet1_rows != sheet3_rows:
            #     raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
            #                            detail=get_message("validate_number_row", language))

            sheet1 = workbook.active  # or `sheet1 = workbook['Sheet1']` if it's specifically named
            for row in sheet1.iter_rows(min_row=3, max_row=sheet1.max_row, values_only=True):
                sheet1_data.append(row)

            # categorized_roles = categorize_roles_by_status(sheet2, db)
            # categorized_groups = categorize_groups_by_status(sheet3, db)
            data = []

            for user_data in sheet1_data:
                full_name = user_data[0]
                email = user_data[1]
                # roles = categorized_roles.get(email, {"active_roles": [], "inactive_roles": []})
                # groups = categorized_groups.get(email, {"active_groups": [], "inactive_groups": []})

                user_info = [
                    full_name,
                    email,
                    # roles["active_roles"],
                    # groups["active_groups"]
                ]
                data.append(user_info)

            updated_data = []
            data = [user for user in data if not all(field is None for field in user)]
            for user in data:
                db_user = db.query(Users).filter(Users.email == user[1], Users.is_deleted == False,
                                                 Users.company_id == company_id).first()
                db_user.full_name = user[0]
                # db.query(UserRole).filter(UserRole.user_id == db_user.id).delete()

                # TODO: Add roles to user
                # for role_name in user[2]:
                #     if role_name.upper() == "ADMIN" or role_name.upper() == "USER":
                #         role = db.query(Role).filter(
                #             Role.name == role_name,
                #             Role.important == True,
                #             Role.is_deleted == False,
                #             Role.is_active == True
                #         ).first()
                #         if role:
                #             new_user_role = UserRole(user_id=db_user.id, role_id=role.id)
                #             db.add(new_user_role)
                #     else:
                #         role = db.query(Role).filter(
                #             Role.name == role_name,
                #             Role.company_id == company_id,
                #             Role.is_deleted == False,
                #             Role.is_active == True
                #         ).first()
                #         if role:
                #             new_user_role = UserRole(user_id=db_user.id, role_id=role.id)
                #             db.add(new_user_role)
                #
                # db.query(UserGroup).filter(UserGroup.user_id == db_user.id).delete()
                #
                # # TODO: Add groups to user
                # for group_name in user[3]:
                #     group = db.query(Group).filter(
                #         Group.name == group_name,
                #         Group.company_id == company_id,
                #         Group.is_deleted == False,
                #         Group.is_active == True
                #     ).first()
                #     if group:
                #         new_user_group = UserGroup(user_id=db_user.id, group_id=group.id)
                #         db.add(new_user_group)

                db.commit()
                db.refresh(db_user)

                # TODO: return ưith key and value
                updated_user = {
                    "full_name": db_user.full_name,
                    "email": db_user.email,
                    # "roles": user[2],
                    # "groups": user[3]
                }
                updated_data.append(updated_user)
            return updated_data
        else:
            raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                   detail=get_message("invalid_file_name", language))
    except NotFound:
        raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                               detail=f"Users not found.")


def remove_last_column(sheet):
    last_column = sheet.max_column
    sheet.delete_cols(last_column)


def get_roles_status(db: Session):
    role_db = db.query(Role).filter(Role.is_deleted == False).all()
    active_roles = {role.name: role.is_active for role in role_db}
    return active_roles


# TODO: Function to get role data from second sheet
def get_roles_from_sheet(sheet):
    roles_data = {}

    for row in sheet.iter_rows(min_row=3, values_only=True):
        email = row[2]
        roles = row[3:]
        roles_data[email] = roles

    return roles_data


def categorize_roles_by_status(sheet, db: Session):
    active_roles = get_roles_status(db)

    # TODO: GET ROLES DATA FROM SHEET
    roles_data = get_roles_from_sheet(sheet)
    categorized_roles = {}

    for email, roles in roles_data.items():
        active_roles_for_user = []
        inactive_roles_for_user = []
        for idx, role in enumerate(roles):
            if role == "○" or role == "○":
                role_name = sheet.cell(row=1, column=idx + 4).value
                if active_roles.get(role_name, False):
                    active_roles_for_user.append(role_name)
            else:
                role_name = sheet.cell(row=1, column=idx + 4).value
                inactive_roles_for_user.append(role_name)

        categorized_roles[email] = {
            "active_roles": active_roles_for_user,
            "inactive_roles": inactive_roles_for_user
        }

    return categorized_roles


# TODO: Function to get group data from third sheet
def get_groups_status(db: Session):
    group_db = db.query(Group).filter(Group.is_deleted == False).all()
    active_groups = {group.name: group.is_active for group in group_db}
    return active_groups


# TODO: Function to get group data from third sheet
def get_groups_from_sheet(sheet):
    groups_data = {}

    for row in sheet.iter_rows(min_row=3, values_only=True):
        email = row[2]
        groups = row[3:]
        groups_data[email] = groups

    return groups_data


# TODO: Function to categorize groups by status
def categorize_groups_by_status(sheet, db: Session):
    active_groups = get_groups_status(db)

    groups_data = get_groups_from_sheet(sheet)

    categorized_groups = {}

    for email, groups in groups_data.items():
        active_groups_for_user = []
        inactive_groups_for_user = []

        for idx, group in enumerate(groups):
            if group == "○" or group == "○":
                group_name = sheet.cell(row=1, column=idx + 4).value
                if active_groups.get(group_name, False):
                    active_groups_for_user.append(group_name)
            else:
                group_name = sheet.cell(row=1, column=idx + 4).value
                inactive_groups_for_user.append(group_name)

        categorized_groups[email] = {
            "active_groups": active_groups_for_user,
            "inactive_groups": inactive_groups_for_user
        }

    return categorized_groups


def fonfun_csv_template(db: Session):
    static_headers = ["番号", "フルネーム", "メール", "パスワード", "User", "Admin"]
    group_db = db.query(Group).filter(Group.is_deleted == False, Group.is_active == True).all()
    dynamic_group_headers = [group.name for group in group_db]
    headers = static_headers + dynamic_group_headers

    os.makedirs("uploads", exist_ok=True)

    now = datetime.now().strftime("%Y%m%d%H%M%S")
    file_path = os.path.join("uploads", f"{now}_template_import_users.csv")

    with open(file_path, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(headers)

        group_user = [0] * len(dynamic_group_headers)
        user_data = [
                        "",
                        "USER1",
                        "<EMAIL>",
                        "T123456aA@",
                        1,
                        1,
                    ] + group_user
        writer.writerow(user_data)
    return file_path


def fonfun_download_to_csv(userUpdateExcel, language, db):
    if userUpdateExcel.list_user is None:
        raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                               detail=get_message("something_wrong", language))

    for user_id in userUpdateExcel.list_user:
        db_user = db.query(Users).filter(Users.id == user_id).first()
        if not db_user:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=get_message("user_not_found", language))

    static_headers = ["番号", "フルネーム", "メール", "パスワード", "User", "Admin"]

    group_db = db.query(Group).filter(Group.is_deleted == False, Group.is_active == True).all()
    dynamic_group_headers = [group.name for group in group_db]
    headers = static_headers + dynamic_group_headers

    users = db.query(Users).filter(
        Users.is_deleted == False,
        Users.is_active == True,
        Users.id.in_(userUpdateExcel.list_user)
    ).all()

    os.makedirs("uploads", exist_ok=True)
    now = datetime.now().strftime("%Y%m%d%H%M%S")
    file_path = os.path.join("uploads", f"{now}_UPDATE_USERS_INFORMATION.csv")

    with open(file_path, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        for user in users:
            user_roles = db.query(Role).join(UserRole, UserRole.role_id == Role.id).join(Users,
                                                                                         Users.id == UserRole.user_id).filter(
                Role.is_active == True, Role.is_deleted == False,
                UserRole.user_id == user.id).all()
            user_groups = db.query(Group).join(UserGroup, UserGroup.group_id == Group.id).join(Users,
                                                                                               Users.id == UserGroup.user_id).filter(
                Group.is_active == True, Group.is_deleted == False,
                UserGroup.user_id == user.id).all()

            role_user = [role.name for role in user_roles]

            group_user = [0] * len(dynamic_group_headers)
            for group in group_db:
                for user_group in user_groups:
                    if group.id == user_group.id:
                        group_user[group_db.index(group)] = 1

            user_data = [
                user.id,
                user.full_name,
                user.email,
                "",
                1 if "User" in role_user else 0,
                1 if "Admin" in role_user else 0,
            ]
            user_data += group_user
            writer.writerow(user_data)
    return file_path


def fonfun_upload_by_csv(file, language, db: Session):
    content = file.read()
    csv_reader = csv.DictReader(io.StringIO(content.decode("utf-8")))

    # Static headers
    expected_static_headers = ["番号", "フルネーム", "メール", "パスワード", "User", "Admin"]

    # Fetch dynamic group headers from the database
    group_names = db.query(Group.name).filter(Group.is_deleted == False, Group.is_active == True).all()
    dynamic_group_headers = [group.name for group in group_names]

    # Combine static and dynamic headers
    expected_headers = expected_static_headers + dynamic_group_headers

    # Validate headers
    if not all(header in csv_reader.fieldnames for header in expected_headers):
        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                               detail=get_message("validate_file_csv_header", language))

    # Initialize counters and error records list
    success_count = 0
    error_count = 0
    error_records = []

    # validation message
    validation_messages = {
        "en": {
            "fullname": {
                "required": "Full name is required."
            },
            "email": {
                "required": "Email is required.",
                "email": "Invalid email address.",
            },
            "password": {
                "required": "Password is required.",
                "policy": "Password must be at least 8 characters long and contain uppercase, number, special character, non-letter.",
            },
            "user": {
                "not_in": "User must be in [0, 1].",
            },
            "admin": {
                "not_in": "Admin must be in [0, 1].",
            },
            "group": {
                "required": "Group is required.",
                "not_in": "Group must be in [0, 1].",
            },
        },
        "jp": {
            "fullname": {
                "required": "フルネームは必須です。"
            },
            "email": {
                "required": "メールは必須です。",
                "email": "メールアドレスが無効です。",
            },
            "password": {
                "required": "パスワードは必須です。",
                "policy": "パスワードは8文字以上で、大文字、数字、特殊文字、非文字を含む必要があります。",
            },
            "user": {
                "not_in": "ユーザーは[0, 1]の中にある必要があります。",
            },
            "admin": {
                "not_in": "管理者は[0, 1]の中にある必要があります。",
            },
            "group": {
                "required": "グループは必須です。",
                "not_in": "グループは[0, 1]の中にある必要があります。",
            },
        }
    }

    # Validate and parse CSV rows
    users_data = []
    for row in csv_reader:
        try:
            if not row["フルネーム"]:
                raise ValueError(validation_messages[language]["fullname"]["required"])
            if not row["メール"]:
                raise ValueError(validation_messages[language]["email"]["required"])
            if not row["パスワード"]:
                raise ValueError(validation_messages[language]["password"]["required"])
            if not validate_email(row["メール"]):
                raise ValueError(validation_messages[language]["email"]["email"])

            is_strong, score, message = check_password_strength(row["パスワード"])
            if not is_strong:
                raise ValueError(validation_messages[language]["password"]["policy"])

            if not row["User"] and not row["Admin"]:
                raise ValueError(
                    "ユーザーまたは管理者が必要です。" if language == "jp" else "User or Admin is required.")
            if row["User"] not in ["0", "1"]:
                raise ValueError(validation_messages[language]["user"]["not_in"])

            if row["Admin"] not in ["0", "1"]:
                raise ValueError(validation_messages[language]["admin"]["not_in"])

            if not all(row[group] for group in dynamic_group_headers):
                raise ValueError(validation_messages[language]["group"]["required"])
            if any(row[group] not in ["0", "1"] for group in dynamic_group_headers):
                raise ValueError(validation_messages[language]["group"]["not_in"])

        except Exception as e:
            error_count += 1
            error_records.append({"row": row, "error": str(e)})

        user_data = {
            "full_name": row["フルネーム"],
            "email": row["メール"],
            "hashed_password": hash_password(row["パスワード"]),
            "roles": [],
            "group": [],
        }

        # get roles from db
        roles = db.query(Role).filter(Role.is_deleted == False, Role.is_active == True).all()
        if row["User"] == "1":
            # append roles' id where role name is User to user_data["roles"]
            user_data["roles"].append([role.id for role in roles if role.name == "User"][0])
        if row["Admin"] == "1":
            user_data["roles"].append([role.id for role in roles if role.name == "Admin"][0])

        groups = db.query(Group).filter(Group.is_deleted == False, Group.is_active == True).all()
        for group in groups:
            if row[group.name] == "1":
                user_data["group"].append(group.id)

        users_data.append(user_data)

    if error_count > 0:
        return DataResponse().success(
            statusCode=http_status.HTTP_200_OK,
            data={
                "message": {
                    "success": f"アップロード件数：{success_count}件",
                    "error": f"エラー件数：{error_count}件"
                },
                "error_records": error_records
            }
        )

    # Initialize counters and error records list
    success_count = 0
    error_count = 0
    error_records = []

    # save user_data to database, if email is already exist, update user's information, roles, groups.
    # if email is not exist, create new user, roles, groups
    # if user is already exist, update user's information, roles, groups
    for user_data in users_data:
        try:
            user = db.query(Users).filter(Users.email == user_data["email"]).first()
            if user:
                user.full_name = user_data["full_name"]
                user.hashed_password = user_data["hashed_password"]
                if user.is_deleted:
                    user.is_deleted = False
                    user.is_active = True
                    user.deleted_at = None
                    user.created_at = datetime.now()
                    user.updated_at = datetime.now()
                db.commit()
                db.refresh(user)
                # delete all roles of user
                db.query(UserRole).filter(UserRole.user_id == user.id).delete()
                db.commit()
                # add new roles to user
                for role_id in user_data["roles"]:
                    user_role = UserRole(user_id=user.id, role_id=role_id)
                    db.add(user_role)
                    db.commit()
                    db.refresh(user_role)

                # delete all groups of user
                db.query(UserGroup).filter(UserGroup.user_id == user.id).delete()
                db.commit()
                # add new groups to user
                for group_id in user_data["group"]:
                    user_group = UserGroup(user_id=user.id, group_id=group_id)
                    db.add(user_group)
                    db.commit()
                    db.refresh(user_group)
            else:
                user = Users(full_name=user_data["full_name"], email=user_data["email"],
                             hashed_password=user_data["hashed_password"])
                db.add(user)
                db.commit()
                db.refresh(user)
                for role_id in user_data["roles"]:
                    user_role = UserRole(user_id=user.id, role_id=role_id)
                    db.add(user_role)
                    db.commit()
                    db.refresh(user_role)
                for group_id in user_data["group"]:
                    user_group = UserGroup(user_id=user.id, group_id=group_id)
                    db.add(user_group)
                    db.commit()
                    db.refresh(user_group)
            success_count += 1
        except Exception as e:
            error_count += 1
            error_records.append({"row": user_data, "error": str(e)})
            continue

    return DataResponse().success(
        statusCode=http_status.HTTP_200_OK,
        data={
            "message": {
                "success": f"アップロード件数：{success_count}件",
                "error": f"エラー件数：{error_count}件"
            },
            "error_records": error_records
        }
    )
