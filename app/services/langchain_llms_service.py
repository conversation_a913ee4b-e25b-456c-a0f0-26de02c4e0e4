import re
import random
from typing import List
from sqlalchemy.orm import Session
from app.core.cloud_engine.google import GoogleCloudPlatform
from app.core.rag.callbacks.token_callback_handler import (OpenAITokenCallback<PERSON>and<PERSON>, VertexAIToken<PERSON>allback<PERSON>and<PERSON>,
                                                           ClaudeVertexAITokenCallbackHandler)
from app.core.rag.builders.langchain_builder import RetrievalAndLL<PERSON>hain
from app.helpers.config import settings
from app.services.chat_history_service import ChatHistoryService
from app.services.chat_room_service import ChatRoomService
from app.schemas.chat import ChatReq
from app.models.ai_services import ChatRoom
from app.helpers.exceptions import GatewayException
from fastapi import APIRouter, Depends, status as http_status


class ConversationalLangChain:
    __slots__ = ['db', 'project_id', 'user_id', 'params', 'chatroom_service', 'chat_history_service', 'db_usecase',
                 'llm_type']

    def __init__(self, db: Session, user_id: int, db_usecase, params: ChatReq):
        self.db = db
        _, self.project_id = GoogleCloudPlatform.init_credentials()
        self.user_id = user_id
        self.params = params
        self.db_usecase = db_usecase
        self.chatroom_service = ChatRoomService(db=self.db)
        self.chat_history_service = ChatHistoryService(db=self.db)
        self.llm_type = "vertexai"

    def get_llm_config(self):
        detail_provider = self.chat_history_service.get_detail_provider(
            params=self.params)
        if detail_provider:
            if detail_provider.ai_service.lower() in ["vertexai", "google"]:
                if self.params.provider_id:
                    db_provider = self.chat_history_service.get_provider(
                        params=self.params)
                    if db_provider:
                        return {
                            "model_name": db_provider,
                            "api_key": None,
                            "callback_handler": VertexAITokenCallbackHandler(model_name=db_provider)
                        }
                    else:
                        raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                               detail=f"AIモデルはシステムに存在しないか、削除されています")
                elif self.params.provider_id is None and self.params.chatroom_code:
                    db_provider = self.chat_history_service.get_provider_none(
                        params=self.params)
                    if db_provider:
                        return {
                            "model_name": db_provider,
                            "api_key": None,
                            "callback_handler": VertexAITokenCallbackHandler(model_name=db_provider)
                        }
            elif detail_provider.ai_service.lower() in ["anthropic", "claude-vertexai"]:
                db_provider = self.chat_history_service.get_provider(
                    params=self.params)
                location = 'us-east5'
                test = ClaudeVertexAITokenCallbackHandler(
                    model_name=db_provider,
                    project=self.project_id,
                    location=location,
                )
                print(test)
                return {
                    "model_name": db_provider,
                    "api_key": None,
                    "callback_handler": test
                }
            elif detail_provider.ai_service.lower() in ["openai"]:
                key = self.chat_history_service.get_openai_key(self.user_id)
                if not key:
                    raise GatewayException(status_code=http_status.HTTP_400_BAD_REQUEST,
                                           detail="OpenAIのAPIキーが設定されていません。管理者にお問い合わせください。")
                db_provider = self.chat_history_service.get_provider(
                    params=self.params)
                model_name = db_provider if db_provider else "gpt-4o-mini"
                return {
                    "model_name": model_name,
                    "api_key": key,
                    "callback_handler": OpenAITokenCallbackHandler(
                        model_name=model_name,
                        api_key=key
                    )
                }
        else:
            models: List[str] = [
                'gemini-2.0-flash',
                'gemini-2.0-flash-exp',
            ]
            weights: List[float] = [0.1, 0.3]
            model_name = random.choices(models, weights=weights, k=1)[0]
            print(f"Selected model: {model_name}")
            return {
                "model_name": model_name,
                "api_key": None,
                "callback_handler": VertexAITokenCallbackHandler(model_name=model_name)
            }

    def build_chain(self, query: str):
        llm_config = self.get_llm_config()
        rag_config = {
            "project_id": self.project_id,
            "location_id": self.db_usecase.location,
            "data_store_id": self.db_usecase.data_store_id,
            "max_documents": self.db_usecase.max_documents,
            "get_extractive_answers": self.db_usecase.get_extractive_answers,
            "engine_data_type": self.db_usecase.engine_data_type,
            "max_extractive_answer_count": self.db_usecase.max_extractive_answer_count,
            "max_extractive_segment_count": 1,
        }
        detail_provider = self.chat_history_service.get_detail_provider(
            params=self.params)
        if detail_provider:
            if detail_provider.ai_service.lower() in ["vertexai", "google"]:
                self.llm_type = "vertexai"
            elif detail_provider.ai_service.lower() in ["anthropic", "claude-vertexai"]:
                self.llm_type = "claude"
            elif detail_provider.ai_service.lower() in ["openai"]:
                self.llm_type = "openai"

        # Get chatroom to check user's reasoning preference
        chatroom = None
        if self.params.chatroom_code:
            chatroom = self.db.query(ChatRoom).filter(
                ChatRoom.chatroom_code == self.params.chatroom_code,
                ChatRoom.is_deleted == False
            ).first()

        # Determine final chain_of_thought setting with proper priority
        user_reasoning_preference = None

        # Priority 1: Use enable_reasoning from current request (highest priority)
        if hasattr(self.params, 'enable_reasoning') and self.params.enable_reasoning is not None:
            user_reasoning_preference = self.params.enable_reasoning
        # Priority 2: Fallback to chatroom setting from database
        elif chatroom and chatroom.reasoning_enabled is not None:
            user_reasoning_preference = chatroom.reasoning_enabled

        return RetrievalAndLLMChain(
            project_id=self.project_id,
            location='us-east5',
            llm_type=self.llm_type,
            query=query,
            retriever_type="single",
            system_prompt=self.db_usecase.system_prompt,
            human_prompt=self.db_usecase.human_prompt,
            chain_prompt=self.db_usecase.chain_prompt,
            format_output=self.db_usecase.format_output,
            chain_of_thought_prompt=self.db_usecase.chain_of_thought_prompt,
            user_reasoning_preference=user_reasoning_preference,  # Pass user preference
            config=rag_config,
            model_name=llm_config["model_name"],
            api_key=llm_config["api_key"],
            callback_handler=llm_config["callback_handler"],
            use_ragsource=self.db_usecase.use_ragsource,
            db=self.db if self.db else None,
        )

    async def handle_request(self, user_info=None):
        wrappers = [
            r'^\s*```(json|text|code|python)?\s*',
            r'```\s*$',
            r'^\s*{',
            r'}\s*$',
        ]
        chain = self.build_chain(self.params.message)
        message_history_session = await self.chat_history_service.get_all_chat_history_in_session(params=self.params,
                                                                                                  user_id=self.user_id)
        history_messages = []
        if message_history_session:
            for row in message_history_session[::-1]:
                if row.user_message:
                    history_messages.append({"role": "human", "content": row.user_message})
                if row.ai_response:
                    history_messages.append({"role": "assistant", "content": row.ai_response})

        response = await chain.run(params=self.params, history_messages=history_messages, user_info=user_info)
        answer_text = response.get("response")
        for wrapper in wrappers:
            answer_text = re.sub(wrapper, '', answer_text.strip(), flags=re.DOTALL)  # re.MULTILINE
        answer_text = answer_text.strip()

        token_usage = response.get("token_usage")
        references = response.get("references")

        chatroom = await self.chatroom_service.create_chatroom(
            title=self.params.title,
            chatroom_code=self.params.chatroom_code,
            user_id=self.user_id,
            use_case_id=self.params.use_case_id,
            provider_id=self.params.provider_id,
            enable_reasoning=self.params.enable_reasoning
        )

        chat_history = await self.chat_history_service.create_chat_history(
            params=self.params,
            db_usecase=self.db_usecase,
            chatroom_id=chatroom.id,
            user_id=self.user_id,
            ai_response=answer_text,
            references=references
        )

        return {
            "chatroom_code": self.params.chatroom_code,
            "chatroom": chatroom.id,
            "chat_history": {
                "message_id": chat_history.message_id,
                "session_id": chat_history.session_id,
                "use_case_id": self.params.use_case_id,
            },
            "token_usage": token_usage,
            "answer_text": answer_text,
            "references": references,
            "state": "SUCCESS",
            "reasoning_enabled": chatroom.reasoning_enabled,
            "use_case_allows_reasoning": self.db_usecase.chain_of_thought_prompt
        }
