import asyncio
import traceback
from typing import List, Dict, Optional, Any
from app.helpers.constants import EmailStatus
from app.core.sendmail.send_mail import EmailService, EmailClient, EmailTemplate
from app.core.sendmail.send_async_mail import EmailAsyncService, EmailAsyncClient, EmailAsyncTemplate


class PasswordResetService(EmailService):
    def __init__(self, client: EmailClient, template: EmailTemplate):
        super().__init__(client)
        self.template = template

    def send_password_reset(self, receivers: List[Dict], db) -> List[Dict[str, str]]:
        resp = []
        for recipient in receivers:
            context = recipient.get('context', {})
            html_content = self.template.render(context)
            send_status = self.send_email(
                recipient=recipient['email'],
                subject='Reset Password Link',
                html_content=html_content
            )
            resp.append({"email": recipient['email'], "status": send_status})

        return resp


class PasswordResetAsyncService(EmailAsyncService):
    def __init__(self, client: <PERSON>ailAsyncClient, template: EmailAsyncTemplate):
        super().__init__(client)
        self.template = template

    async def send_password_reset(self, receivers: List[Dict], db) -> List[Dict[str, str]]:
        resp = []
        tasks = []

        for recipient in receivers:
            context = recipient.get('context', {})
            html_content = self.template.render(context)
            tasks.append(
                self.send_email(
                    recipient=recipient['email'],
                    subject='Reset Password Link',
                    html_content=html_content
                )
            )

        results = await asyncio.gather(*tasks, return_exceptions=True)
        for recipient, result in zip(receivers, results):
            if isinstance(result, Exception):
                resp.append({"email": recipient['email'], "status": f"{EmailStatus.FAIL}: {str(result)}"})
            else:
                resp.append({"email": recipient['email'], "status": f"{EmailStatus.SUCCESS}"})

        return resp
