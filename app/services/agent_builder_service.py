from sqlalchemy.orm import Session
from google.cloud import discoveryengine_v1 as discoveryengine
from app.core.rag.agent_builder import Agent<PERSON><PERSON><PERSON>
from app.services.chat_history_service import ChatHistoryService
from app.services.chat_room_service import ChatRoomService
from app.schemas.chat import ChatReq


class ConversationalAgent:
    __slots__ = ['db', 'user_id', 'params', 'chatroom_service', 'chat_history_service', 'db_usecase', 'builder',
                 'client']

    def __init__(self, db: Session, user_id: int, db_usecase, params: ChatReq):
        self.db = db
        self.user_id = user_id
        self.params = params
        self.db_usecase = db_usecase
        self.chatroom_service = ChatRoomService(db=self.db)
        self.chat_history_service = ChatHistoryService(db=self.db)
        self.builder = AgentBuilder(self.db_usecase)
        self.client = discoveryengine.ConversationalSearchServiceClient(client_options=self.builder.client_options)

    async def handle_request(self):
        query = discoveryengine.Query({"text": self.params.message})
        answer_request = discoveryengine.AnswerQueryRequest(
            serving_config=self.builder.serving_config,
            query=query,
            query_understanding_spec=self.builder.build_query_understanding_spec(),
            answer_generation_spec=self.builder.build_answer_generation_spec(),
        )
        chat_response = self.client.answer_query(request=answer_request)
        answer_text = chat_response.answer.answer_text

        token_usage = None
        references = None

        chatroom = await self.chatroom_service.create_chatroom(
            title=self.params.title,
            chatroom_code=self.params.chatroom_code,
            user_id=self.user_id,
            use_case_id=self.params.use_case_id
        )

        chat_history = await self.chat_history_service.create_chat_history(
            params=self.params,
            db_usecase=self.db_usecase,
            chatroom_id=chatroom.id,
            user_id=self.user_id,
            ai_response=str(answer_text),
            references=references
        )

        return {
            "chatroom_code": self.params.chatroom_code,
            "chatroom": chatroom.id,
            "chat_history": {
                "message_id": chat_history.message_id,
                "session_id": chat_history.session_id,
                "use_case_id": self.params.use_case_id,
            },
            "token_usage": token_usage,
            "answer_text": answer_text,
            "references": references,
            "state": "SUCCESS"
        }
