from __future__ import annotations

from abc import ABC, abstractmethod
from collections.abc import Sequence
from typing import Union
from pydantic import BaseModel, Field
from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    get_buffer_string,
)
from sqlalchemy.orm import Session
from app.services.agent_builder_service import ConversationalAgent
from app.services.langchain_llms_service import Conversational<PERSON>ang<PERSON>hain
from app.services.use_case_service import UseCaseService
from app.schemas.chat import ChatReq


class BaseChatMessageHistory(ABC):
    messages: list[BaseMessage]

    async def aget_messages(self) -> list[BaseMessage]:
        from langchain_core.runnables.config import run_in_executor

        return await run_in_executor(None, lambda: self.messages)

    def add_user_message(self, message: Union[HumanMessage, str]) -> None:
        if isinstance(message, HumanMessage):
            self.add_message(message)
        else:
            self.add_message(HumanMessage(content=message))

    def add_ai_message(self, message: Union[AIMessage, str]) -> None:
        if isinstance(message, AIMessage):
            self.add_message(message)
        else:
            self.add_message(AIMessage(content=message))

    def add_message(self, message: BaseMessage) -> None:
        if type(self).add_messages != BaseChatMessageHistory.add_messages:
            self.add_messages([message])
        else:
            msg = (
                "add_message is not implemented for this class. "
                "Please implement add_message or add_messages."
            )
            raise NotImplementedError(msg)

    def add_messages(self, messages: Sequence[BaseMessage]) -> None:
        for message in messages:
            self.add_message(message)

    async def aadd_messages(self, messages: Sequence[BaseMessage]) -> None:
        from langchain_core.runnables.config import run_in_executor

        await run_in_executor(None, self.add_messages, messages)

    @abstractmethod
    def clear(self) -> None:
        """Remove all messages from the store"""

    async def aclear(self) -> None:
        from langchain_core.runnables.config import run_in_executor

        await run_in_executor(None, self.clear)

    def __str__(self) -> str:
        return get_buffer_string(self.messages)


class InMemoryChatMessageHistory(BaseChatMessageHistory, BaseModel):
    messages: list[BaseMessage] = Field(default_factory=list)
    async def aget_messages(self) -> list[BaseMessage]:
        return self.messages

    def add_message(self, message: BaseMessage) -> None:
        self.messages.append(message)

    async def aadd_messages(self, messages: Sequence[BaseMessage]) -> None:
        self.add_messages(messages)

    def clear(self) -> None:
        self.messages = []

    async def aclear(self) -> None:
        self.clear()


class ConversationalHandler:
    __slots__ = ['db', 'user_id', 'params', 'use_case_service', 'db_usecase', 'handler']

    def __init__(self, db: Session, user_id: int, params: ChatReq):
        self.db = db
        self.user_id = user_id
        self.params = params
        self.use_case_service = UseCaseService(db=self.db)
        self.db_usecase = self.use_case_service.get_use_case(self.params.use_case_id)

        match self.db_usecase.ai_service:
            case "Agent builder":
                self.handler = ConversationalAgent(db=db, user_id=user_id, db_usecase=self.db_usecase, params=params)

            case _:
                self.handler = ConversationalLangChain(db=db, user_id=user_id, db_usecase=self.db_usecase, params=params)

    async def handle_request(self, user_info=None):
        return await self.handler.handle_request(user_info=user_info)