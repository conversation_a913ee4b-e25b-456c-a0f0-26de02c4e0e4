from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import desc
from app.models.ai_services import Chat<PERSON><PERSON>ory, ChatRoom, Providers
from app.schemas.chat import ChatReq
from app.models.config import Configuration, ConfigurationModelKey
from app.models.users import Users
from app.helpers.config import settings


class ChatHistoryService:
    def __init__(self, db: Session):
        self.db = db

    async def get_all_chat_history_in_session(self, params: ChatReq, user_id: int) -> List[ChatHistory] | None:
        message_history = (
            self.db.query(ChatHistory)
            .join(ChatRoom, ChatHistory.chatroom_id == ChatRoom.id)
            .with_entities(ChatHistory.user_message.label("user_message"), ChatHistory.ai_response.label("ai_response"))
            .filter(
                ChatRoom.chatroom_code == params.chatroom_code,
                ChatHistory.is_deleted == False,
                ChatHistory.is_active == True,
                ChatHistory.user_id == user_id
            )
            .order_by(desc(ChatHistory.id))
            .limit(5)
            .all()
        )
        if message_history:
            return message_history
        return None

    async def create_chat_history(self, params: ChatReq, db_usecase, chatroom_id: int, user_id: int, ai_response: str,
                                  references: str) -> ChatHistory:
        chat_history = ChatHistory(
            user_id=user_id,
            chatroom_id=chatroom_id,
            message_id=params.message_id,
            chat_id=params.chat_id,
            session_id=params.session_id,
            service_name=db_usecase.ai_service,
            use_case_id=params.use_case_id,
            user_message=params.message,
            ai_response=ai_response,
            language=db_usecase.answer_language_code,
            references=references,
            sentiment=None,
            input_length=None,
            output_length=None,
            input_tokens=None,
            output_tokens=None
        )
        self.db.add(chat_history)
        self.db.commit()
        self.db.refresh(chat_history)
        return chat_history

    def get_provider(self, params: ChatReq) -> str | None:
        db_provider = self.db.query(Providers).filter(Providers.id == params.provider_id,
                                                      Providers.is_active == True,
                                                      Providers.is_deleted == False).first()
        if db_provider:
            return db_provider.llm_model_id
        return None

    def get_provider_none(self, params: ChatReq) -> str | None:
        get_provider_id = self.db.query(ChatRoom).filter(ChatRoom.chatroom_code == params.chatroom_code,
                                                         ChatRoom.is_active == True,
                                                         ChatRoom.is_deleted == False).first()
        db_provider = self.db.query(Providers).filter(Providers.id == get_provider_id.provider_id,
                                                      Providers.is_active == True,
                                                      Providers.is_deleted == False).first()
        if db_provider:
            return db_provider.llm_model_id
        return None

    def get_detail_provider(self, params: ChatReq):
        db_provider = self.db.query(Providers).filter(Providers.id == params.provider_id,
                                                      Providers.is_active == True,
                                                      Providers.is_deleted == False).first()
        if db_provider:
            return db_provider
        return None

    def get_openai_key(self, user_id: int):
        detail_user = self.db.query(Users).filter(
            Users.is_active == True,
            Users.is_deleted == False,
            Users.id == user_id
        ).first()
        model_key = self.db.query(ConfigurationModelKey).filter(
            ConfigurationModelKey.service_name == 'OPENAI_KEY',
            ConfigurationModelKey.is_active == True,
            ConfigurationModelKey.is_deleted == False,
            ConfigurationModelKey.company_id == detail_user.company_id
        ).first()
        if detail_user.company_id == 1:
            return settings.MUSA_OPENAI_API_KEY
        else:
            if model_key:
                return model_key.service_key
            else:
                return None
