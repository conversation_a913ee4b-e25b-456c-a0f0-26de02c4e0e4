from sqlalchemy.orm import Session
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from app.models.ai_services import UseCases


class UseCaseService:
    def __init__(self, db: Session):
        self.db = db

    def get_use_case(self, use_case_id: int):
        use_case = self.db.query(UseCases).filter(
            UseCases.id == use_case_id,
            UseCases.is_active == True,
            UseCases.is_deleted == False
        ).first()
        if not use_case:
            raise GatewayException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="UseCase Not Found!"
            )
        return use_case
