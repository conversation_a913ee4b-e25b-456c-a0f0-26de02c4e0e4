import traceback
from datetime import timed<PERSON><PERSON>
from google.cloud import discoveryengine_v1
from google.api_core.exceptions import NotFound, AlreadyExists, BadRequest as GoogleBadRequest
from app.core.cloud_engine.google import GoogleCloudPlatform
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException


class StorageService(GoogleCloudPlatform):
    def get_tree_documents(self, bucket_name: str, prefix: str = "", max_depth: int = None):
        bucket = self.storage_client.bucket(bucket_name)
        blobs = list(self.storage_client.list_blobs(bucket, prefix=prefix))

        tree = {}
        if blobs:
            for blob in blobs:
                parts = blob.name.split("/")
                if max_depth and len(parts) > max_depth:
                    continue

                current_level = tree
                for part in parts[:-1]:
                    current_level = current_level.setdefault(part, {})
                if not blob.name.endswith("/"):
                    current_level[parts[-1]] = "file"

            return tree

        return None

    def set_iam_policy(self, bucket_name, blob_name, user_email):
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            policy = blob.get_iam_policy(requested_policy_version=3)
            role = "roles/storage.objectViewer"
            member = f"user:{user_email}"
            if not any(binding["role"] == role and member in binding["members"] for binding in policy.bindings):
                policy.bindings.append({"role": role, "members": {member}})
            blob.set_iam_policy(policy)
        except Exception:
            print(traceback.format_exc())

    def generate_signed_url_with_access(self, bucket_name, blob_name, user_email, expiration_minutes=15):
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)

            # acl = blob.acl
            # acl.user(user_email)()
            # acl.save()

            return blob.generate_signed_url(
                expiration=timedelta(minutes=expiration_minutes),
                method="GET"
            )

        except Exception:
            print(traceback.format_exc())

    def generate_signed_url(
            self,
            user_email,
            bucket_name,
            blob_name,
            expiration_minutes=15,
            role="roles/storage.objectViewer"
    ):
        try:
            bucket = self.storage_client.bucket(bucket_name)
            bucket_policy = bucket.get_iam_policy(requested_policy_version=3)
            member = f"user:{user_email}"
            existing_binding = next(
                (binding for binding in bucket_policy.bindings if binding.get('role') == role),
                None
            )

            if existing_binding:
                if member not in existing_binding.get('members', set()):
                    existing_binding['members'].add(member)
            else:
                bucket_policy.bindings.append({
                    "role": role,
                    "members": {member}
                })

            bucket.set_iam_policy(bucket_policy)

            blob = bucket.blob(blob_name)
            signed_url = blob.generate_signed_url(
                expiration=timedelta(minutes=expiration_minutes),
                version="v4",
                method="GET"
            )

            return signed_url

        except Exception:
            print(f"Unexpected signed_url error: {traceback.format_exc()}")
            raise

    def upload_blob(self, bucket_name, source_file_name, destination_blob_name):
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(destination_blob_name)
        blob.upload_from_filename(source_file_name)
        print(f"File {source_file_name} uploaded to {destination_blob_name}.")

    def delete_blob(self, bucket_name, blob_name):
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        blob.delete()
        print(f"Blob {blob_name} deleted from {bucket_name}.")


class DiscoveryEngineService(GoogleCloudPlatform):
    def list_data_stores(self, project_id, location="global"):
        parent = f"projects/{project_id}/locations/{location}"
        data_stores = self.discovery_engine_client.list_data_stores(parent=parent)
        if data_stores:
            return [data_store.name.split('/')[-1] for data_store in data_stores]
        return []

    def list_documents(self, project_id, location, data_store_id, page_size=50):
        client = discoveryengine_v1.DocumentServiceClient()
        parent = f"projects/{project_id}/locations/{location}/dataStores/{data_store_id}/branches/default_branch"
        cfs = {
            "parent": parent,
            "page_size": page_size,
        }
        req = discoveryengine_v1.ListDocumentsRequest(cfs)
        documents = client.list_documents(request=req)
        document_list = []
        for document in documents:
            document_dict = {
                "name": document.name,
                "id": document.id,
                "schema_id": document.schema_id,
                "struct_data": dict(document.struct_data),
                "parent_document_id": document.parent_document_id,
                "content": {
                    "mime_type": document.content.mime_type,
                    "uri": document.content.uri
                }
            }
            document_list.append(document_dict)

        return document_list if document_list else []

    def get_detail_data_store_id(self, project_id, location, data_store_id):
        client = self.discovery_engine_client
        data_store_path = f"projects/{project_id}/locations/{location}/dataStores/{data_store_id}"

        try:
            data_store = client.get_data_store(name=data_store_path)
            industry_vertical = data_store.industry_vertical.name if data_store.industry_vertical else None
            content_config = data_store.content_config.name if data_store.content_config else None
            solution_types = [solution.name for solution in
                              data_store.solution_types] if data_store.solution_types else None

            billing_estimation = {
                "unstructured_data_size": data_store.billing_estimation.unstructured_data_size,
                "unstructured_data_update_time": data_store.billing_estimation.unstructured_data_update_time if data_store.billing_estimation.unstructured_data_update_time else None
            } if data_store.billing_estimation else None

            document_processing_config = {
                "name": data_store.document_processing_config.name,
                "default_parsing_config": {
                    "ocr_parsing_config": {
                        "use_native_text": data_store.document_processing_config.default_parsing_config.ocr_parsing_config.use_native_text
                    }
                }
            } if data_store.document_processing_config else None

            if data_store.create_time:
                create_time = data_store.create_time
                create_time_str = create_time.strftime('%Y-%m-%d %H:%M:%S')
            else:
                create_time_str = None

            data = {
                "name": data_store.name,
                "display_name": data_store.display_name,
                "create_time": create_time_str,
                "industry_vertical": industry_vertical,
                "content_config": content_config,
                "solution_types": solution_types,
                "default_schema_id": data_store.default_schema_id,
                "billing_estimation": billing_estimation,
                "document_processing_config": document_processing_config,
            }

            return data

        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"DataStore với ID '{data_store_id}' không tồn tại.")
        except Exception:
            print(traceback.format_exc())
            raise GatewayException(status_code=http_status.HTTP_502_BAD_GATEWAY,
                                   detail=f"Lỗi khi lấy thông tin DataStore: {data_store_id}")

    def create_data_store(self, project_id, location, data_store_name, data_store_id):
        client = self.discovery_engine_client
        data_store = discoveryengine_v1.DataStore(
            display_name=data_store_name,
            industry_vertical=discoveryengine_v1.IndustryVertical.GENERIC,
            content_config=discoveryengine_v1.DataStore.ContentConfig.CONTENT_REQUIRED,
        )

        try:
            operation = client.create_data_store(
                request=discoveryengine_v1.CreateDataStoreRequest(
                    parent=client.collection_path(project_id, location, "default_collection"),
                    data_store=data_store,
                    data_store_id=data_store_id,
                )
            )
            datastore = operation.result(timeout=90)
            return datastore
        except AlreadyExists:
            raise GatewayException(status_code=http_status.HTTP_422_UNPROCESSABLE_ENTITY,
                                   detail=f"DataStore with ID {data_store_id} already exists.")
        except Exception:
            raise GatewayException(status_code=http_status.HTTP_502_BAD_GATEWAY,
                                   detail=f"Error during long-running operation")

    def update_data_store(self, project_id, location, data_store_id, new_name):
        client = self.discovery_engine_client
        data_store = client.get_data_store(
            name=f"projects/{project_id}/locations/{location}/dataStores/{data_store_id}")

        data_store.display_name = new_name

        try:
            updated_data_store = client.update_data_store(data_store=data_store)
            return updated_data_store
        except Exception:
            raise GatewayException(status_code=http_status.HTTP_502_BAD_GATEWAY,
                                   detail=f"Error updating data store")

    def delete_data_store(self, project_id, location, data_store_id):
        client = self.discovery_engine_client
        try:
            client.delete_data_store(name=f"projects/{project_id}/locations/{location}/dataStores/{data_store_id}")
            return f"DataStore {data_store_id} deleted."
        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"DataStore {data_store_id} does not exist.")
        except Exception:
            raise GatewayException(status_code=http_status.HTTP_502_BAD_GATEWAY, detail=f"Error delete data store")

    def check_data_store_exists(self, project_id, location, data_store_id):
        client = self.discovery_engine_client
        parent = f"projects/{project_id}/locations/{location}/dataStores/{data_store_id}"
        try:
            client.get_data_store(name=parent)
            return True
        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"DataStore {data_store_id} does not exist.")

    def list_project_id(self):
        try:
            list_project = self.list_project(self, )
            return list_project
        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"Project id not found.")

    def create_data_store_agent_builder(self, path, client_docs, project_id, location, data_store_id, company_id):
        try:
            create = self.create_data_store_AB(path, client_docs, project_id, location,
                                               data_store_id, company_id)
            return create

        except NotFound:
            raise GatewayException(status_code=http_status.HTTP_404_NOT_FOUND,
                                   detail=f"DataStore not found.")
