from typing import Optional
from sqlalchemy.orm import Session
from app.models.ai_services import Chat<PERSON>oom


class ChatRoomService:
    def __init__(self, db: Session):
        self.db = db

    async def create_chatroom(self, title: str, chatroom_code: str, user_id: int, use_case_id: int,
                              enable_reasoning: bool = None,
                              provider_id: Optional[int] = None) -> ChatRoom:
        # Check if chatroom already exists
        existing_chatroom = self.db.query(ChatRoom).filter(
            ChatRoom.chatroom_code == chatroom_code,
            ChatRoom.is_deleted == False
        ).first()

        if existing_chatroom:
            # Update reasoning_enabled if user has changed preference
            if enable_reasoning is not None and existing_chatroom.reasoning_enabled != enable_reasoning:
                existing_chatroom.reasoning_enabled = enable_reasoning
                self.db.commit()
                self.db.refresh(existing_chatroom)
            return existing_chatroom

        chatroom = ChatRoom(
            title=title,
            chatroom_code=chatroom_code,
            user_id=user_id,
            use_case_id=use_case_id,
            provider_id=provider_id,
            is_deleted=False,
            deleted_at=None,
            reasoning_enabled=enable_reasoning  # Set initial reasoning preference
        )
        self.db.add(chatroom)
        self.db.commit()
        self.db.refresh(chatroom)
        return chatroom
