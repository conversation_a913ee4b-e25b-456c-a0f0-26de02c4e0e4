from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class ResourceUsage(BaseModel):
    __tablename__ = "resource_usage"

    resource_id = Column(Integer, nullable=False)
    company_id = Column(Integer, nullable=False)
    resource_type = Column(String, nullable=False)
    usage_amount = Column(Integer, nullable=False)
    usage_date = Column(DateTime, nullable=False)
