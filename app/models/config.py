from sqlalchemy import Column, String, Boolean, Integer
from app.models.base import BaseModel


class Configuration(BaseModel):
    __tablename__ = "configuration"
    config_key = Column(String, nullable=False)
    config_value = Column(String, nullable=False)
    important = Column(Boolean, default=False)


class ConfigurationUserChatTitle(BaseModel):
    __tablename__ = "configuration_user_chat_title"
    userchat_title_jp = Column(String, nullable=False)
    userchat_title_en = Column(String, nullable=False)
    company_id = Column(Integer)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)


class ConfigurationModelKey(BaseModel):
    __tablename__ = "configuration_model_key"
    service_name = Column(String, nullable=False)
    service_key = Column(String, nullable=False)
    company_id = Column(Integer)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)
