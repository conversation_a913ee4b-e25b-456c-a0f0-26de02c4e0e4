from sqlalchemy import Colum<PERSON>, String, Inte<PERSON>, <PERSON><PERSON><PERSON>, Text, DateTime
from sqlalchemy.sql import func
from app.models.base import BaseModel


class GCPProject(BaseModel):
    __tablename__ = "gcp_projects"
    project_id = Column(String(100), nullable=False, unique=True)
    project_number = Column(String(30))
    project_name = Column(String(200))
    bucket = Column(String(200))
    description = Column(Text)
    datastore_quota = Column(Integer, default=500)
    agent_builder_quota = Column(Integer, default=100)
