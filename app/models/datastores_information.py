from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class DatastoresInformation(BaseModel):
    __tablename__ = "datastores_information"

    datastore_name = Column(String, nullable=False, default=None)
    datastore_id = Column(String, unique=True, default=None)
    company_slug = Column(String, unique=False, default=None)
    company_id = Column(Integer, nullable=False, default=None)
    project_id = Column(String, nullable=False, default=None)
    path = Column(String, nullable=False, default=None)
    location = Column(String, nullable=False, default=None)
    description = Column(Text, nullable=True, default=None)
