from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel
from sqlalchemy.dialects.postgresql import JSONB


class ServicePackage(BaseModel):
    __tablename__ = "service_packages"

    package_name = Column(String, nullable=False, default=None)
    description = Column(Text, nullable=True, default=None)
    max_datastores = Column(Integer, nullable=False, default=None)
    max_agent_builders = Column(Integer, nullable=False, default=None)
    max_queries_per_day = Column(Integer, nullable=False, default=None)
    max_documents_per_query = Column(Integer, nullable=False, default=None)
    max_users = Column(Integer, nullable=False, default=None)
    max_admins = Column(Integer, nullable=False, default=None)
    max_managers = Column(Integer, nullable=False, default=None)
    storage_limit = Column(Integer, nullable=False, default=None)
    credits_package_limit = Column(Integer, nullable=False, default=None)
    llm_model_type = Column(JSONB, nullable=False, default=None)
    max_tokens_per_month = Column(Integer, nullable=False, default=None)
    conversation_retention_days = Column(Integer, nullable=False, default=None)
    max_concurrent_conversations = Column(Integer, nullable=False, default=None)
    encryption_level = Column(String, nullable=False, default=None)
    old_max_users = Column(Integer, nullable=True, default=None)
    note = Column(Text, nullable=True, default=None)
