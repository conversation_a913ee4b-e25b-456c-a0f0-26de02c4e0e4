from sqlalchemy import Column, String
from app.models.base import BaseModel


class DatastoresInformationIndexed(BaseModel):
    __tablename__ = "datastores_information_indexed"
    datastore_id = Column(String, unique=True, default=None)
    project_id = Column(String, nullable=False, default=None)
    path_indexed = Column(String, nullable=False, default=None)
    import_mode = Column(String, nullable=False, default=None)
