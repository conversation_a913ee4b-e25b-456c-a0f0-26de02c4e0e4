from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class Companies(BaseModel):
    __tablename__ = "companies"

    company_name = Column(String, nullable=False, default=None)
    company_code = Column(String, unique=True, default=None)
    company_slug = Column(String, unique=True, default=None)
    company_type = Column(String, nullable=False, default=None)
    parent_company_id = Column(Integer, nullable=True, default=None)
    tax_code = Column(Integer, nullable=True, default=None)
    registration_number = Column(Integer, nullable=True, default=None)
    address = Column(Text, nullable=True, default=None)
    ward = Column(String, nullable=True, default=None)
    province = Column(String, nullable=True, default=None)
    city = Column(String, nullable=True, default=None)
    phone = Column(String, nullable=True, default=None)
    website_url = Column(String, nullable=True, default=None)
    industry = Column(String, nullable=True, default=None)
    number_of_employees = Column(Integer, nullable=True, default=None)
    email = Column(String, nullable=True, default=None)
