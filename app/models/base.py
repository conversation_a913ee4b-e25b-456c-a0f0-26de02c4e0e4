from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, DateTime, Boolean
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from datetime import datetime


@as_declarative()
class BaseCustom:
    __abstract__ = True
    __name__: str

    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


class BaseModel(BaseCustom):
    __abstract__ = True

    id = Column(Integer, primary_key=True, autoincrement=True)
    is_active = Column(Boolean, index=True, default=True)
    created_at = Column(DateTime(timezone=True), index=True, default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    is_deleted = Column(Boolean, nullable=True, default=False)
    deleted_at = Column(DateTime, nullable=True, default=None)

    class Config:
        from_attributes = True

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
