from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, BigInteger
from app.models.base import BaseModel
from sqlalchemy.dialects.postgresql import JSONB


class ActionLog(BaseModel):
    __tablename__ = "action_logs"

    user_id = Column(String, index=True, nullable=False)
    company_id = Column(Integer, nullable=True, default=None)
    action_name = Column(String, nullable=True, default=None)
    endpoint = Column(String, nullable=True, default=None)
    method = Column(String, nullable=True, default=None)
    status_code = Column(Integer, nullable=True, default=None)
    is_success = Column(Boolean, nullable=True, default=False)
    request_data = Column(String, nullable=True, default=None)
    response_data = Column(JSONB, nullable=True, default=None)
    error_message = Column(String, nullable=True, default=None)
    x_request_id = Column(String, nullable=True, default=None)
    ip_address = Column(String, nullable=True, default=None)
    user_agent = Column(String, nullable=True, default=None)
    process_time = Column(BigInteger, nullable=True, default=None)
    language = Column(String, nullable=True, default=None)

    def __repr__(self):
        return f"<ActionLog(user_id={self.user_id}, action_name={self.action_name})>"
