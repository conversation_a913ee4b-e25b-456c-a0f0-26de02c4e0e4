from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text, Date
from app.models.base import BaseModel


class Notifications(BaseModel):
    __tablename__ = "notifications"

    title = Column(String, nullable=False, default=None)
    content = Column(Text, nullable=False, default=None)
    level = Column(Integer, nullable=False, default=None)
    company_id = Column(Integer, nullable=False, default=None)
    start_date = Column(Date, nullable=False, default=None)
    end_date = Column(Date, nullable=False, default=None)
    type = Column(Integer, nullable=False, default=None)

