from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class Datastores(BaseModel):
    __tablename__ = "datastores"

    company_id = Column(Integer, nullable=False, default=None)
    datastore_name = Column(String, nullable=False, default=None)
    description = Column(Text, nullable=True, default=None)
    created_by = Column(Integer, nullable=False, default=None)
    status = Column(String, nullable=False, default=None)
    storage_used = Column(Integer, nullable=False, default=None)
    document_count = Column(Integer, nullable=False, default=None)
