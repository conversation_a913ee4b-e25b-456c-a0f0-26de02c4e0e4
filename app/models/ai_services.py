from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, Text, TIMESTAMP, Enum, DateTime
from sqlalchemy.sql import func
from app.models.base import BaseModel


class Document(BaseModel):
    __tablename__ = "documents"

    user_id = Column(Integer, nullable=False)
    description = Column(Text, nullable=False)
    cloud_url = Column(Text, nullable=True)


class Providers(BaseModel):
    __tablename__ = "providers"

    ai_service = Column(String, nullable=False)
    service_name = Column(String, nullable=True, default=None)
    model_name = Column(Text, nullable=True, default=None)
    llm_model_id = Column(Text, nullable=True, default=None)
    description = Column(Text, nullable=True, default=None)
    is_public = Column(Boolean, nullable=False, default=False)


class DataSources(BaseModel):
    __tablename__ = "datasource"

    title = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True, default=None)
    provider_id = Column(Integer, nullable=True, default=None)
    retriever_type = Column(String, nullable=True, default=None)
    ai_search_data_store_id = Column(String, nullable=True, default=None)
    engine_compute_id = Column(String, nullable=True, default=None)
    engine_data_type = Column(String, nullable=True, default=None)
    max_document = Column(Integer, nullable=True, default=1)
    max_extractive_answer_count = Column(Integer, default=1)
    ai_service = Column(String, nullable=True, default=None)
    llms_model = Column(String, nullable=True, default=None)
    chain_of_thought_prompt = Column(Boolean, nullable=True, default=False)
    chain_prompt = Column(Text, nullable=True, default=None)


class UseCases(BaseModel):
    __tablename__ = "use_cases"
    use_case_title = Column(String, unique=True, nullable=False, index=True)
    use_case_code = Column(String, unique=True, nullable=False, index=True)
    use_case_private = Column(Boolean, nullable=True, default=True)
    description = Column(Text, nullable=True, default=None)
    provider_id = Column(Integer, nullable=True, default=None)
    project_id = Column(String, nullable=True, index=True)
    location = Column(String, nullable=True, index=True, default="global")
    engine_id = Column(String, nullable=True, index=True)
    data_store_id = Column(String, nullable=True, index=True)
    max_documents = Column(Integer, nullable=True, default=1)
    max_extractive_answer_count = Column(Integer, nullable=True, default=5)
    get_extractive_answers = Column(Boolean, nullable=True, default=True)
    engine_data_type = Column(Integer, nullable=True, default=2)
    ai_service = Column(String, nullable=True, default="Google")
    llms_model_name = Column(String, nullable=True)
    llms_model_version = Column(String, nullable=True)
    system_prompt = Column(Text, nullable=True)
    human_prompt = Column(Text, nullable=True, default=None)
    chain_prompt = Column(Text, nullable=True, default=None)
    format_output = Column(Text, nullable=True, default=None)
    answer_language_code = Column(String, nullable=True, default="ja")
    chain_of_thought_prompt = Column(Boolean, nullable=True, default=True)
    search_type = Column(String, nullable=True, default=None)
    can_multimodal = Column(Boolean, nullable=True, default=True)
    is_multimodal = Column(Boolean, nullable=True, default=False)
    multimodal_extensions = Column(JSONB, nullable=True, default=None)
    retriever_type = Column(String, nullable=True, default='multi')
    result_count = Column(Integer, nullable=True, default=None)
    related_questions = Column(Boolean, nullable=True, default=False)
    snippets_or_extractive = Column(Boolean, nullable=True, default=False)
    autocomplete_suggestions = Column(Boolean, nullable=True, default=False)
    feedback = Column(Boolean, nullable=True, default=False)
    use_ragsource = Column(Boolean, nullable=True, default=True)
    company_id = Column(Integer, nullable=False, default=None)
    priority = Column(Integer, nullable=False, default=1)
    list_provider_ids = Column(JSONB, nullable=True, default=None)
    is_public = Column(Boolean, nullable=False, default=False)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)


class ChatRoom(BaseModel):
    __tablename__ = "chatrooms"

    title = Column(String, nullable=False)
    chatroom_code = Column(String, nullable=True, default=None)
    user_id = Column(Integer, nullable=False)
    use_case_id = Column(Integer, nullable=True, default=None)
    provider_id = Column(Integer, nullable=True, default=None)
    reasoning_enabled = Column(Boolean, nullable=True, default=None)


class ChatHistory(BaseModel):
    __tablename__ = 'chat_history'

    user_id = Column(Integer, nullable=False)
    chatroom_id = Column(Integer, nullable=False)
    message_id = Column(String, nullable=False)
    chat_id = Column(String, nullable=False)
    session_id = Column(String, nullable=False)
    service_name = Column(String, nullable=True, default=None)  # "chatgpt", "claude", "google"
    llm_model_id = Column(Integer, nullable=True, default=None)
    use_case_id = Column(Integer, nullable=True, default=None)
    user_message = Column(Text, nullable=True, default=None)
    ai_response = Column(Text, nullable=True, default=None)
    language = Column(String, nullable=True, default=None)  # "ja", "en"
    sentiment = Column(String, nullable=True, default=None)  # "positive", "neutral", "negative"
    input_length = Column(Integer, nullable=True, default=None)
    output_length = Column(Integer, nullable=True, default=None)
    input_tokens = Column(Integer, nullable=True, default=None)
    output_tokens = Column(Integer, nullable=True, default=None)
    response_time_ms = Column(Integer, nullable=True, default=None)
    is_resolved = Column(Boolean, default=False)
    source_info = Column(String, nullable=True, default=None)
    has_images = Column(Boolean, default=False)
    has_files = Column(Boolean, default=False)
    image_list = Column(JSONB, nullable=True, default=None)
    file_list = Column(JSONB, nullable=True, default=None)
    reactions = Column(String, nullable=True, default=None)
    references = Column(JSONB, nullable=True, default=None)


class GroupUseCase(BaseModel):
    __tablename__ = "group_use_cases"

    group_id = Column(Integer, nullable=False)
    use_case_id = Column(Integer, nullable=False)


class ChatHistoryReaction(BaseModel):
    __tablename__ = 'chat_history_reaction'

    user_id = Column(Integer, nullable=False)
    chatroom_id = Column(Integer, nullable=False)
    use_case_id = Column(Integer, nullable=True, default=None)
    message_id = Column(Integer, nullable=False)
    user_shared_id = Column(Integer, nullable=False)
    reaction_type = Column(String, nullable=False)
