import hashlib
from base64 import b64encode
from bcrypt import checkpw as bcrypt_check, hashpw, gensalt
from passlib.context import CryptContext
from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from sqlalchemy.dialects.postgresql import J<PERSON>NB
from app.models.base import BaseModel
from app.helpers.constants import AuthMode, Access

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Users(BaseModel):
    __tablename__ = "users"

    full_name = Column(String, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String(255), default=None)
    provider = Column(String, index=True, default=None)
    google_id = Column(String, nullable=True, default=None)
    auth_mode = Column(String, index=True, nullable=True, default=AuthMode.OAUTH)
    bio = Column(JSONB, nullable=True, default=None)
    uid = Column(String, unique=True, index=True, default=None)
    validated = Column(Boolean, index=True, default=False)
    is_superuser = Column(Boolean, index=True, default=False)
    last_login = Column(DateTime, index=True, default=None)
    avatar_url = Column(String, nullable=True, default=None)
    additional_info = Column(JSONB, nullable=True)
    is_first_login = Column(Boolean, default=False)
    company_id = Column(Integer, nullable=True, default=None)
    manager_id = Column(Integer, nullable=True, default=None)
    user_name = Column(String, nullable=True, default=None)
    business_type = Column(String, nullable=True, default=None)
    is_musashino = Column(Boolean, default=False)

    def __repr__(self):
        return '{}'.format(self.id)

    @property
    def password(self):
        return None

    @password.setter
    def password(self, val):
        self.hashed_password = self._hash_password(val)

    def _hash_password(self, password: str) -> str:
        hashed_sha256 = hashlib.sha256(password.encode()).digest()
        b64pwd = b64encode(hashed_sha256)
        return str(hashpw(b64pwd, gensalt()))

    def verify_password(self, password: str) -> bool:
        hashed_sha256 = hashlib.sha256(password.encode()).digest()
        b64pwd = b64encode(hashed_sha256)
        return bcrypt_check(b64pwd, self.hashed_password.encode())


class Role(BaseModel):
    __tablename__ = "roles"

    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True, default=None)
    important = Column(Boolean, nullable=True, default=False)
    company_id = Column(Integer, nullable=False)


class UserRole(BaseModel):
    __tablename__ = "user_roles"

    user_id = Column(Integer, nullable=False)
    role_id = Column(Integer, nullable=False)


class Group(BaseModel):
    __tablename__ = "groups"

    name = Column(String, unique=True, nullable=False)
    company_id = Column(Integer, nullable=False)
    description = Column(Text, nullable=True, default=None)
    group_config = Column(JSONB, nullable=True, default=None)


class UserGroup(BaseModel):
    __tablename__ = "user_groups"

    user_id = Column(Integer, nullable=False)
    group_id = Column(Integer, nullable=True, default=None)


class Permission(BaseModel):
    __tablename__ = "permissions"

    name = Column(String, nullable=False)
    description = Column(Text, nullable=True, default=None)
    endpoint = Column(String, nullable=True, default=None)
    grant_type = Column(String, nullable=True, default=Access.ALLOW)
    component = Column(String, nullable=True, default=None)
    position = Column(Integer, nullable=True, default=None)
    valid_from = Column(DateTime, nullable=True, default=None)
    valid_to = Column(DateTime, nullable=True, default=None)


class GroupPermission(BaseModel):
    __tablename__ = "group_permissions"

    group_id = Column(Integer, nullable=False)
    permission_id = Column(Integer, nullable=False)
    grant_type = Column(String, nullable=True, default=Access.ALLOW)
    valid_from = Column(DateTime, nullable=True, default=None)
    valid_to = Column(DateTime, nullable=True, default=None)


class ResourcePermission(BaseModel):
    __tablename__ = "resource_permissions"

    resource_id = Column(Integer, nullable=False)
    permission_id = Column(Integer, nullable=False)
    access_conditions = Column(JSONB, nullable=True, default=None)
    grant_type = Column(String, default='allow')
    valid_from = Column(DateTime, nullable=True, default=None)
    valid_to = Column(DateTime, nullable=True, default=None)
