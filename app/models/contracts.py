from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class Contracts(BaseModel):
    __tablename__ = "contracts"

    company_id = Column(Integer, nullable=False, default=None)
    package_id = Column(Integer, nullable=False, default=None)
    start_date = Column(DateTime, nullable=False, default=None)
    end_date = Column(DateTime, nullable=False, default=None)
    total_price = Column(Integer, nullable=False, default=None)
    status = Column(String, nullable=False, default=None)
