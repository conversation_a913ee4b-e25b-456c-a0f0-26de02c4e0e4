from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Foreign<PERSON>ey, Integer, BigInteger, String, Text, Enum, DateTime
from sqlalchemy.dialects.postgresql import JSONB

from app.models.base import BaseModel


class Token(BaseModel):
    __tablename__ = 'tokens'

    user_id = Column(BigInteger, nullable=True, default=None)
    access_token = Column(String, index=True, nullable=True, default=None)
    refresh_token = Column(String, nullable=True, default=None)
    domain = Column(String, nullable=True, default=None)
    product_type = Column(String, index=True, nullable=False)
    x_request_id = Column(String, nullable=True, default=None)
    ip_address = Column(String, nullable=True, default=None)
    user_agent = Column(Text, nullable=True, default=None)
    user_uid = Column(String,  nullable=True, default=None)
    jti_t = Column(BigInteger, nullable=True, default=None)
    iat_t = Column(BigInteger, nullable=True, default=None)
    jti_r = Column(BigInteger, nullable=True, default=None)
    iat_r = Column(BigInteger, nullable=True, default=None)

    def __repr__(self):
        return '{}'.format(self.id)


class PasswordResetToken(BaseModel):
    __tablename__ = "password_reset_tokens"

    user_id = Column(BigInteger, nullable=False, index=True)
    token = Column(String, unique=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    used = Column(Boolean, default=False, nullable=False)
    # status = Column(String, nullable=True, default='PENDING')  # PENDING, DONE, FAILED

    def is_valid(self) -> bool:
        return (
                not self.used
                and self.expires_at > datetime.now()
        )

    def __repr__(self):
        return '{}'.format(self.id)