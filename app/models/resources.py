from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, Date<PERSON><PERSON>, Integer, Text
from app.models.base import BaseModel
from sqlalchemy.dialects.postgresql import JSONB


class Resources(BaseModel):
    __tablename__ = "resources"

    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    resource_metadata = Column(JSONB, nullable=True, default=None)
