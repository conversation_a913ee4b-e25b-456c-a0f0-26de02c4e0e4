from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel


class CompanySubscriptions(BaseModel):
    __tablename__ = "company_subscriptions"

    company_id = Column(Integer, nullable=False, default=None)
    package_id = Column(Integer, nullable=False, default=None)
    start_date = Column(DateTime, nullable=True, default=None)
    end_date = Column(DateTime, nullable=True, default=None)
    status = Column(String, nullable=True, default=None)
