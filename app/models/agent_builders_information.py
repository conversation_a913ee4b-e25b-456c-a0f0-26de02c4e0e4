from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from app.models.base import BaseModel
from sqlalchemy.dialects.postgresql import JSONB


class AgentBuildersInformation(BaseModel):
    __tablename__ = "agent_builders_information"
    agent_builder_name = Column(String, nullable=False, default=None)
    agent_builder_id = Column(String, unique=True, default=None)
    data_store_ids = Column(JSONB, unique=False, default=None)
    company_slug = Column(Integer, nullable=False, default=None)
    project_id = Column(String, nullable=False, default=None)
    location = Column(String, nullable=False, default=None)
    company_id = Column(Integer, nullable=False, default=None)
    description = Column(Text, nullable=True, default=None)
