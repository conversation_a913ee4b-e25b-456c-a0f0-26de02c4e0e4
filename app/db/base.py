from app.helpers.config import settings
from sqlalchemy import create_engine, DDL, event
from sqlalchemy.orm import sessionmaker


engine = create_engine(settings.DATABASE_URL, pool_size=50, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, expire_on_commit=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
