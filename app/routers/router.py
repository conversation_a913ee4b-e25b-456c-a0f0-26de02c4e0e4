from fastapi import APIRouter

from app.api import healthcheck
from app.api import users, groups, permissions, user_groups, group_permissions, roles, user_roles, group_use_cases, \
    companies, contracts, service_packages, company_subscriptions, departments, user_departments, datastores, \
    department_datastore_access, role_permissions, resource_usage, resources, notifications, gcp_projects
from app.api.auth import authorization, refresh_token
from app.api.services import datasource, providers, use_case, chatroom, chat_history, chat, audio, agent_builder
from app.api import configuration

router = APIRouter()

router.include_router(healthcheck.router, tags=["health"], prefix="/health")
router.include_router(users.router, tags=["Users"], prefix="")
router.include_router(authorization.router, tags=["Auth"], prefix="")
router.include_router(refresh_token.router, tags=["Token"], prefix="/auth/token")
router.include_router(roles.router, tags=["Role"], prefix="")
router.include_router(user_roles.router, tags=["User Roles"], prefix="")
router.include_router(groups.router, tags=["Groups"], prefix="")
router.include_router(user_groups.router, tags=["User Groups"], prefix="")
router.include_router(permissions.router, tags=["Permission"], prefix="")
router.include_router(group_permissions.router, tags=["Groups Permission"], prefix="")
router.include_router(group_use_cases.router, tags=["Groups UseCase"], prefix="")
router.include_router(providers.router, tags=["Services"], prefix="")
router.include_router(datasource.router, tags=["DataSource"], prefix="")
router.include_router(use_case.router, tags=["Services"], prefix="")
router.include_router(chatroom.router, tags=["Services"], prefix="")
router.include_router(chat_history.router, tags=["Services"], prefix="")
router.include_router(chat.router, tags=["Services"], prefix="")
router.include_router(audio.router, tags=["Services"], prefix="")
router.include_router(configuration.router, tags=["Configuration"], prefix="")
router.include_router(companies.router, tags=["Companies"], prefix="")
router.include_router(contracts.router, tags=["Contracts"], prefix="")
router.include_router(service_packages.router, tags=["Service Packages"], prefix="")
router.include_router(company_subscriptions.router, tags=["Company Subscriptions"], prefix="")
router.include_router(departments.router, tags=["Departments"], prefix="")
router.include_router(user_departments.router, tags=["User Departments"], prefix="")
router.include_router(datastores.router, tags=["Data Store"], prefix="")
router.include_router(department_datastore_access.router, tags=["Department Datastore Access"], prefix="")
router.include_router(role_permissions.router, tags=["Role Permissions"], prefix="")
router.include_router(resource_usage.router, tags=["Resource Usage"], prefix="")
router.include_router(resources.router, tags=["Resources"], prefix="")
# router.include_router(audit_log.router, tags=["Audit Log"], prefix="")
router.include_router(agent_builder.router, tags=["Agent Builder"], prefix="")
router.include_router(notifications.router, tags=["Notifications"], prefix="")
router.include_router(gcp_projects.router, tags=["GCP PROJECTS"], prefix="")
