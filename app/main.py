import sys
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from app.helpers.config import settings
from app.helpers.middlewares import add_middleware
from app.helpers.exceptions import add_exception_handlers
from app.helpers.logging import log
from app.routers.router import router
from app.models.base import BaseCustom
from app.db.base import engine
from app.utils.common import create_upload_directories


def initialize_database():
    log.info("Initializing database schema...")
    BaseCustom.metadata.create_all(bind=engine, checkfirst=True)


@asynccontextmanager
async def lifespan(app: FastAPI):
    version = f"{sys.version_info.major}.{sys.version_info.minor}"
    initialize_database()
    create_upload_directories()
    app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
    app.mount("/static", StaticFiles(directory="static"), name="static")
    log.info(f"INITIALIZING APP API WITH PYTHON {version} AND ENV {settings.ENVIRONMENT} ...")

    yield

    log.info("SHUTTING DOWN APP API ...")

def create_application() -> FastAPI:
    application = FastAPI(
        title=settings.PROJECT_NAME,
        docs_url= "/v2/documentation" if settings.ENVIRONMENT in ['local', 'test'] else None,
        redoc_url= "/v2/redoc" if settings.ENVIRONMENT in ['local', 'test'] else None,
        openapi_url=f"{settings.API_PREFIX}/api_mrag.json",
        description="MRAG SYSTEM",
        swagger_ui_parameters={"defaultModelsExpandDepth": -1},
        lifespan=lifespan
    )

    add_middleware(application)
    add_exception_handlers(application)
    application.include_router(router, prefix=settings.API_PREFIX)

    return application


app = create_application()


if __name__ == '__main__':

    uvicorn.run(app, host="0.0.0.0", port=7224, workers=4)
