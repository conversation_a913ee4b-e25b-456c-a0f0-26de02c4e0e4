import os
import secrets
import string
from tempfile import NamedTemporaryFile
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader
from fastapi import status as http_status
from app.helpers.exceptions import GatewayException
from app.helpers.message import MESSAGES


def get_message(code: str, language: str = "jp") -> str:
    message = MESSAGES.get(code)
    if not message:
        return "Message code not found."
    return message.text.get(language, "Message not available in this language.")


def create_upload_directories():
    os.makedirs("uploads/logo", exist_ok=True)

def generate_id_code(length: int = 20) -> str:
    characters: str = string.ascii_letters + string.digits
    return ''.join(secrets.choice(characters) for _ in range(int(length)))


async def load_document(file, language: str = "jp"):
    documents = None
    if file:
        limit_size = 5 * 1024 * 1024
        file_name = file.filename
        file_type = file.content_type
        file_size = len(await file.read())
        await file.seek(0)

        if file_size > limit_size:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("file_size_exceeds", language)
            )

        if file_type == "application/pdf":
            with NamedTemporaryFile(delete=True, suffix=".pdf") as temp_file:
                temp_file.write(await file.read())
                temp_file_path = temp_file.name

            loader = PyPDFLoader(temp_file_path)
            documents = loader.load()

        elif file_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            with NamedTemporaryFile(delete=True, suffix=".docx") as temp_file:
                temp_file.write(await file.read())
                temp_file_path = temp_file.name

            loader = Docx2txtLoader(temp_file_path)
            documents = loader.load()

        else:
            raise GatewayException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=get_message("unsupported_file_type", language)
            )

    return documents