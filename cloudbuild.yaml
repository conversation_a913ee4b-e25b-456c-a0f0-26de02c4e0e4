steps:
  # Step 1: Copy .env from GCS
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Copy .env from GCS'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Copying .env from GCS..."
        gsutil cp gs://musashino-rag_cloudbuild/infra/be/env.live .env

  # Step 2: Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'Build Docker image'
    args:
      - 'build'
      - '-t'
      - 'asia-northeast1-docker.pkg.dev/musashino-rag/image-repository/musashino-rag-backend:${SHORT_SHA}'
      - '.'

  # Step 3: Push Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'Push Docker image'
    args:
      - 'push'
      - 'asia-northeast1-docker.pkg.dev/musashino-rag/image-repository/musashino-rag-backend:${SHORT_SHA}'

  # Step 4: Deploy application to Google App Engine
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'Deploy to GAE'
    entrypoint: 'gcloud'
    args:
      - 'app'
      - 'deploy'
      - '--image-url=asia-northeast1-docker.pkg.dev/musashino-rag/image-repository/musashino-rag-backend:${SHORT_SHA}'

images:
  - 'asia-northeast1-docker.pkg.dev/musashino-rag/image-repository/musashino-rag-backend:${SHORT_SHA}'

options:
  logging: CLOUD_LOGGING_ONLY

service_account: "<EMAIL>"
