[loggers]
keys = root, app

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console

[logger_app]
level = DEBUG
handlers = console
qualname = app
propagate = 0

[handler_console]
class = StreamHandler
level = NOTSET
args = (sys.stderr,)
formatter = generic

[formatter_generic]
format = %(levelname)-10.10s %(asctime)s [%(name)s][%(module)s:%(lineno)d] %(message)s
