# Icon Configuration API Documentation

## 概要 (Overview)

MRAG システムでは、ユーザーがファビコン、ロゴ、アイコンを自由に変更できる機能を提供しています。
この機能により、企業やユーザーは自分のブランドに合わせてシステムの外観をカスタマイズできます。

The MRAG system provides functionality for users to freely change favicons, logos, and icons.
This feature allows companies and users to customize the system's appearance to match their brand.

## サポートされているアイコンタイプ (Supported Icon Types)

| アイコンタイプ | 説明 | 用途 |
|---------------|------|------|
| `CONFIG_FAVICON` | ファビコン | ブラウザタブに表示されるアイコン |
| `CONFIG_LOGO_CLIENT` | クライアントロゴ | クライアント画面のロゴ |
| `CONFIG_LOGO_ADMIN` | 管理者ロゴ | 管理者画面のロゴ |
| `CONFIG_LOGO_BOT` | ボットアイコン | チャットボットの画像 |
| `CONFIG_ICON_USER` | ユーザーアイコン | ユーザーインターフェースのアイコン |
| `CONFIG_ICON_CHAT` | チャットアイコン | チャットインターフェースのアイコン |

## API エンドポイント (API Endpoints)

### 1. アイコン/ロゴのアップロード (Upload Icon/Logo)

**エンドポイント:** `POST /v2/api/configuration/logo/update`

**パラメータ:**
- `logo_type` (string, required): アイコンタイプ
- `file` (file, required): アップロードするファイル

**サポートされているファイル形式:**
- ファビコン (`CONFIG_FAVICON`): `.ico`, `.png`, `.jpg`, `.jpeg`
- その他のアイコン: `.jpg`, `.jpeg`, `.png`, `.svg`, `.webp`

**リクエスト例:**
```bash
curl -X POST "http://localhost:7224/v2/api/configuration/logo/update" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "logo_type=CONFIG_FAVICON" \
  -F "file=@favicon.ico"
```

**レスポンス例:**
```json
{
  "statusCode": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "config_key": "CONFIG_FAVICON",
    "config_value": "https://storage.googleapis.com/bucket/uploads/favicon/favicon.ico",
    "important": false,
    "is_active": true,
    "created_at": "2025-01-03T10:00:00Z",
    "updated_at": "2025-01-03T10:00:00Z"
  }
}
```

### 2. アイコン/ロゴの取得 (Get Icon/Logo)

**エンドポイント:** `GET /v2/api/configuration/logo`

**パラメータ:**
- `logo_type` (string, required): アイコンタイプ

**リクエスト例:**
```bash
curl -X GET "http://localhost:7224/v2/api/configuration/logo?logo_type=CONFIG_FAVICON" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. ファビコンの直接アクセス (Direct Favicon Access)

**エンドポイント:** `GET /favicon.ico`

このエンドポイントは、ブラウザが自動的にファビコンを取得するために使用されます。
設定されたファビコンがある場合はそれにリダイレクトし、ない場合はデフォルトファビコンを返します。

**リクエスト例:**
```bash
curl -X GET "http://localhost:7224/favicon.ico"
```

## 使用方法 (Usage)

### 1. ファビコンの設定

```bash
# ファビコンをアップロード
curl -X POST "http://localhost:7224/v2/api/configuration/logo/update" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "logo_type=CONFIG_FAVICON" \
  -F "file=@my-favicon.ico"
```

### 2. ボットアイコンの設定

```bash
# ボットアイコンをアップロード
curl -X POST "http://localhost:7224/v2/api/configuration/logo/update" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "logo_type=CONFIG_LOGO_BOT" \
  -F "file=@bot-avatar.png"
```

### 3. 設定されたアイコンの確認

```bash
# ファビコンの確認
curl -X GET "http://localhost:7224/v2/api/configuration/logo?logo_type=CONFIG_FAVICON" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## エラーハンドリング (Error Handling)

### よくあるエラー (Common Errors)

| エラーコード | 説明 | 解決方法 |
|-------------|------|----------|
| 400 | 無効なロゴタイプ | サポートされているロゴタイプを使用してください |
| 400 | 無効なファイル形式 | サポートされているファイル形式を使用してください |
| 400 | ファイルが必要 | ファイルをアップロードしてください |
| 404 | ロゴが見つからない | 先にロゴをアップロードしてください |

## セキュリティ (Security)

- アイコンのアップロードには管理者権限が必要です
- アップロードされたファイルは Google Cloud Storage に保存されます
- ファイル形式とサイズの検証が行われます

## 制限事項 (Limitations)

- ファイルサイズ制限: 5MB
- サポートされているファイル形式のみアップロード可能
- 管理者権限が必要
