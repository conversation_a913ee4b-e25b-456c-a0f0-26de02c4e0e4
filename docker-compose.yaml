version: '3.7'

networks:
  ragzoho:
    driver: bridge

services:
  ########################################################
  # WEB API Musashino Rag Dev
  ########################################################
  api-ragzoho:
    build:
      context: .
      dockerfile: Dockerfile
    image: api-ragzoho:latest
    container_name: api-ragzoho
    user: app_user
    volumes:
#      - /var/log/musashino-rag-backend:/app/logs/musashino-rag-backend
      - ./chroma_db:/app/chroma_db
    environment:
      - PROJECT_NAME
      - ENV
      - SECRET_KEY
      - GOOGLE_APPLICATION_CREDENTIALS
      - BACKEND_CORS_ORIGINS
      - ALLOWED_HOSTS
      - HOST_CLIENT_APP
      - DATABASE_URL
      - DEBUG
      - LOGGING_DIR
      - TZ
      - SENDER_EMAIL
      - SENDER_EMAIL_PASSWORD
      - SMTP_SERVER
      - SMTP_PORT
      - OPENAI_API_KEY
      - CLAUDE_API_KEY
      - AZURE_API_KEY
    env_file:
      - .env
    ports:
      - "7224:8080"
    restart: always
    networks:
      - "ragzoho"
