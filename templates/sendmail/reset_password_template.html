<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ title|default('パスワードのリセット') }}</title>
    <!--    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">-->

    <style>
      @import url("https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@400;700&display=swap");

      body {
        font-family: "Noto Serif JP", serif;
        margin: 0;
        padding: 20px;
        min-height: 100vh;
        /* background: #f3e7d3; */

        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h100v100H0z' fill='%23f9f2e8' fill-opacity='0.4'/%3E%3C/svg%3E");
        color: #2c1810;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .page-container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 80px);
      }

      .email-wrapper {
        width: 100%;
        max-width: 800px;
        /* background: #fff; */
        padding: 60px 40px;
        position: relative;
        box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
        border: 1px solid #d4b59e;
        background-image: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.8) 0%,
            transparent 10%
          ),
          linear-gradient(-90deg, rgba(255, 255, 255, 0.8) 0%, transparent 10%),
          linear-gradient(#fff7eb 25px, #f9f2e8 26px);
        background-size: 100% 100%, 100% 100%, 100% 50px;
        margin: 20px auto;
      }

      .email-wrapper::before {
        content: "";
        position: absolute;
        left: 40px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: rgba(165, 42, 42, 0.1);
      }

      .email-inner {
        max-width: 600px;
        margin: 0 auto;
      }

      .logo {
        text-align: center;
        margin-bottom: 40px;
      }

      .logo img {
        max-width: 150px;
        height: auto;
        filter: sepia(20%);
      }

      h1 {
        font-size: 28px;
        color: #2c1810;
        text-align: center;
        margin: 0 auto 30px;
        font-weight: 700;
        border-bottom: 2px solid #d4b59e;
        padding-bottom: 15px;
        letter-spacing: 0.1em;
        max-width: 80%;
      }

      .email-content {
        font-size: 16px;
        line-height: 2;
        margin-bottom: 25px;
        color: #2c1810;
        text-align: left;
        padding: 0 20px;
      }

      .button-wrapper {
        text-align: center;
        margin: 40px 0;
      }

      .reset-link {
        display: inline-block;
        background: linear-gradient(135deg, #8b4513, #d2691e);
        color: #ffffff;
        text-decoration: none;
        padding: 16px 40px;
        font-size: 20px;
        font-family: "Arial Black", sans-serif;
        border-radius: 10px;
        font-weight: bold;
        letter-spacing: 1px;
        text-transform: uppercase;
        transition: transform 0.3s ease, box-shadow 0.3s ease,
          background 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;
      }

      .reset-link::before {
        content: "";
        position: absolute;
        top: 0;
        left: -50%;
        width: 200%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        transform: skewX(-45deg);
        transition: transform 0.3s ease;
        z-index: 0;
      }

      .reset-link:hover::before {
        transform: translateX(150%) skewX(-45deg);
      }

      .reset-link:hover {
        background: linear-gradient(135deg, #723a0f, #b35a1e);
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
      }

      .reset-link span {
        position: relative;
        z-index: 1;
      }

      .divider {
        height: 2px;
        background: linear-gradient(90deg, transparent, #d4b59e, transparent);
        margin: 40px auto;
        width: 80%;
        opacity: 0.5;
      }

      .footer {
        text-align: center;
        color: #5c4030;
        font-size: 14px;
        line-height: 1.8;
        margin-top: 40px;
      }

      .footer p {
        margin: 5px 0;
      }

      .footer-links {
        margin: 25px 0;
        padding: 15px 0;
        border-top: 1px solid #d4b59e;
        border-bottom: 1px solid #d4b59e;
      }

      .footer-links a {
        color: #8b4513;
        text-decoration: none;
        margin: 0 15px;
        transition: all 0.3s ease;
        position: relative;
        display: inline-block;
        padding: 5px 0;
      }

      .footer-links a::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: #8b4513;
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
      }

      .footer-links a:hover::after {
        transform: scaleX(1);
        transform-origin: left;
      }

      .warning-text {
        font-size: 14px;
        color: #8b4513;
        text-align: center;
        margin: 20px 0;
        font-style: italic;
        padding: 10px;
        background: rgba(139, 69, 19, 0.05);
        border-left: 3px solid #8b4513;
      }

      .social-links {
        margin: 30px 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
      }

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        padding: 5px;
        box-sizing: content-box;
        background: #fff;
        border: 2px solid #d4b59e;
        border-radius: 50%;
        color: #8b4513;
        text-decoration: none;
        transition: all 0.3s ease;
      }

      .social-link:hover {
        background: #8b4513;
        border-color: #8b4513;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);
      }

      .social-link img {
        width: 24px; /* Icon size */
        height: 24px;
        transition: all 0.3s ease;
      }

      .social-link:hover img {
        transform: scale(1.1);
      }

      @media (max-width: 768px) {
        .page-container {
          padding: 20px;
        }

        .email-wrapper {
          padding: 30px 20px;
        }

        .email-inner {
          padding: 0 10px;
        }

        h1 {
          font-size: 24px;
        }

        .email-content {
          font-size: 15px;
          padding: 0 10px;
        }

        .reset-link {
          padding: 14px 30px;
          font-size: 15px;
        }

        .footer-links a {
          margin: 0 8px;
          font-size: 13px;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="email-wrapper">
        <div class="email-inner">
          {% if company_logo %}
          <div class="logo">
            <img
              src="{{ company_logo }}"
              alt="{{ company_name|default('会社') }} ロゴ"
            />
          </div>
          {% endif %}

          <h1>{{ name }} 様</h1>

          <p class="email-content">
            {% if purpose == 'reset_password' %}
            お客様のアカウントのパスワードリセットのご依頼を承りました。このリクエストに心当たりがない場合は、このメールを無視していただきますようお願いいたします。
            {% else %} {{
            main_content|default('弊社サービスをご利用いただき、誠にありがとうございます。')
            }} {% endif %}
          </p>

          <div class="button-wrapper">
            <a href="{{ reset_link }}" class="reset-link">
              {{ action_text|default('パスワードをリセット') }}
            </a>
          </div>

          <p class="warning-text">
            {% if purpose == 'reset_password' %} ※このリンクは {{
            expires_at|default('15分') }} 後に失効いたします。 {% endif %}
          </p>

          <div class="divider"></div>

          <div class="footer">
            <p>{{ company_name|default('株式会社武蔵野') }}</p>
            <p>
              {{ company_address|default('〒184-0011 東京都小金井市東町4-33-8')
              }}
            </p>
            <p>
              {{ company_phone|default('TEL: 0120-85-6340 / FAX: 0120-28-6340')
              }}
            </p>

            {% if show_footer %}
            <div class="footer-links">
              <a href="{{ privacy_policy_url|default('#') }}"
                >プライバシーポリシー</a
              >
              <a href="{{ terms_url|default('#') }}">利用規約</a>
              <a href="{{ contact_url|default('#') }}">お問い合わせ</a>
            </div>
            {% endif %} {% if show_social_links %}

            <div class="social-links" style="">
              <a
                href="{{ facebook_url|default('#') }}"
                class="social-link"
                aria-label="Facebook"
                style="margin-right: 25px"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/b/b8/2021_Facebook_icon.svg"
                  alt="Facebook"
                  style="padding: 15px"
                />
              </a>

              <a
                href="{{ twitter_url|default('#') }}"
                class="social-link"
                aria-label="Twitter"
                style="margin-right: 25px"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/en/6/60/Twitter_Logo_as_of_2021.svg"
                  alt="Twitter"
                  style="padding: 15px"
                />
              </a>

              <a
                href="{{ linkedin_url|default('#') }}"
                class="social-link"
                aria-label="LinkedIn"
                style="margin-right: 25px"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/e/e9/Linkedin_icon.svg"
                  alt="LinkedIn"
                  style="padding: 15px"
                />
              </a>

              <a
                href="{{ instagram_url|default('#') }}"
                class="social-link"
                aria-label="Instagram"
                style="margin-right: 25px"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/a/a5/Instagram_icon.png"
                  alt="Instagram"
                  style="padding: 15px"
                />
              </a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
