import pytest
import io
from fastapi.testclient import TestClient
from fastapi import status
from unittest.mock import patch, MagicMock
from app.main import app
from app.models.config import Configuration


client = TestClient(app)


class TestIconConfiguration:
    """Test cases for icon and favicon configuration endpoints"""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        with patch('app.api.configuration.get_db') as mock_get_db:
            mock_session = MagicMock()
            mock_get_db.return_value = mock_session
            yield mock_session

    @pytest.fixture
    def mock_auth(self):
        """Mock authentication"""
        with patch('app.helpers.security.AuthPermissionChecker') as mock_auth:
            mock_auth.return_value = lambda: True
            yield mock_auth

    @pytest.fixture
    def sample_image_file(self):
        """Create a sample image file for testing"""
        return io.BytesIO(b"fake image content")

    def test_update_favicon_success(self, mock_db_session, mock_auth, sample_image_file):
        """Test successful favicon upload"""
        # Mock configuration object
        mock_config = MagicMock(spec=Configuration)
        mock_config.config_key = "CONFIG_FAVICON"
        mock_config.config_value = ""
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

        # Mock Google Cloud Storage
        with patch('app.api.configuration.storage.Client') as mock_storage:
            mock_bucket = MagicMock()
            mock_blob = MagicMock()
            mock_blob.public_url = "https://example.com/favicon.ico"
            mock_bucket.blob.return_value = mock_blob
            mock_storage.from_service_account_json.return_value.bucket.return_value = mock_bucket

            # Test favicon upload
            response = client.post(
                "/v2/api/configuration/logo/update",
                params={"logo_type": "CONFIG_FAVICON"},
                files={"file": ("favicon.ico", sample_image_file, "image/x-icon")}
            )

            assert response.status_code == status.HTTP_200_OK
            mock_db_session.commit.assert_called()

    def test_update_favicon_invalid_type(self, mock_db_session, mock_auth):
        """Test favicon upload with invalid logo type"""
        response = client.post(
            "/v2/api/configuration/logo/update",
            params={"logo_type": "INVALID_TYPE"},
            files={"file": ("favicon.ico", io.BytesIO(b"content"), "image/x-icon")}
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_update_favicon_invalid_format(self, mock_db_session, mock_auth):
        """Test favicon upload with invalid file format"""
        # Mock configuration object
        mock_config = MagicMock(spec=Configuration)
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

        response = client.post(
            "/v2/api/configuration/logo/update",
            params={"logo_type": "CONFIG_FAVICON"},
            files={"file": ("favicon.txt", io.BytesIO(b"content"), "text/plain")}
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_update_user_icon_success(self, mock_db_session, mock_auth, sample_image_file):
        """Test successful user icon upload"""
        # Mock configuration object
        mock_config = MagicMock(spec=Configuration)
        mock_config.config_key = "CONFIG_ICON_USER"
        mock_config.config_value = ""
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

        # Mock Google Cloud Storage
        with patch('app.api.configuration.storage.Client') as mock_storage:
            mock_bucket = MagicMock()
            mock_blob = MagicMock()
            mock_blob.public_url = "https://example.com/user-icon.png"
            mock_bucket.blob.return_value = mock_blob
            mock_storage.from_service_account_json.return_value.bucket.return_value = mock_bucket

            response = client.post(
                "/v2/api/configuration/logo/update",
                params={"logo_type": "CONFIG_ICON_USER"},
                files={"file": ("user-icon.png", sample_image_file, "image/png")}
            )

            assert response.status_code == status.HTTP_200_OK
            mock_db_session.commit.assert_called()

    def test_get_favicon_success(self, mock_db_session):
        """Test successful favicon retrieval"""
        # Mock configuration object
        mock_config = MagicMock(spec=Configuration)
        mock_config.config_key = "CONFIG_FAVICON"
        mock_config.config_value = "https://example.com/favicon.ico"
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

        response = client.get("/v2/api/configuration/logo?logo_type=CONFIG_FAVICON")

        assert response.status_code == status.HTTP_200_OK

    def test_get_favicon_not_found(self, mock_db_session):
        """Test favicon retrieval when not configured"""
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None

        response = client.get("/v2/api/configuration/logo?logo_type=CONFIG_FAVICON")

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_favicon_endpoint_with_config(self, mock_db_session):
        """Test /favicon.ico endpoint when favicon is configured"""
        # Mock configuration object
        mock_config = MagicMock(spec=Configuration)
        mock_config.config_value = "https://example.com/favicon.ico"
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

        response = client.get("/v2/api/favicon.ico", follow_redirects=False)

        assert response.status_code == status.HTTP_307_TEMPORARY_REDIRECT
        assert response.headers["location"] == "https://example.com/favicon.ico"

    def test_favicon_endpoint_without_config(self, mock_db_session):
        """Test /favicon.ico endpoint when favicon is not configured"""
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None

        response = client.get("/v2/api/favicon.ico")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert response_data["status"] == False
        assert "ロゴが見つかりません!" in response_data["error"]

    def test_all_supported_logo_types(self, mock_db_session, mock_auth):
        """Test that all supported logo types are accepted"""
        supported_types = [
            "CONFIG_LOGO_ADMIN",
            "CONFIG_LOGO_CLIENT", 
            "CONFIG_LOGO_BOT",
            "CONFIG_FAVICON",
            "CONFIG_ICON_USER",
            "CONFIG_ICON_CHAT"
        ]

        for logo_type in supported_types:
            # Mock configuration object
            mock_config = MagicMock(spec=Configuration)
            mock_config.config_key = logo_type
            mock_config.config_value = ""
            mock_db_session.query.return_value.filter_by.return_value.first.return_value = mock_config

            response = client.get(f"/v2/api/configuration/logo?logo_type={logo_type}")
            
            # Should not return 400 for invalid logo type
            assert response.status_code != status.HTTP_400_BAD_REQUEST
