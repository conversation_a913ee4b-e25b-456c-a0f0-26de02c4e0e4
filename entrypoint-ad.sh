#!/bin/bash

DB_NAME="musashinodev"
DB_USER="admin"
DB_PASSWORD="Musa2024a!"
DB_HOST="************"
DB_PORT="5432"

check_user() {
    table_exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
        SELECT EXISTS (
            SELECT FROM pg_tables
            WHERE schemaname = 'public' AND tablename = 'users'
        );
    ")

    if [[ $table_exists == "t" ]]; then
        echo "Bảng 'users' đã tồn tại. Kiểm tra người dùng 'admin'..."

        admin_exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
            SELECT EXISTS (
                SELECT 1 FROM users WHERE email = '<EMAIL>'
            );
        ")

        if [[ $admin_exists == "f" ]]; then
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
                INSERT INTO users (full_name, email, hashed_password, role)
                VALUES ('Admin', '<EMAIL>', crypt('Musa2024a!', gen_salt('bf')), 'Admin');
            "
            echo "Người dùng 'admin' đã được tạo thành công."
        else
            echo "Người dùng 'admin' đã tồn tại."
        fi
    else
        echo "Bảng 'users' không tồn tại. Vui lòng kiểm tra lại."
    fi
}

check_user
