# docker volumes
/volumes

# Byte-compiled / optimized / DLL files
__pycache__/

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
.idea/
lib/
lib64/
parts/
sdist/
wheels/
conf/redis/gateway
*.db
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
jobs/
logs/*
chroma_db/*
vectordb/
dockers/data/*
scripts/
# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# celery beat schedule file
celerybeat-schedule

# virtualenv
.venv/*
venv/*
envs/*

migrations/versions/*.py
# docker local data
docker/
conf/redis/cache/**

# dotenv
.env

