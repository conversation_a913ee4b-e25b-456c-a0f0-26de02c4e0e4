-- Migration: Add default prompt templates to companies table
-- Created: 2025-07-03
-- Author: Hieuda
-- Description: Add default prompt template fields to companies table to allow 
--              setting company-wide default prompts that can be inherited by use cases

-- Add default prompt template columns to companies table
ALTER TABLE companies 
ADD COLUMN default_system_prompt TEXT NULL,
ADD COLUMN default_format_output TEXT NULL;

-- Add comments to explain the purpose of each column
COMMENT ON COLUMN companies.default_system_prompt IS 'Default system prompt template for all use cases in this company';
COMMENT ON COLUMN companies.default_format_output IS 'Default format output template for all use cases in this company';

-- Optional: Add indexes if needed for performance (uncomment if required)
-- CREATE INDEX idx_companies_default_system_prompt ON companies USING gin (to_tsvector('english', default_system_prompt));
-- CREATE INDEX idx_companies_default_human_prompt ON companies USING gin (to_tsvector('english', default_human_prompt)); 