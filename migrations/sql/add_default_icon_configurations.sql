-- Migration: Add default icon configurations
-- Description: Thê<PERSON> các config mặc định cho favicon và các loại icon mới
-- Date: 2025-01-03

-- Thêm config cho favicon
INSERT INTO configuration (config_key, config_value, important, is_active, created_at, updated_at, is_deleted)
VALUES 
    ('CONFIG_FAVICON', '', false, true, NOW(), NOW(), false)
ON CONFLICT (config_key) DO NOTHING;

-- Thêm config cho user interface icon
INSERT INTO configuration (config_key, config_value, important, is_active, created_at, updated_at, is_deleted)
VALUES 
    ('CONFIG_ICON_USER', '', false, true, NOW(), NOW(), false)
ON CONFLICT (config_key) DO NOTHING;

-- Thêm config cho chat interface icon
INSERT INTO configuration (config_key, config_value, important, is_active, created_at, updated_at, is_deleted)
VALUES 
    ('CONFIG_ICON_CHAT', '', false, true, NOW(), NOW(), false)
ON CONFLICT (config_key) DO NOTHING;

-- Cập nhật description cho các config logo hiện tại (nếu cần)
UPDATE configuration 
SET config_value = CASE 
    WHEN config_key = 'CONFIG_LOGO_CLIENT' AND config_value = '' THEN ''
    WHEN config_key = 'CONFIG_LOGO_ADMIN' AND config_value = '' THEN ''
    WHEN config_key = 'CONFIG_LOGO_BOT' AND config_value = '' THEN ''
    ELSE config_value
END,
updated_at = NOW()
WHERE config_key IN ('CONFIG_LOGO_CLIENT', 'CONFIG_LOGO_ADMIN', 'CONFIG_LOGO_BOT')
AND is_deleted = false;

-- Đảm bảo các config logo cũ tồn tại (nếu chưa có)
INSERT INTO configuration (config_key, config_value, important, is_active, created_at, updated_at, is_deleted)
VALUES 
    ('CONFIG_LOGO_CLIENT', '', false, true, NOW(), NOW(), false),
    ('CONFIG_LOGO_ADMIN', '', false, true, NOW(), NOW(), false),
    ('CONFIG_LOGO_BOT', '', false, true, NOW(), NOW(), false)
ON CONFLICT (config_key) DO NOTHING;
